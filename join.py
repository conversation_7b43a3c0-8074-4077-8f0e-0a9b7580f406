import requests
import uuid
import hashlib
import time
import threading


def phone_to_md5(phone_number):
    """
    将手机号转换为32位MD5哈希值
    
    Args:
        phone_number (str): 手机号码
    
    Returns:
        str: 32位MD5哈希值
    """
    return hashlib.md5(phone_number.encode('utf-8')).hexdigest()


def speak_end_timer(session, room_id, user_identity, interval=5):
    """
    定时器函数，持续执行过麦请求
    
    Args:
        session: requests会话对象
        room_id (str): 房间ID
        user_identity (str): 用户身份标识
        interval (int): 执行间隔（秒），默认5秒
    """
    #qa3
    base = "http://lrqa.langren001.com"
    # qa1
    # base="http://qa-rancher.langren001.com"
    headers = {
        'ptype': '1',
        'pcode': '209080'
    }
    
    while True:
        try:
            url = base + f'/langren/werewolfGame/{room_id}/speak/end'
            data = {}  # 过麦请求通常不需要额外数据
            
            response = session.post(url=url, data=data, headers=headers)
            result = response.text
            
            print(f"[{time.strftime('%H:%M:%S')}] 用户 {user_identity} 过麦结果: {result}")
            
            time.sleep(interval)
            
        except Exception as e:
            print(f"[{time.strftime('%H:%M:%S')}] 过麦请求出错: {e}")
            time.sleep(interval)


def join_room_by_phone(phone_number, room_id='langren_room_27004', access_token='e10adc3949ba59abbe56e057f20f883e', start_speak_timer=False):
    """
    通过手机号登录并加入房间的函数
    
    Args:
        phone_number (str): 手机号码
        room_id (str): 房间ID，默认为 'langren_room_27002'
        access_token (str): 访问令牌，默认值
        start_speak_timer (bool): 是否启动过麦定时器，默认False
    
    Returns:
        tuple: (login_response, join_response, ready_response, session) 登录响应、加入房间响应、准备游戏响应和会话对象
    """
    # 将手机号转换为MD5作为用户身份标识
    user_identity = phone_to_md5(phone_number)
    print(f"手机号 {phone_number} 转换为用户标识: {user_identity}")
    
    login_resp, join_resp, ready_resp, session = join_room(user_identity, room_id, access_token)
    
    # 如果启动过麦定时器
    if start_speak_timer:
        # 启动定时器线程
        timer_thread = threading.Thread(
            target=speak_end_timer, 
            args=(session, room_id, user_identity),
            daemon=True  # 设置为守护线程，主程序结束时自动结束
        )
        timer_thread.start()
        print(f"已为用户 {phone_number} 启动过麦定时器")
    
    return login_resp, join_resp, ready_resp, session


def join_room(user_identity, room_id='langren_room_27004', access_token='e10adc3949ba59abbe56e057f20f883e'):
    """
    登录并加入房间的函数
    
    Args:
        user_identity (str): 用户身份标识（MD5哈希值）
        room_id (str): 房间ID，默认为 'langren_room_27002'
        access_token (str): 访问令牌，默认值
    
    Returns:
        tuple: (login_response, join_response) 登录响应和加入房间响应
    """
    base = "http://lrqa.langren001.com"
    deviceid = uuid.uuid4()
    
    # 创建一个会话对象，自动处理 cookies
    session = requests.Session()
    
    # 第一次请求：登录
    url = base + '/langren/securities/login'
    data = {
        'userIdentity': user_identity,
        'accessToken': access_token,
        'userType': 1,
        'phoneType': 1,
        'version': 209080,
        'deviceId': deviceid
    }
    headers = {
        'ptype': '1',
        'pcode': '209080'
    }
    
    response = session.post(url=url, data=data, headers=headers)
    login_result = response.text
    print(f"用户 {user_identity} 登录结果：")
    print(login_result)
    
    # 第二次请求：加入房间（会自动携带第一次请求的 cookies）
    url2 = base + f'/langren/room/{room_id}/join'
    data2 = {'versionFlag': 8}
    
    # 使用同一个 session 对象发送请求，自动包含 cookies
    response2 = session.post(url=url2, data=data2, headers=headers)
    join_result = response2.text
    
    print("*************************************")
    print(f"用户 {user_identity} 加入房间结果：")
    print(join_result)
    
    # 第三次请求：准备游戏
    url3 = base + f'/langren/room/{room_id}/ready/1'
    data3 = {}  # 准备请求通常不需要额外数据
    
    # 使用同一个 session 对象发送请求，自动包含 cookies
    response3 = session.post(url=url3, data=data3, headers=headers)
    ready_result = response3.text
    
    print("*************************************")
    print(f"用户 {user_identity} 准备游戏结果：")
    print(ready_result)
    
    return login_result, join_result, ready_result, session


# 示例使用
if __name__ == "__main__":
    # 使用不同的手机号
    phone_numbers = [
        '14725836911',
        '14725836913',
        # 可以添加更多手机号
    ]
    phone_numbers =[f'147258369{x}' for x in range(10,18)]
    
    for phone in phone_numbers:
        print(f"\n{'='*50}")
        print(f"处理手机号: {phone}")
        print(f"{'='*50}")
        
        try:
            login_resp, join_resp, ready_resp, session = join_room_by_phone(phone, 'langren_room_26001', start_speak_timer=True)
            
            # 检查登录是否成功
            if '"err_code"' in login_resp:
                print(f"❌ 手机号 {phone} 登录失败，跳过...")
                continue
                
            # 检查加入房间是否成功
            if '"err_code"' in join_resp:
                print(f"❌ 手机号 {phone} 加入房间失败，跳过...")
                continue
                
            # 检查准备游戏是否成功
            if '"err_code"' in ready_resp:
                print(f"❌ 手机号 {phone} 准备游戏失败，跳过...")
                continue
                
            print(f"✅ 手机号 {phone} 全部操作成功完成")
            
        except Exception as e:
            print(f"❌ 手机号 {phone} 处理出现异常: {e}")
            print("继续处理下一个手机号...")
            continue
    
    # 保持程序运行，让定时器持续工作
    try:
        print("\n所有用户已加入房间并启动过麦定时器，按 Ctrl+C 退出...")
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n程序已退出")