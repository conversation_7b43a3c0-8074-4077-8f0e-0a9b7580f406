"""
<AUTHOR>
@date 2020/6/18 0018
"""
import time
from abc import abstractmethod
from typing import List, Dict, Type, TypeVar, Optional, Tuple, Any

from role.game_role import GameRole
from util.log import log, get_logger
from util.rpc import RpcClient
from util.thread_tool import ThreadTool

T = TypeVar('T')


class SeatProfile(object):
    def __init__(self, seat_num: int, rpc_client: RpcClient, nickname: str, role_name: str):
        self.seat_num = seat_num
        self.rpc_client = rpc_client
        self.nickname = nickname
        self.role_name = role_name


class Context(object):

    @property
    @abstractmethod
    def thread_tool(self) -> ThreadTool:
        pass

    @abstractmethod
    def status(self) -> str:
        pass

    @abstractmethod
    def dispose(self):
        pass


class GameContext(Context):

    def __init__(self, thread_tool: ThreadTool):
        self.__thread_tool = thread_tool
        self.action_provider: 'ActionProvider' = None

        self.seat_profiles: Dict[int, SeatProfile] = {}
        self.seat_nums: List[int] = []
        self.werewolf_seat_nums: List[int] = []
        self.witch_seat_num: int = -1
        self.villager_seat_nums: List[int] = []
        self.current_turn: int = 0
        self.turn_current_speaker: Dict[int, int] = {}
        self.turn_spoken_speakers: Dict[int, List[int]] = {}
        self.turn_vote_kill_seat_nums: Dict[int:int] = {}
        self.turn_night_start_time: Dict[int:int] = {}
        self.death_seat_nums: List[int] = []
        self.witch_rescue_used = False
        self.witch_rescue_used_turn = -1
        self.witch_poison_used = False

        self.progress_msgs: List[str] = []
        self.system_msgs: List[str] = []
        self.poll = True

        self.logger = get_logger('GameContext')

    @property
    def thread_tool(self) -> ThreadTool:
        return self.__thread_tool

    def status(self) -> str:
        return 'turn: %s' % self.current_turn

    def dispose(self):
        self.poll = False

    def is_witch_died(self) -> bool:
        return self.witch_seat_num in self.death_seat_nums

    def is_all_werewolf_died(self) -> bool:
        return all(sn in self.death_seat_nums for sn in self.werewolf_seat_nums)

    def is_all_man_died(self) -> bool:
        man_seat_nums = [self.witch_seat_num]
        man_seat_nums.extend(self.villager_seat_nums)
        return all(sn in self.death_seat_nums for sn in self.werewolf_seat_nums)

    @log
    def get_alive_seat_nums(self) -> List[int]:
        return list(set(self.seat_nums).difference(set(self.death_seat_nums)))

    def get_alive_werewolf(self) -> List[int]:
        return list(set(self.werewolf_seat_nums).difference(set(self.death_seat_nums)))

    @log
    def infer_death_seat_nums(self) -> List[int]:
        """
        推断夜晚死亡的人
        :return:
        """
        return self.__try_infer_death_seat_nums(1)

    def __try_infer_death_seat_nums(self, try_times: int) -> List[int]:
        if try_times > 5:
            raise RuntimeError('failed')
        msg = self.progress_msgs[-1]
        if not msg.startswith('昨晚'):
            time.sleep(1)
            return self.__try_infer_death_seat_nums(try_times + 1)
        if '平安夜' in msg:
            return []

        seat_nums = []
        try:
            start = msg.index('昨晚') + 2
            end = msg.index('死亡')

            while True:
                try:
                    n_start = msg.index('[', start, end)
                    seat_nums.append(int(msg[n_start + 1]))
                    start = n_start + 2
                except ValueError:
                    break
            return seat_nums
        except ValueError:
            time.sleep(1)
            return self.__try_infer_death_seat_nums(try_times + 1)

    @log
    def infer_current_speaker(self, exclude: int) -> int:
        """
        推断当前说话的人
        :param exclude:排除上一个说话的人
        :return:
        """
        return self.__try_infer_current_speaker(exclude, 1)

    def __try_infer_current_speaker(self, exclude: int, try_times: int):
        if try_times > 5:
            raise RuntimeError('failed')
        txt = self.progress_msgs[-1]

        if ']号开始发言' in txt:
            end = txt.index(']号开始发言')
            sn = int(txt[end - 1])
            if sn != exclude:
                return sn

        time.sleep(1)
        return self.__try_infer_current_speaker(exclude, try_times + 1)

    @log
    def infer_vote_kill_seat_num(self) -> int:
        """
        推断投票处决的人
        :return:
        """
        msg = self.progress_msgs[-1]
        if '处决' in msg:
            return int(msg[1])
        else:
            return -1

    # 拉取系统消息
    def poll_system_msg(self):
        logger = get_logger('SystemMsg')
        while self.poll:
            try:
                msg = self.proxy(GameRole).get_latest_system_msg()
            except Exception:
                time.sleep(1)
                continue

            if len(self.system_msgs) == 0:
                self.system_msgs.append(msg)
                continue
            latest_msg = self.system_msgs[-1]

            if latest_msg != msg:
                self.system_msgs.append(msg)
                logger.info('[%s] -> %s' % (self.status(), msg))
            time.sleep(0.5)

    # 拉取游戏流程消息
    def poll_progress_msg(self):
        logger = get_logger('ProgressMsg')
        while self.poll:
            try:
                # 死亡视角下，没有弹窗不会遮住文字
                sn = self.villager_seat_nums[0] if len(self.death_seat_nums) == 0 else self.death_seat_nums[0]
                msg = self.proxy(GameRole, sn).get_progress_msg()
            except Exception:
                time.sleep(1)
                continue
            if len(self.progress_msgs) == 0:
                self.progress_msgs.append(msg)
                continue
            latest_msg = self.progress_msgs[-1]
            if latest_msg != msg:
                self.progress_msgs.append(msg)
                logger.info('[%s] -> %s' % (self.status(), msg))

            time.sleep(0.5)

    def start_poll_msg(self):
        self.thread_tool.run_async(self.poll_system_msg)
        self.thread_tool.run_async(self.poll_progress_msg)

    def proxy(self, t: Type[T], sn: int = -1) -> T:
        if sn == -1:
            sn = self.villager_seat_nums[0]
        return self.seat_profiles[sn].rpc_client.get_proxy(t)

    def ensure_no_deaths(self, seat_nums: List[int]):
        if len(set(seat_nums).intersection(set(self.death_seat_nums))) > 0:
            raise AssertionError('death seat cannot action')

    def __str__(self):
        return self.__class__.__name__


class ActionProvider(object):

    def __init__(self, ctx: GameContext):
        self.ctx = ctx

    @property
    @abstractmethod
    def kill_seat_nums(self) -> Dict[int, int]:
        pass

    @property
    @abstractmethod
    def rescue_confirm(self) -> Optional[int]:
        pass

    @property
    @abstractmethod
    def rescue_cancel(self) -> Optional[int]:
        pass

    @property
    @abstractmethod
    def poison_select(self) -> Optional[Tuple[int, int]]:
        pass

    @property
    @abstractmethod
    def poison_confirm(self) -> Optional[int]:
        pass

    @property
    @abstractmethod
    def poison_cancel(self) -> Optional[int]:
        pass

    @property
    @abstractmethod
    def speak_end(self) -> List[int]:
        pass

    @property
    @abstractmethod
    def vote_select(self) -> Dict[int, int]:
        pass

    @property
    @abstractmethod
    def vote_confirm(self) -> List[int]:
        pass

    @property
    @abstractmethod
    def vote_cancel(self) -> List[int]:
        pass

    @property
    @abstractmethod
    def last_words_end(self) -> Optional[int]:
        pass

    @property
    @abstractmethod
    def asserts(self) -> Dict[str, Any]:
        pass


class ExcelGameCase(object):
    def __init__(self):
        # night
        self.kill: Dict[int, int] = {}
        self.rescue_confirm: Optional[int] = None
        self.rescue_cancel: Optional[int] = None
        self.poison_select: Optional[Tuple[int, int]] = None
        self.poison_confirm: Optional[int] = None
        self.poison_cancel: Optional[int] = None
        # day
        self.speak_end: List[int] = []
        self.vote_select: Dict[int, int] = {}
        self.vote_confirm: List[int] = []
        self.vote_cancel: List[int] = []
        self.last_words_end: Optional[int] = None
        # assert
        self.asserts: Dict[str, Any] = {}
        self.real_seat_nums: List[int] = []

    def validate_case(self):
        # 不能同时救人/放弃
        if self.rescue_cancel is not None and self.rescue_confirm is not None:
            raise RuntimeError('error')
        # 不能同时下毒/放弃
        if self.poison_cancel is not None and self.poison_confirm is not None:
            raise RuntimeError('error')
        # 不能同时投票/放弃
        if len(set(self.vote_confirm).intersection(set(self.vote_cancel))) > 0:
            raise RuntimeError('error')

        # 验证狼人座位号
        if any(sn not in [1, 2] for sn in self.kill.keys()):
            raise RuntimeError('error')

        # 验证女巫座位号
        if self.rescue_confirm is not None and self.rescue_confirm != 3:
            raise RuntimeError('error')

        if self.rescue_cancel is not None and self.rescue_cancel != 3:
            raise RuntimeError('error')

        if self.poison_select is not None and self.poison_select[0] != 3:
            raise RuntimeError('error')

        if self.poison_confirm is not None and self.poison_confirm != 3:
            raise RuntimeError('error')

        if self.poison_cancel is not None and self.poison_cancel != 3:
            raise RuntimeError('error')

    def change_to_real_seat_num(self, seat_nums: List[int]):
        self.real_seat_nums = seat_nums

        real_kill: Dict[int, int] = {}
        for sn in self.kill:
            real_kill[self.get_real_seat_num(sn)] = self.get_real_seat_num(self.kill[sn])
        self.kill = real_kill

        if self.rescue_confirm is not None:
            self.rescue_confirm = self.get_real_seat_num(self.rescue_confirm)

        if self.rescue_cancel is not None:
            self.rescue_cancel = self.get_real_seat_num(self.rescue_cancel)

        if self.poison_select is not None:
            sn, target = self.poison_select
            self.poison_select = (self.get_real_seat_num(sn), self.get_real_seat_num(target))

        if self.poison_confirm is not None:
            self.poison_confirm = self.get_real_seat_num(self.poison_confirm)

        if self.poison_cancel is not None:
            self.poison_cancel = self.get_real_seat_num(self.poison_cancel)

        real_speak_end = []
        for sn in self.speak_end:
            real_speak_end.append(self.get_real_seat_num(sn))
        self.speak_end = real_speak_end

        real_vote_select: Dict[int, int] = {}
        for sn in self.vote_select:
            real_vote_select[self.get_real_seat_num(sn)] = self.get_real_seat_num(self.vote_select[sn])
        self.vote_select = real_vote_select

        real_vote_confirm = []
        for sn in self.vote_confirm:
            real_vote_confirm.append(self.get_real_seat_num(sn))
        self.vote_confirm = real_vote_confirm

        real_vote_cancel = []
        for sn in self.vote_cancel:
            real_vote_cancel.append(self.get_real_seat_num(sn))
        self.vote_cancel = real_vote_cancel

        if self.last_words_end is not None:
            self.last_words_end = self.get_real_seat_num(self.last_words_end)

        for k in self.asserts:
            args = self.asserts[k]
            if k == 'last_night_death':
                if args != '':
                    self.asserts[k] = list(
                        map(lambda x: self.get_real_seat_num(int(float(x))), str(args).split(',')))
            elif k == 'vote_death':
                if args != '':
                    self.asserts[k] = self.get_real_seat_num(int(args))
            elif k == 'win':
                if args not in ['man', 'werewolf']:
                    raise RuntimeError('error')

    def get_real_seat_num(self, seat_num: int) -> int:
        return self.real_seat_nums[seat_num - 1]

    def __str__(self):
        return self.__class__.__name__


class ExcelCaseActionProvider(ActionProvider):

    def __init__(self, ctx: GameContext, cases: Dict[int, ExcelGameCase]):
        super().__init__(ctx)
        self.cases = cases

    @property
    def __current_case(self):
        return self.cases[self.ctx.current_turn]

    @property
    def kill_seat_nums(self) -> Dict[int, int]:
        return self.__current_case.kill

    @property
    def rescue_confirm(self) -> Optional[int]:
        return self.__current_case.rescue_confirm

    @property
    def rescue_cancel(self) -> Optional[int]:
        return self.__current_case.rescue_cancel

    @property
    def poison_select(self) -> Optional[Tuple[int, int]]:
        return self.__current_case.poison_select

    @property
    def poison_confirm(self) -> Optional[int]:
        return self.__current_case.poison_confirm

    @property
    def poison_cancel(self) -> Optional[int]:
        return self.__current_case.poison_cancel

    @property
    def speak_end(self) -> List[int]:
        return self.__current_case.speak_end

    @property
    def vote_select(self) -> Dict[int, int]:
        return self.__current_case.vote_select

    @property
    def vote_confirm(self) -> List[int]:
        return self.__current_case.vote_confirm

    @property
    def vote_cancel(self) -> List[int]:
        return self.__current_case.vote_cancel

    @property
    def last_words_end(self) -> Optional[int]:
        return self.__current_case.last_words_end

    @property
    def asserts(self) -> Dict[str, Any]:
        return self.__current_case.asserts


class DslCase(object):

    def __init__(self):
        self.werewolf_seat_nums: List[int] = []
        self.witch_seat_num: int = -1
        self.villager_seat_nums: List[int] = []
        self.turn_operations: Dict[int, List[Tuple[str, Tuple]]] = {}
        self.turn_asserts: Dict[int, List[Tuple[str, Tuple]]] = {}
