"""
<AUTHOR>
@date 2020/7/13 0013
"""
from typing import Dict, List, Tuple, Optional

import time
import xlwt
from xlwt import Workbook, Worksheet

import im_client as im
from case_tool import do_prepare_db, do_prepare_apollo
from case_tool import read_gift_cases
from domain import UserConfig, GiftConfig
from langren_http import HttpCall
from langren_im import ImCall

host = 'lrqa.langren001.com'
case_xlsx_path = r'./resource/send_gift_validate_hg.xlsx'

# 物品到账等待时间
GIFT_WAIT_SECONDS = 1

OPTIONAL_INT = Optional[int]

ER = Dict[str, Tuple[OPTIONAL_INT, int, OPTIONAL_INT, OPTIONAL_INT]]
VR = Tuple[Optional[bool], ER]


def do_validate(user_cfg: Dict[int, UserConfig], cases: List[Tuple[int, int, int, int]]) -> \
        List[Tuple[VR, VR, VR, VR, VR, VR, VR, VR, VR]]:
    http_calls: Dict[int, HttpCall] = {}
    im_calls: Dict[int, ImCall] = {}
    gift_cfg: Dict[int, GiftConfig] = {}

    for from_user_id, to_user_id, gift_id, count in cases:
        from_user = user_cfg[from_user_id]
        to_user = user_cfg[to_user_id]
        if from_user_id not in http_calls:
            from_http_call = HttpCall(host)
            _, im_token = from_http_call.login(from_user.user_identity,
                                               from_user.access_token,
                                               from_user.device_id)
            from_im_call = ImCall(host)
            from_im_call.login(from_user_id, im_token, from_user.device_id)

            http_calls[from_user_id] = from_http_call
            im_calls[from_user_id] = from_im_call

        if to_user_id not in http_calls:
            to_http_call = HttpCall(host)
            _, im_token = to_http_call.login(to_user.user_identity,
                                             to_user.access_token,
                                             to_user.device_id)

            http_calls[to_user_id] = to_http_call

        if gift_id not in gift_cfg:
            gift_config = http_calls[from_user_id].get_gift_config(gift_id)
            gift_cfg[gift_id] = gift_config

    results: List[Tuple[VR, VR, VR, VR, VR, VR, VR, VR, VR]] = []
    for from_user_id, to_user_id, gift_id, count in cases:
        from_http_call = http_calls[from_user_id]
        to_http_call = http_calls[to_user_id]

        from_im_call = im_calls[from_user_id]
        original_to_popularity = to_http_call.get_popularity(to_user_id)
        original_from_balance = from_http_call.get_balance()
        original_from_stored_gifts = from_http_call.get_stored_gifts()
        original_to_balance = to_http_call.get_balance()
        original_to_vip = to_http_call.get_vip_expire_time()
        original_to_decorations = to_http_call.get_decorations()
        original_to_stored_gifts = to_http_call.get_stored_gifts()
        original_to_props = to_http_call.get_props()

        gift_config = from_http_call.get_gift_config(gift_id)
        print(gift_config)
        popularity, effect = from_im_call.send_gift(from_user_id, to_user_id, gift_config,
                                                    count)
        print(popularity, effect)
        # 物品到账可能有延迟，等待一会
        time.sleep(GIFT_WAIT_SECONDS)

        # 校验人气
        current_to_popularity = to_http_call.get_popularity(to_user_id)
        popularity_ok = (current_to_popularity == original_to_popularity + popularity, {
            '人气': (original_to_popularity, popularity, original_to_popularity + popularity, current_to_popularity)})

        # 校验存量礼物扣除
        remain_count = count
        from_stored_gifts_ok: VR = (None, {})
        gift_name = gift_config.name
        if gift_name in original_from_stored_gifts:
            original_from_store_gifts_count = original_from_stored_gifts[gift_name]
            if original_from_store_gifts_count > 0:
                if original_from_store_gifts_count >= count:
                    expected_stored_count = original_from_store_gifts_count - count
                    remain_count = 0
                else:
                    expected_stored_count = 0
                    remain_count = count - original_from_store_gifts_count
                # 自己送自己又爆出了存量礼物
                new_stored_count = 0
                if from_user_id == to_user_id and gift_name in effect:
                    new_stored_count = effect[gift_name]

                current_from_stored_gifts = from_http_call.get_stored_gifts()
                current_from_store_gifts_count = current_from_stored_gifts[
                    gift_name] if gift_name in current_from_stored_gifts else 0

                from_stored_gifts_ok = (current_from_store_gifts_count == expected_stored_count + new_stored_count, {
                    '送礼方存量礼物': (original_from_store_gifts_count, remain_count - count + new_stored_count,
                                expected_stored_count + new_stored_count,
                                current_from_store_gifts_count)})

        current_to_decorations = None
        current_to_stored_gifts = None
        current_to_props = None

        from_balance_ok: VR = (None, {})
        to_balance_ok: VR = (None, {})
        vip_ok: VR = (None, {})

        has_gold = False

        decoration_ok_list: List[bool] = []
        decoration_detail_list: ER = {}

        to_stored_gifts_ok_list: List[bool] = []
        to_stored_gifts_detail_list: ER = {}

        props_ok_list: List[bool] = []
        props_detail_list: ER = {}
        other_detail_list: ER = {}
        for e in effect:
            v = effect[e]
            if e == '金币':
                has_gold = True
                current_to_balance = to_http_call.get_balance()
                current_from_balance = from_http_call.get_balance()

                if from_user_id == to_user_id:
                    amount = - remain_count * gift_config.amount + v
                    from_balance_ok = (current_to_balance == original_to_balance + amount,
                                       {'送礼方金币': (original_to_balance, amount, original_to_balance + amount,
                                                  current_to_balance)})
                    to_balance_ok = (from_balance_ok[0], {})
                else:
                    from_amount = - remain_count * gift_config.amount
                    from_balance_ok = (current_from_balance == original_from_balance + from_amount,
                                       {'送礼方金币': (
                                           original_from_balance, from_amount, original_from_balance + from_amount,
                                           current_from_balance)})
                    to_amount = v
                    to_balance_ok = (current_to_balance == original_to_balance + to_amount,
                                     {'收礼方金币': (original_to_balance, to_amount, original_to_balance + to_amount,
                                                current_to_balance)})
            elif e == 'VIP':
                # VIP转换为剩余天数计算
                current_to_vip = to_http_call.get_vip_expire_time()
                now = time.time() * 1000
                if original_to_vip == 0:
                    original_to_vip_days = 0
                else:
                    original_to_vip_days = int((original_to_vip - now) / (24 * 60 * 60 * 1000))
                current_to_vip_days = int((current_to_vip - now) / (24 * 60 * 60 * 1000))
                vip_ok = (current_to_vip_days == original_to_vip_days + v,
                          {'VIP': (original_to_vip_days, v, original_to_vip_days + v,
                                   current_to_vip_days)})
            elif e in original_to_decorations:
                # 校验装扮
                if current_to_decorations is None:
                    current_to_decorations = to_http_call.get_decorations()
                ov = original_to_decorations[e]
                cv = current_to_decorations[e]
                if ov == -1:
                    decoration_ok_list.append(cv == -1)
                    decoration_detail_list[e] = (ov, v, -1, cv)
                elif cv != -1:
                    decoration_ok_list.append(cv == ov + v)
                    decoration_detail_list[e] = (ov, v, ov + v, cv)
            elif e in original_to_stored_gifts:
                # 存量礼物
                if current_to_stored_gifts is None:
                    current_to_stored_gifts = to_http_call.get_stored_gifts()

                # 自己送自己 减去消耗的存量礼物
                deduct_count = 0
                if from_user_id == to_user_id and e == gift_name:
                    deduct_count = remain_count - count

                ov = original_to_stored_gifts[e]
                cv = current_to_stored_gifts[e]
                to_stored_gifts_ok_list.append(cv == ov + v + deduct_count)
                to_stored_gifts_detail_list[e] = (ov, v + deduct_count, ov + v + deduct_count, cv)
            elif e in original_to_props:
                # 道具卡
                if current_to_props is None:
                    current_to_props = to_http_call.get_props()
                ov = original_to_props[e]
                cv = current_to_props[e]
                props_ok_list.append(cv == ov + v)
                props_detail_list[e] = (ov, v, ov + v, cv)
            else:
                other_detail_list[e] = (None, v, None, None)

        if not has_gold:
            # 没有爆出金币只计算送礼方金币扣除情况
            current_from_balance = from_http_call.get_balance()
            from_amount = - remain_count * gift_config.amount
            from_balance_ok = (current_from_balance == original_from_balance + from_amount,
                               {'送礼方金币': (
                                   original_from_balance, from_amount, original_from_balance + from_amount,
                                   current_from_balance)})

        results.append((popularity_ok, from_balance_ok, to_balance_ok, vip_ok,
                        (all(decoration_ok_list) if len(decoration_ok_list) > 0 else None, decoration_detail_list),
                        from_stored_gifts_ok,
                        (all(to_stored_gifts_ok_list) if len(to_stored_gifts_ok_list) > 0 else None,
                         to_stored_gifts_detail_list),
                        (all(props_ok_list) if len(props_ok_list) > 0 else None, props_detail_list),
                        (None, other_detail_list)))

    return results


def write_results(
        data: Dict[str, Tuple[List[Tuple[int, int, int, int]], List[Tuple[VR, VR, VR, VR, VR, VR, VR, VR, VR]]]]):
    workbook: Workbook = Workbook(encoding='utf8')

    for case_name in data:
        cases, all_result = data[case_name]
        st: Worksheet = workbook.add_sheet(f'CASE_{case_name}')
        st.set_panes_frozen(True)
        st.set_horz_split_pos(1)
        st.set_vert_split_pos(4)

        case_headers = ['送礼方ID', '收礼方ID', '礼物ID', '礼物数量', '全部到账', '人气', '送礼方金币', '收礼方金币', 'VIP', '装扮',
                        '送礼方存量礼物', '收礼方存量礼物', '道具卡']
        for i in range(0, len(case_headers)):
            st.write(0, i, case_headers[i])

        case_num = len(cases)
        for i in range(0, case_num):
            from_user_id, to_user_id, gift_id, count = cases[i]
            r = i + 1
            st.write(r, 0, from_user_id)
            st.write(r, 1, to_user_id)
            st.write(r, 2, gift_id)
            st.write(r, 3, count)

            result = all_result[i][:-1]
            __write_assert_cell(st, r, 4, all(x[0] for x in result if x[0] is not None))

            for ri in range(0, len(result)):
                rc = ri + 5
                __write_assert_cell(st, r, rc, result[ri][0])

        st: Worksheet = workbook.add_sheet(f'DETAIL_{case_name}')
        st.set_panes_frozen(True)
        st.set_horz_split_pos(1)
        detail_headers = ['名称', '初始', '获得', '预期', '实际']
        for i in range(0, len(detail_headers)):
            st.write(0, i, detail_headers[i])

        r = 1
        for i in range(0, len(all_result)):
            st.write_merge(r, r, 0, 4, i + 1, xlwt.easyxf('align: horz center, vert center;'))
            r += 1

            detail_names = []
            decoration_names = {}

            details = {}
            for rt in all_result[i]:
                detail = rt[1]
                details.update(detail)
                for n in detail.keys():
                    detail_names.append(n)

            decoration_names.update(all_result[i][4][1])

            for d in detail_names:
                is_deco = d in decoration_names
                detail = details[d]

                original, amount, expect, current = detail
                style = xlwt.Style.default_style
                if original is None and current is None:
                    style = xlwt.easyxf('font: color-index white; pattern: pattern solid, fore_colour yellow;')
                elif expect != current:
                    style = xlwt.easyxf('font: color-index white; pattern: pattern solid, fore_colour red;')

                st.write(r, 0, d, style)
                for di in range(0, len(detail)):
                    dc = di + 1
                    dv = detail[di]
                    if is_deco:
                        __write_decoration_cell(st, r, dc, dv, style)
                    else:
                        st.write(r, dc, dv, style)
                r += 1

    workbook.save(r'./report/send_gift_validate_%s.xlsx' % time.strftime('%Y%m%d_%H%M%S'))


def __write_assert_cell(st: Worksheet, r: int, c: int, v: bool):
    if v is None:
        return
    if v:
        st.write(r, c, '是')
    else:
        failed_style = xlwt.easyxf('font: color-index white; pattern: pattern solid, fore_colour red;')
        st.write(r, c, '否', failed_style)


def __write_decoration_cell(st: Worksheet, r: int, c: int, v: int, style: xlwt.Style = xlwt.Style.default_style):
    if v != -1:
        st.write(r, c, v, style)
    else:
        forever_style = xlwt.easyxf('font: color-index white; pattern: pattern solid, fore_colour green;') \
            if style is xlwt.Style.default_style else style
        st.write(r, c, '永久', forever_style)


def main():
    start_time = time.time()

    user_cfg, prepare_database, prepare_apollo, all_case = read_gift_cases(case_xlsx_path)

    do_prepare_db(prepare_database)
    do_prepare_apollo(prepare_apollo)

    im.start_reactor()
    try:
        all_effects: Dict[
            str, Tuple[List[Tuple[int, int, int, int]], List[Tuple[VR, VR, VR, VR, VR, VR, VR, VR, VR]]]] = {}
        for case_name in all_case:
            print(f'start to run case {case_name}')
            cases = all_case[case_name]
            results = do_validate(user_cfg, cases)
            all_effects[case_name] = (cases, results)

        print('start to write_results...')
        write_results(all_effects)
    finally:
        im.stop_reactor()
    end_time = time.time()
    print('costs: %ss' % (end_time - start_time))


if __name__ == '__main__':
    main()
