import requests


def upload_file():
    headers = {'cookie': 'SESSION=9ed112bf-9afd-449c-bd90-cb1757f3ecbe', 'charset': 'utf-8'}
    url = 'https://qa-rancher.langren001.com:90/werewolf/config/turtleSoup/config/upload'
    file_path = r"D:\download\wolf_admin_test\excel_handle\海龟汤题.xls"
    with open(file_path, 'rb') as file:
        # 读取文件的二进制数据
        binary_data = file.read()
        # 构建 multipart/form-data 数据
        files = {'excelFile': (file_path.split('\\')[-1], binary_data, 'application/vnd.ms-excel')}
        # 发送 POST 请求
        response = requests.post(url, files=files, headers=headers)
        print(response.text)


# 调用函数
upload_file()

def p():
    url='https://qa-rancher.langren001.com:90/userAdmin/banReason/suggestion/config/createOrUpdate'