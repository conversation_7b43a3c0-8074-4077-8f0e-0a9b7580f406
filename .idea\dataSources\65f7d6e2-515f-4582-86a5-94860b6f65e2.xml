<?xml version="1.0" encoding="UTF-8"?>
<dataSource name="ClickHouse - @cc-bp1j6i01q18k59m5q.clickhouse.ads.aliyuncs.com">
  <database-model serializer="dbm" dbms="CLICKHOUSE" family-id="CLICKHOUSE" format-version="4.17">
    <root id="1">
      <ServerVersion>22.8.5.29</ServerVersion>
    </root>
    <schema id="2" parent="1" name="langren">
      <Current>1</Current>
    </schema>
    <routine id="3" parent="1" name="BIT_AND">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="4" parent="1" name="BIT_OR">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="5" parent="1" name="BIT_XOR">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="6" parent="1" name="CAST">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="7" parent="1" name="CHARACTER_LENGTH">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="8" parent="1" name="CHAR_LENGTH">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="9" parent="1" name="COVAR_POP">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="10" parent="1" name="COVAR_SAMP">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="11" parent="1" name="CRC32">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="12" parent="1" name="CRC32IEEE">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="13" parent="1" name="CRC64">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="14" parent="1" name="DATABASE">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="15" parent="1" name="DATE">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="16" parent="1" name="DATE_TRUNC">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="17" parent="1" name="DAY">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="18" parent="1" name="DAYOFMONTH">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="19" parent="1" name="DAYOFWEEK">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="20" parent="1" name="DAYOFYEAR">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="21" parent="1" name="FQDN">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="22" parent="1" name="FROM_BASE64">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="23" parent="1" name="FROM_UNIXTIME">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="24" parent="1" name="HOUR">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="25" parent="1" name="INET6_ATON">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="26" parent="1" name="INET6_NTOA">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="27" parent="1" name="INET_ATON">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="28" parent="1" name="INET_NTOA">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="29" parent="1" name="IPv4CIDRToRange">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="30" parent="1" name="IPv4NumToString">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="31" parent="1" name="IPv4NumToStringClassC">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="32" parent="1" name="IPv4StringToNum">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="33" parent="1" name="IPv4StringToNumOrDefault">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="34" parent="1" name="IPv4StringToNumOrNull">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="35" parent="1" name="IPv4ToIPv6">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="36" parent="1" name="IPv6CIDRToRange">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="37" parent="1" name="IPv6NumToString">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="38" parent="1" name="IPv6StringToNum">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="39" parent="1" name="IPv6StringToNumOrDefault">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="40" parent="1" name="IPv6StringToNumOrNull">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="41" parent="1" name="JSONExtract">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="42" parent="1" name="JSONExtractArrayRaw">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="43" parent="1" name="JSONExtractBool">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="44" parent="1" name="JSONExtractFloat">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="45" parent="1" name="JSONExtractInt">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="46" parent="1" name="JSONExtractKeys">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="47" parent="1" name="JSONExtractKeysAndValues">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="48" parent="1" name="JSONExtractKeysAndValuesRaw">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="49" parent="1" name="JSONExtractRaw">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="50" parent="1" name="JSONExtractString">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="51" parent="1" name="JSONExtractUInt">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="52" parent="1" name="JSONHas">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="53" parent="1" name="JSONKey">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="54" parent="1" name="JSONLength">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="55" parent="1" name="JSONType">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="56" parent="1" name="JSON_EXISTS">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="57" parent="1" name="JSON_QUERY">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="58" parent="1" name="JSON_VALUE">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="59" parent="1" name="L1Distance">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="60" parent="1" name="L1Norm">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="61" parent="1" name="L1Normalize">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="62" parent="1" name="L2Distance">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="63" parent="1" name="L2Norm">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="64" parent="1" name="L2Normalize">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="65" parent="1" name="L2SquaredDistance">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="66" parent="1" name="L2SquaredNorm">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="67" parent="1" name="LAST_DAY">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="68" parent="1" name="LinfDistance">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="69" parent="1" name="LinfNorm">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="70" parent="1" name="LinfNormalize">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="71" parent="1" name="LpDistance">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="72" parent="1" name="LpNorm">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="73" parent="1" name="LpNormalize">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="74" parent="1" name="MACNumToString">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="75" parent="1" name="MACStringToNum">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="76" parent="1" name="MACStringToOUI">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="77" parent="1" name="MD4">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="78" parent="1" name="MD5">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="79" parent="1" name="MINUTE">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="80" parent="1" name="MONTH">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="81" parent="1" name="QUARTER">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="82" parent="1" name="REGEXP_MATCHES">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="83" parent="1" name="REGEXP_REPLACE">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="84" parent="1" name="SECOND">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="85" parent="1" name="SHA1">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="86" parent="1" name="SHA224">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="87" parent="1" name="SHA256">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="88" parent="1" name="SHA384">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="89" parent="1" name="SHA512">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="90" parent="1" name="STDDEV_POP">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="91" parent="1" name="STDDEV_SAMP">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="92" parent="1" name="SVG">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="93" parent="1" name="TO_BASE64">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="94" parent="1" name="URLHash">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="95" parent="1" name="URLHierarchy">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="96" parent="1" name="URLPathHierarchy">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="97" parent="1" name="UUIDNumToString">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="98" parent="1" name="UUIDStringToNum">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="99" parent="1" name="VAR_POP">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="100" parent="1" name="VAR_SAMP">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="101" parent="1" name="YEAR">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="102" parent="1" name="_CAST">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="103" parent="1" name="__bitBoolMaskAnd">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="104" parent="1" name="__bitBoolMaskOr">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="105" parent="1" name="__bitSwapLastTwo">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="106" parent="1" name="__bitWrapperFunc">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="107" parent="1" name="__getScalar">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="108" parent="1" name="abs">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="109" parent="1" name="accurateCast">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="110" parent="1" name="accurateCastOrDefault">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="111" parent="1" name="accurateCastOrNull">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="112" parent="1" name="acos">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="113" parent="1" name="acosh">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="114" parent="1" name="addDays">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="115" parent="1" name="addHours">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="116" parent="1" name="addMicroseconds">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="117" parent="1" name="addMilliseconds">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="118" parent="1" name="addMinutes">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="119" parent="1" name="addMonths">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="120" parent="1" name="addNanoseconds">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="121" parent="1" name="addQuarters">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="122" parent="1" name="addSeconds">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="123" parent="1" name="addWeeks">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="124" parent="1" name="addYears">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="125" parent="1" name="addressToLine">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="126" parent="1" name="addressToLineWithInlines">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="127" parent="1" name="addressToSymbol">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="128" parent="1" name="aes_decrypt_mysql">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="129" parent="1" name="aes_encrypt_mysql">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="130" parent="1" name="aggThrow">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="131" parent="1" name="alphaTokens">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="132" parent="1" name="and">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="133" parent="1" name="any">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="134" parent="1" name="anyHeavy">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="135" parent="1" name="anyLast">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="136" parent="1" name="appendTrailingCharIfAbsent">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="137" parent="1" name="argMax">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="138" parent="1" name="argMin">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="139" parent="1" name="array">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="140" parent="1" name="arrayAUC">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="141" parent="1" name="arrayAll">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="142" parent="1" name="arrayAvg">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="143" parent="1" name="arrayCompact">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="144" parent="1" name="arrayConcat">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="145" parent="1" name="arrayCount">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="146" parent="1" name="arrayCumSum">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="147" parent="1" name="arrayCumSumNonNegative">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="148" parent="1" name="arrayDifference">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="149" parent="1" name="arrayDistinct">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="150" parent="1" name="arrayElement">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="151" parent="1" name="arrayEnumerate">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="152" parent="1" name="arrayEnumerateDense">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="153" parent="1" name="arrayEnumerateDenseRanked">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="154" parent="1" name="arrayEnumerateUniq">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="155" parent="1" name="arrayEnumerateUniqRanked">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="156" parent="1" name="arrayExists">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="157" parent="1" name="arrayFill">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="158" parent="1" name="arrayFilter">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="159" parent="1" name="arrayFirst">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="160" parent="1" name="arrayFirstIndex">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="161" parent="1" name="arrayFirstOrNull">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="162" parent="1" name="arrayFlatten">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="163" parent="1" name="arrayIntersect">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="164" parent="1" name="arrayJoin">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="165" parent="1" name="arrayLast">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="166" parent="1" name="arrayLastIndex">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="167" parent="1" name="arrayLastOrNull">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="168" parent="1" name="arrayMap">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="169" parent="1" name="arrayMax">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="170" parent="1" name="arrayMin">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="171" parent="1" name="arrayPopBack">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="172" parent="1" name="arrayPopFront">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="173" parent="1" name="arrayProduct">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="174" parent="1" name="arrayPushBack">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="175" parent="1" name="arrayPushFront">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="176" parent="1" name="arrayReduce">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="177" parent="1" name="arrayReduceInRanges">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="178" parent="1" name="arrayResize">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="179" parent="1" name="arrayReverse">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="180" parent="1" name="arrayReverseFill">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="181" parent="1" name="arrayReverseSort">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="182" parent="1" name="arrayReverseSplit">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="183" parent="1" name="arraySlice">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="184" parent="1" name="arraySort">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="185" parent="1" name="arraySplit">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="186" parent="1" name="arrayStringConcat">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="187" parent="1" name="arraySum">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="188" parent="1" name="arrayUniq">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="189" parent="1" name="arrayWithConstant">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="190" parent="1" name="arrayZip">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="191" parent="1" name="asin">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="192" parent="1" name="asinh">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="193" parent="1" name="assumeNotNull">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="194" parent="1" name="atan">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="195" parent="1" name="atan2">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="196" parent="1" name="atanh">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="197" parent="1" name="avg">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="198" parent="1" name="avgWeighted">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="199" parent="1" name="bar">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="200" parent="1" name="base58Decode">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="201" parent="1" name="base58Encode">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="202" parent="1" name="base64Decode">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="203" parent="1" name="base64Encode">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="204" parent="1" name="basename">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="205" parent="1" name="bin">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="206" parent="1" name="bitAnd">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="207" parent="1" name="bitCount">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="208" parent="1" name="bitHammingDistance">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="209" parent="1" name="bitNot">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="210" parent="1" name="bitOr">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="211" parent="1" name="bitPositionsToArray">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="212" parent="1" name="bitRotateLeft">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="213" parent="1" name="bitRotateRight">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="214" parent="1" name="bitShiftLeft">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="215" parent="1" name="bitShiftRight">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="216" parent="1" name="bitSlice">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="217" parent="1" name="bitTest">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="218" parent="1" name="bitTestAll">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="219" parent="1" name="bitTestAny">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="220" parent="1" name="bitXor">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="221" parent="1" name="bitmapAnd">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="222" parent="1" name="bitmapAndCardinality">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="223" parent="1" name="bitmapAndnot">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="224" parent="1" name="bitmapAndnotCardinality">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="225" parent="1" name="bitmapBuild">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="226" parent="1" name="bitmapCardinality">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="227" parent="1" name="bitmapContains">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="228" parent="1" name="bitmapHasAll">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="229" parent="1" name="bitmapHasAny">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="230" parent="1" name="bitmapMax">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="231" parent="1" name="bitmapMin">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="232" parent="1" name="bitmapOr">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="233" parent="1" name="bitmapOrCardinality">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="234" parent="1" name="bitmapSubsetInRange">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="235" parent="1" name="bitmapSubsetLimit">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="236" parent="1" name="bitmapToArray">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="237" parent="1" name="bitmapTransform">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="238" parent="1" name="bitmapXor">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="239" parent="1" name="bitmapXorCardinality">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="240" parent="1" name="bitmaskToArray">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="241" parent="1" name="bitmaskToList">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="242" parent="1" name="blockNumber">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="243" parent="1" name="blockSerializedSize">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="244" parent="1" name="blockSize">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="245" parent="1" name="boundingRatio">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="246" parent="1" name="buildId">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="247" parent="1" name="byteSize">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="248" parent="1" name="caseWithExpr">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="249" parent="1" name="caseWithExpression">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="250" parent="1" name="caseWithoutExpr">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="251" parent="1" name="caseWithoutExpression">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="252" parent="1" name="catboostEvaluate">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="253" parent="1" name="categoricalInformationValue">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="254" parent="1" name="cbrt">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="255" parent="1" name="ceil">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="256" parent="1" name="ceiling">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="257" parent="1" name="char">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="258" parent="1" name="cityHash64">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="259" parent="1" name="coalesce">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="260" parent="1" name="concat">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="261" parent="1" name="concatAssumeInjective">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="262" parent="1" name="connectionId">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="263" parent="1" name="connection_id">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="264" parent="1" name="contingency">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="265" parent="1" name="convertCharset">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="266" parent="1" name="corr">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="267" parent="1" name="corrStable">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="268" parent="1" name="cos">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="269" parent="1" name="cosh">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="270" parent="1" name="cosineDistance">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="271" parent="1" name="count">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="272" parent="1" name="countDigits">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="273" parent="1" name="countEqual">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="274" parent="1" name="countMatches">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="275" parent="1" name="countMatchesCaseInsensitive">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="276" parent="1" name="countSubstrings">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="277" parent="1" name="countSubstringsCaseInsensitive">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="278" parent="1" name="countSubstringsCaseInsensitiveUTF8">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="279" parent="1" name="covarPop">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="280" parent="1" name="covarPopStable">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="281" parent="1" name="covarSamp">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="282" parent="1" name="covarSampStable">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="283" parent="1" name="cramersV">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="284" parent="1" name="cramersVBiasCorrected">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="285" parent="1" name="currentDatabase">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="286" parent="1" name="currentProfiles">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="287" parent="1" name="currentRoles">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="288" parent="1" name="currentUser">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="289" parent="1" name="cutFragment">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="290" parent="1" name="cutIPv6">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="291" parent="1" name="cutQueryString">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="292" parent="1" name="cutQueryStringAndFragment">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="293" parent="1" name="cutToFirstSignificantSubdomain">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="294" parent="1" name="cutToFirstSignificantSubdomainCustom">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="295" parent="1" name="cutToFirstSignificantSubdomainCustomWithWWW">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="296" parent="1" name="cutToFirstSignificantSubdomainWithWWW">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="297" parent="1" name="cutURLParameter">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="298" parent="1" name="cutWWW">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="299" parent="1" name="dateDiff">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="300" parent="1" name="dateName">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="301" parent="1" name="dateTime64ToSnowflake">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="302" parent="1" name="dateTimeToSnowflake">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="303" parent="1" name="dateTrunc">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="304" parent="1" name="decodeURLComponent">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="305" parent="1" name="decodeURLFormComponent">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="306" parent="1" name="decodeXMLComponent">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="307" parent="1" name="decrypt">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="308" parent="1" name="defaultProfiles">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="309" parent="1" name="defaultRoles">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="310" parent="1" name="defaultValueOfArgumentType">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="311" parent="1" name="defaultValueOfTypeName">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="312" parent="1" name="degrees">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="313" parent="1" name="deltaSum">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="314" parent="1" name="deltaSumTimestamp">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="315" parent="1" name="demangle">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="316" parent="1" name="dense_rank">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="317" parent="1" name="detectCharset">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="318" parent="1" name="detectLanguage">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="319" parent="1" name="detectLanguageMixed">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="320" parent="1" name="detectLanguageUnknown">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="321" parent="1" name="detectProgrammingLanguage">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="322" parent="1" name="detectTonality">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="323" parent="1" name="dictGet">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="324" parent="1" name="dictGetChildren">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="325" parent="1" name="dictGetDate">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="326" parent="1" name="dictGetDateOrDefault">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="327" parent="1" name="dictGetDateTime">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="328" parent="1" name="dictGetDateTimeOrDefault">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="329" parent="1" name="dictGetDescendants">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="330" parent="1" name="dictGetFloat32">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="331" parent="1" name="dictGetFloat32OrDefault">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="332" parent="1" name="dictGetFloat64">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="333" parent="1" name="dictGetFloat64OrDefault">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="334" parent="1" name="dictGetHierarchy">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="335" parent="1" name="dictGetInt16">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="336" parent="1" name="dictGetInt16OrDefault">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="337" parent="1" name="dictGetInt32">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="338" parent="1" name="dictGetInt32OrDefault">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="339" parent="1" name="dictGetInt64">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="340" parent="1" name="dictGetInt64OrDefault">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="341" parent="1" name="dictGetInt8">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="342" parent="1" name="dictGetInt8OrDefault">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="343" parent="1" name="dictGetOrDefault">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="344" parent="1" name="dictGetOrNull">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="345" parent="1" name="dictGetString">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="346" parent="1" name="dictGetStringOrDefault">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="347" parent="1" name="dictGetUInt16">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="348" parent="1" name="dictGetUInt16OrDefault">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="349" parent="1" name="dictGetUInt32">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="350" parent="1" name="dictGetUInt32OrDefault">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="351" parent="1" name="dictGetUInt64">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="352" parent="1" name="dictGetUInt64OrDefault">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="353" parent="1" name="dictGetUInt8">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="354" parent="1" name="dictGetUInt8OrDefault">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="355" parent="1" name="dictGetUUID">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="356" parent="1" name="dictGetUUIDOrDefault">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="357" parent="1" name="dictHas">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="358" parent="1" name="dictIsIn">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="359" parent="1" name="distanceL1">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="360" parent="1" name="distanceL2">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="361" parent="1" name="distanceL2Squared">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="362" parent="1" name="distanceLinf">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="363" parent="1" name="distanceLp">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="364" parent="1" name="divide">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="365" parent="1" name="domain">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="366" parent="1" name="domainWithoutWWW">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="367" parent="1" name="dotProduct">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="368" parent="1" name="dumpColumnStructure">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="369" parent="1" name="e">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="370" parent="1" name="empty">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="371" parent="1" name="emptyArrayDate">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="372" parent="1" name="emptyArrayDateTime">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="373" parent="1" name="emptyArrayFloat32">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="374" parent="1" name="emptyArrayFloat64">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="375" parent="1" name="emptyArrayInt16">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="376" parent="1" name="emptyArrayInt32">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="377" parent="1" name="emptyArrayInt64">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="378" parent="1" name="emptyArrayInt8">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="379" parent="1" name="emptyArrayString">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="380" parent="1" name="emptyArrayToSingle">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="381" parent="1" name="emptyArrayUInt16">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="382" parent="1" name="emptyArrayUInt32">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="383" parent="1" name="emptyArrayUInt64">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="384" parent="1" name="emptyArrayUInt8">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="385" parent="1" name="enabledProfiles">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="386" parent="1" name="enabledRoles">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="387" parent="1" name="encodeURLComponent">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="388" parent="1" name="encodeURLFormComponent">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="389" parent="1" name="encodeXMLComponent">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="390" parent="1" name="encrypt">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="391" parent="1" name="endsWith">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="392" parent="1" name="entropy">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="393" parent="1" name="equals">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="394" parent="1" name="erf">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="395" parent="1" name="erfc">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="396" parent="1" name="errorCodeToName">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="397" parent="1" name="evalMLMethod">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="398" parent="1" name="exp">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="399" parent="1" name="exp10">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="400" parent="1" name="exp2">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="401" parent="1" name="exponentialMovingAverage">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="402" parent="1" name="exponentialTimeDecayedAvg">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="403" parent="1" name="exponentialTimeDecayedCount">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="404" parent="1" name="exponentialTimeDecayedMax">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="405" parent="1" name="exponentialTimeDecayedSum">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="406" parent="1" name="extract">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="407" parent="1" name="extractAll">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="408" parent="1" name="extractAllGroups">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="409" parent="1" name="extractAllGroupsHorizontal">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="410" parent="1" name="extractAllGroupsVertical">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="411" parent="1" name="extractGroups">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="412" parent="1" name="extractTextFromHTML">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="413" parent="1" name="extractURLParameter">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="414" parent="1" name="extractURLParameterNames">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="415" parent="1" name="extractURLParameters">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="416" parent="1" name="farmFingerprint64">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="417" parent="1" name="farmHash64">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="418" parent="1" name="file">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="419" parent="1" name="filesystemAvailable">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="420" parent="1" name="filesystemCapacity">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="421" parent="1" name="filesystemFree">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="422" parent="1" name="finalizeAggregation">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="423" parent="1" name="firstSignificantSubdomain">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="424" parent="1" name="firstSignificantSubdomainCustom">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="425" parent="1" name="first_value">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="426" parent="1" name="flatten">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="427" parent="1" name="flattenTuple">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="428" parent="1" name="floor">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="429" parent="1" name="format">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="430" parent="1" name="formatDateTime">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="431" parent="1" name="formatReadableQuantity">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="432" parent="1" name="formatReadableSize">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="433" parent="1" name="formatReadableTimeDelta">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="434" parent="1" name="formatRow">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="435" parent="1" name="formatRowNoNewline">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="436" parent="1" name="fragment">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="437" parent="1" name="fromModifiedJulianDay">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="438" parent="1" name="fromModifiedJulianDayOrNull">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="439" parent="1" name="fromUnixTimestamp">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="440" parent="1" name="fromUnixTimestamp64Micro">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="441" parent="1" name="fromUnixTimestamp64Milli">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="442" parent="1" name="fromUnixTimestamp64Nano">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="443" parent="1" name="fullHostName">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="444" parent="1" name="fuzzBits">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="445" parent="1" name="gccMurmurHash">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="446" parent="1" name="gcd">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="447" parent="1" name="generateUUIDv4">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="448" parent="1" name="geoDistance">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="449" parent="1" name="geoToH3">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="450" parent="1" name="geoToS2">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="451" parent="1" name="geohashDecode">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="452" parent="1" name="geohashEncode">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="453" parent="1" name="geohashesInBox">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="454" parent="1" name="getMacro">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="455" parent="1" name="getOSKernelVersion">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="456" parent="1" name="getServerPort">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="457" parent="1" name="getSetting">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="458" parent="1" name="getSizeOfEnumType">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="459" parent="1" name="getTypeSerializationStreams">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="460" parent="1" name="globalIn">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="461" parent="1" name="globalInIgnoreSet">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="462" parent="1" name="globalNotIn">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="463" parent="1" name="globalNotInIgnoreSet">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="464" parent="1" name="globalNotNullIn">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="465" parent="1" name="globalNotNullInIgnoreSet">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="466" parent="1" name="globalNullIn">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="467" parent="1" name="globalNullInIgnoreSet">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="468" parent="1" name="globalVariable">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="469" parent="1" name="greatCircleAngle">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="470" parent="1" name="greatCircleDistance">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="471" parent="1" name="greater">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="472" parent="1" name="greaterOrEquals">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="473" parent="1" name="greatest">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="474" parent="1" name="groupArray">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="475" parent="1" name="groupArrayInsertAt">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="476" parent="1" name="groupArrayMovingAvg">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="477" parent="1" name="groupArrayMovingSum">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="478" parent="1" name="groupArraySample">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="479" parent="1" name="groupBitAnd">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="480" parent="1" name="groupBitOr">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="481" parent="1" name="groupBitXor">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="482" parent="1" name="groupBitmap">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="483" parent="1" name="groupBitmapAnd">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="484" parent="1" name="groupBitmapOr">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="485" parent="1" name="groupBitmapXor">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="486" parent="1" name="groupUniqArray">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="487" parent="1" name="h3CellAreaM2">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="488" parent="1" name="h3CellAreaRads2">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="489" parent="1" name="h3Distance">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="490" parent="1" name="h3EdgeAngle">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="491" parent="1" name="h3EdgeLengthKm">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="492" parent="1" name="h3EdgeLengthM">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="493" parent="1" name="h3ExactEdgeLengthKm">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="494" parent="1" name="h3ExactEdgeLengthM">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="495" parent="1" name="h3ExactEdgeLengthRads">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="496" parent="1" name="h3GetBaseCell">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="497" parent="1" name="h3GetDestinationIndexFromUnidirectionalEdge">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="498" parent="1" name="h3GetFaces">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="499" parent="1" name="h3GetIndexesFromUnidirectionalEdge">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="500" parent="1" name="h3GetOriginIndexFromUnidirectionalEdge">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="501" parent="1" name="h3GetPentagonIndexes">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="502" parent="1" name="h3GetRes0Indexes">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="503" parent="1" name="h3GetResolution">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="504" parent="1" name="h3GetUnidirectionalEdge">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="505" parent="1" name="h3GetUnidirectionalEdgeBoundary">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="506" parent="1" name="h3GetUnidirectionalEdgesFromHexagon">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="507" parent="1" name="h3HexAreaKm2">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="508" parent="1" name="h3HexAreaM2">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="509" parent="1" name="h3HexRing">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="510" parent="1" name="h3IndexesAreNeighbors">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="511" parent="1" name="h3IsPentagon">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="512" parent="1" name="h3IsResClassIII">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="513" parent="1" name="h3IsValid">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="514" parent="1" name="h3Line">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="515" parent="1" name="h3NumHexagons">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="516" parent="1" name="h3PointDistKm">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="517" parent="1" name="h3PointDistM">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="518" parent="1" name="h3PointDistRads">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="519" parent="1" name="h3ToCenterChild">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="520" parent="1" name="h3ToChildren">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="521" parent="1" name="h3ToGeo">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="522" parent="1" name="h3ToGeoBoundary">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="523" parent="1" name="h3ToParent">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="524" parent="1" name="h3ToString">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="525" parent="1" name="h3UnidirectionalEdgeIsValid">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="526" parent="1" name="h3kRing">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="527" parent="1" name="halfMD5">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="528" parent="1" name="has">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="529" parent="1" name="hasAll">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="530" parent="1" name="hasAny">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="531" parent="1" name="hasColumnInTable">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="532" parent="1" name="hasSubstr">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="533" parent="1" name="hasThreadFuzzer">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="534" parent="1" name="hasToken">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="535" parent="1" name="hasTokenCaseInsensitive">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="536" parent="1" name="hashid">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="537" parent="1" name="hex">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="538" parent="1" name="histogram">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="539" parent="1" name="hiveHash">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="540" parent="1" name="hop">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="541" parent="1" name="hopEnd">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="542" parent="1" name="hopStart">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="543" parent="1" name="hostName">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="544" parent="1" name="hostname">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="545" parent="1" name="hypot">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="546" parent="1" name="identity">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="547" parent="1" name="if">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="548" parent="1" name="ifNotFinite">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="549" parent="1" name="ifNull">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="550" parent="1" name="ignore">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="551" parent="1" name="ilike">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="552" parent="1" name="in">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="553" parent="1" name="inIgnoreSet">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="554" parent="1" name="indexHint">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="555" parent="1" name="indexOf">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="556" parent="1" name="initialQueryID">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="557" parent="1" name="initial_query_id">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="558" parent="1" name="initializeAggregation">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="559" parent="1" name="intDiv">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="560" parent="1" name="intDivOrZero">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="561" parent="1" name="intExp10">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="562" parent="1" name="intExp2">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="563" parent="1" name="intHash32">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="564" parent="1" name="intHash64">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="565" parent="1" name="intervalLengthSum">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="566" parent="1" name="isConstant">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="567" parent="1" name="isDecimalOverflow">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="568" parent="1" name="isFinite">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="569" parent="1" name="isIPAddressInRange">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="570" parent="1" name="isIPv4String">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="571" parent="1" name="isIPv6String">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="572" parent="1" name="isInfinite">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="573" parent="1" name="isNaN">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="574" parent="1" name="isNotNull">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="575" parent="1" name="isNull">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="576" parent="1" name="isNullable">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="577" parent="1" name="isValidJSON">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="578" parent="1" name="isValidUTF8">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="579" parent="1" name="isZeroOrNull">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="580" parent="1" name="javaHash">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="581" parent="1" name="javaHashUTF16LE">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="582" parent="1" name="joinGet">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="583" parent="1" name="joinGetOrNull">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="584" parent="1" name="jumpConsistentHash">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="585" parent="1" name="kostikConsistentHash">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="586" parent="1" name="kurtPop">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="587" parent="1" name="kurtSamp">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="588" parent="1" name="lagInFrame">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="589" parent="1" name="last_value">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="590" parent="1" name="lcase">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="591" parent="1" name="lcm">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="592" parent="1" name="leadInFrame">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="593" parent="1" name="least">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="594" parent="1" name="left">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="595" parent="1" name="leftPad">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="596" parent="1" name="leftPadUTF8">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="597" parent="1" name="leftUTF8">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="598" parent="1" name="lemmatize">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="599" parent="1" name="length">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="600" parent="1" name="lengthUTF8">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="601" parent="1" name="less">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="602" parent="1" name="lessOrEquals">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="603" parent="1" name="lgamma">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="604" parent="1" name="like">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="605" parent="1" name="ln">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="606" parent="1" name="locate">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="607" parent="1" name="log">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="608" parent="1" name="log10">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="609" parent="1" name="log1p">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="610" parent="1" name="log2">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="611" parent="1" name="logTrace">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="612" parent="1" name="lowCardinalityIndices">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="613" parent="1" name="lowCardinalityKeys">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="614" parent="1" name="lower">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="615" parent="1" name="lowerUTF8">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="616" parent="1" name="lpad">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="617" parent="1" name="makeDate">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="618" parent="1" name="makeDate32">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="619" parent="1" name="makeDateTime">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="620" parent="1" name="makeDateTime64">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="621" parent="1" name="mannWhitneyUTest">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="622" parent="1" name="map">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="623" parent="1" name="mapAdd">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="624" parent="1" name="mapApply">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="625" parent="1" name="mapContains">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="626" parent="1" name="mapContainsKeyLike">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="627" parent="1" name="mapExtractKeyLike">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="628" parent="1" name="mapFilter">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="629" parent="1" name="mapKeys">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="630" parent="1" name="mapPopulateSeries">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="631" parent="1" name="mapSubtract">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="632" parent="1" name="mapUpdate">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="633" parent="1" name="mapValues">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="634" parent="1" name="match">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="635" parent="1" name="materialize">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="636" parent="1" name="max">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="637" parent="1" name="max2">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="638" parent="1" name="maxIntersections">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="639" parent="1" name="maxIntersectionsPosition">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="640" parent="1" name="maxMappedArrays">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="641" parent="1" name="meanZTest">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="642" parent="1" name="median">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="643" parent="1" name="medianBFloat16">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="644" parent="1" name="medianBFloat16Weighted">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="645" parent="1" name="medianDeterministic">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="646" parent="1" name="medianExact">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="647" parent="1" name="medianExactHigh">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="648" parent="1" name="medianExactLow">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="649" parent="1" name="medianExactWeighted">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="650" parent="1" name="medianTDigest">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="651" parent="1" name="medianTDigestWeighted">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="652" parent="1" name="medianTiming">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="653" parent="1" name="medianTimingWeighted">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="654" parent="1" name="meiliMatch">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="655" parent="1" name="metroHash64">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="656" parent="1" name="mid">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="657" parent="1" name="min">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="658" parent="1" name="min2">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="659" parent="1" name="minMappedArrays">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="660" parent="1" name="minSampleSizeContinous">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="661" parent="1" name="minSampleSizeConversion">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="662" parent="1" name="minus">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="663" parent="1" name="mod">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="664" parent="1" name="modulo">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="665" parent="1" name="moduloLegacy">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="666" parent="1" name="moduloOrZero">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="667" parent="1" name="monthName">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="668" parent="1" name="multiFuzzyMatchAllIndices">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="669" parent="1" name="multiFuzzyMatchAny">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="670" parent="1" name="multiFuzzyMatchAnyIndex">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="671" parent="1" name="multiIf">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="672" parent="1" name="multiMatchAllIndices">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="673" parent="1" name="multiMatchAny">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="674" parent="1" name="multiMatchAnyIndex">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="675" parent="1" name="multiSearchAllPositions">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="676" parent="1" name="multiSearchAllPositionsCaseInsensitive">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="677" parent="1" name="multiSearchAllPositionsCaseInsensitiveUTF8">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="678" parent="1" name="multiSearchAllPositionsUTF8">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="679" parent="1" name="multiSearchAny">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="680" parent="1" name="multiSearchAnyCaseInsensitive">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="681" parent="1" name="multiSearchAnyCaseInsensitiveUTF8">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="682" parent="1" name="multiSearchAnyUTF8">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="683" parent="1" name="multiSearchFirstIndex">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="684" parent="1" name="multiSearchFirstIndexCaseInsensitive">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="685" parent="1" name="multiSearchFirstIndexCaseInsensitiveUTF8">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="686" parent="1" name="multiSearchFirstIndexUTF8">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="687" parent="1" name="multiSearchFirstPosition">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="688" parent="1" name="multiSearchFirstPositionCaseInsensitive">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="689" parent="1" name="multiSearchFirstPositionCaseInsensitiveUTF8">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="690" parent="1" name="multiSearchFirstPositionUTF8">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="691" parent="1" name="multiply">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="692" parent="1" name="murmurHash2_32">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="693" parent="1" name="murmurHash2_64">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="694" parent="1" name="murmurHash3_128">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="695" parent="1" name="murmurHash3_32">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="696" parent="1" name="murmurHash3_64">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="697" parent="1" name="negate">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="698" parent="1" name="neighbor">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="699" parent="1" name="netloc">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="700" parent="1" name="ngramDistance">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="701" parent="1" name="ngramDistanceCaseInsensitive">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="702" parent="1" name="ngramDistanceCaseInsensitiveUTF8">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="703" parent="1" name="ngramDistanceUTF8">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="704" parent="1" name="ngramMinHash">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="705" parent="1" name="ngramMinHashArg">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="706" parent="1" name="ngramMinHashArgCaseInsensitive">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="707" parent="1" name="ngramMinHashArgCaseInsensitiveUTF8">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="708" parent="1" name="ngramMinHashArgUTF8">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="709" parent="1" name="ngramMinHashCaseInsensitive">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="710" parent="1" name="ngramMinHashCaseInsensitiveUTF8">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="711" parent="1" name="ngramMinHashUTF8">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="712" parent="1" name="ngramSearch">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="713" parent="1" name="ngramSearchCaseInsensitive">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="714" parent="1" name="ngramSearchCaseInsensitiveUTF8">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="715" parent="1" name="ngramSearchUTF8">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="716" parent="1" name="ngramSimHash">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="717" parent="1" name="ngramSimHashCaseInsensitive">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="718" parent="1" name="ngramSimHashCaseInsensitiveUTF8">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="719" parent="1" name="ngramSimHashUTF8">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="720" parent="1" name="ngrams">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="721" parent="1" name="nonNegativeDerivative">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="722" parent="1" name="normL1">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="723" parent="1" name="normL2">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="724" parent="1" name="normL2Squared">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="725" parent="1" name="normLinf">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="726" parent="1" name="normLp">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="727" parent="1" name="normalizeL1">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="728" parent="1" name="normalizeL2">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="729" parent="1" name="normalizeLinf">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="730" parent="1" name="normalizeLp">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="731" parent="1" name="normalizeQuery">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="732" parent="1" name="normalizeQueryKeepNames">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="733" parent="1" name="normalizeUTF8NFC">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="734" parent="1" name="normalizeUTF8NFD">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="735" parent="1" name="normalizeUTF8NFKC">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="736" parent="1" name="normalizeUTF8NFKD">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="737" parent="1" name="normalizedQueryHash">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="738" parent="1" name="normalizedQueryHashKeepNames">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="739" parent="1" name="not">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="740" parent="1" name="notEmpty">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="741" parent="1" name="notEquals">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="742" parent="1" name="notILike">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="743" parent="1" name="notIn">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="744" parent="1" name="notInIgnoreSet">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="745" parent="1" name="notLike">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="746" parent="1" name="notNullIn">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="747" parent="1" name="notNullInIgnoreSet">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="748" parent="1" name="nothing">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="749" parent="1" name="now">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="750" parent="1" name="now64">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="751" parent="1" name="nowInBlock">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="752" parent="1" name="nth_value">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="753" parent="1" name="nullIf">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="754" parent="1" name="nullIn">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="755" parent="1" name="nullInIgnoreSet">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="756" parent="1" name="or">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="757" parent="1" name="parseDateTime32BestEffort">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="758" parent="1" name="parseDateTime32BestEffortOrNull">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="759" parent="1" name="parseDateTime32BestEffortOrZero">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="760" parent="1" name="parseDateTime64BestEffort">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="761" parent="1" name="parseDateTime64BestEffortOrNull">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="762" parent="1" name="parseDateTime64BestEffortOrZero">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="763" parent="1" name="parseDateTime64BestEffortUS">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="764" parent="1" name="parseDateTime64BestEffortUSOrNull">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="765" parent="1" name="parseDateTime64BestEffortUSOrZero">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="766" parent="1" name="parseDateTimeBestEffort">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="767" parent="1" name="parseDateTimeBestEffortOrNull">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="768" parent="1" name="parseDateTimeBestEffortOrZero">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="769" parent="1" name="parseDateTimeBestEffortUS">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="770" parent="1" name="parseDateTimeBestEffortUSOrNull">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="771" parent="1" name="parseDateTimeBestEffortUSOrZero">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="772" parent="1" name="parseTimeDelta">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="773" parent="1" name="partitionId">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="774" parent="1" name="path">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="775" parent="1" name="pathFull">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="776" parent="1" name="pi">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="777" parent="1" name="plus">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="778" parent="1" name="pointInEllipses">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="779" parent="1" name="pointInPolygon">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="780" parent="1" name="polygonAreaCartesian">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="781" parent="1" name="polygonAreaSpherical">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="782" parent="1" name="polygonConvexHullCartesian">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="783" parent="1" name="polygonPerimeterCartesian">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="784" parent="1" name="polygonPerimeterSpherical">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="785" parent="1" name="polygonsDistanceCartesian">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="786" parent="1" name="polygonsDistanceSpherical">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="787" parent="1" name="polygonsEqualsCartesian">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="788" parent="1" name="polygonsIntersectionCartesian">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="789" parent="1" name="polygonsIntersectionSpherical">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="790" parent="1" name="polygonsSymDifferenceCartesian">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="791" parent="1" name="polygonsSymDifferenceSpherical">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="792" parent="1" name="polygonsUnionCartesian">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="793" parent="1" name="polygonsUnionSpherical">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="794" parent="1" name="polygonsWithinCartesian">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="795" parent="1" name="polygonsWithinSpherical">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="796" parent="1" name="port">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="797" parent="1" name="position">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="798" parent="1" name="positionCaseInsensitive">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="799" parent="1" name="positionCaseInsensitiveUTF8">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="800" parent="1" name="positionUTF8">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="801" parent="1" name="pow">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="802" parent="1" name="power">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="803" parent="1" name="proportionsZTest">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="804" parent="1" name="protocol">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="805" parent="1" name="quantile">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="806" parent="1" name="quantileBFloat16">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="807" parent="1" name="quantileBFloat16Weighted">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="808" parent="1" name="quantileDeterministic">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="809" parent="1" name="quantileExact">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="810" parent="1" name="quantileExactExclusive">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="811" parent="1" name="quantileExactHigh">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="812" parent="1" name="quantileExactInclusive">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="813" parent="1" name="quantileExactLow">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="814" parent="1" name="quantileExactWeighted">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="815" parent="1" name="quantileTDigest">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="816" parent="1" name="quantileTDigestWeighted">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="817" parent="1" name="quantileTiming">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="818" parent="1" name="quantileTimingWeighted">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="819" parent="1" name="quantiles">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="820" parent="1" name="quantilesBFloat16">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="821" parent="1" name="quantilesBFloat16Weighted">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="822" parent="1" name="quantilesDeterministic">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="823" parent="1" name="quantilesExact">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="824" parent="1" name="quantilesExactExclusive">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="825" parent="1" name="quantilesExactHigh">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="826" parent="1" name="quantilesExactInclusive">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="827" parent="1" name="quantilesExactLow">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="828" parent="1" name="quantilesExactWeighted">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="829" parent="1" name="quantilesTDigest">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="830" parent="1" name="quantilesTDigestWeighted">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="831" parent="1" name="quantilesTiming">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="832" parent="1" name="quantilesTimingWeighted">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="833" parent="1" name="queryID">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="834" parent="1" name="queryString">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="835" parent="1" name="queryStringAndFragment">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="836" parent="1" name="query_id">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="837" parent="1" name="radians">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="838" parent="1" name="rand">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="839" parent="1" name="rand32">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="840" parent="1" name="rand64">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="841" parent="1" name="randConstant">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="842" parent="1" name="randomFixedString">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="843" parent="1" name="randomPrintableASCII">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="844" parent="1" name="randomString">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="845" parent="1" name="randomStringUTF8">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="846" parent="1" name="range">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="847" parent="1" name="rank">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="848" parent="1" name="rankCorr">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="849" parent="1" name="readWKTMultiPolygon">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="850" parent="1" name="readWKTPoint">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="851" parent="1" name="readWKTPolygon">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="852" parent="1" name="readWKTRing">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="853" parent="1" name="regexpQuoteMeta">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="854" parent="1" name="regionHierarchy">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="855" parent="1" name="regionIn">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="856" parent="1" name="regionToArea">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="857" parent="1" name="regionToCity">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="858" parent="1" name="regionToContinent">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="859" parent="1" name="regionToCountry">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="860" parent="1" name="regionToDistrict">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="861" parent="1" name="regionToName">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="862" parent="1" name="regionToPopulation">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="863" parent="1" name="regionToTopContinent">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="864" parent="1" name="reinterpret">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="865" parent="1" name="reinterpretAsDate">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="866" parent="1" name="reinterpretAsDateTime">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="867" parent="1" name="reinterpretAsFixedString">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="868" parent="1" name="reinterpretAsFloat32">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="869" parent="1" name="reinterpretAsFloat64">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="870" parent="1" name="reinterpretAsInt128">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="871" parent="1" name="reinterpretAsInt16">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="872" parent="1" name="reinterpretAsInt256">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="873" parent="1" name="reinterpretAsInt32">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="874" parent="1" name="reinterpretAsInt64">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="875" parent="1" name="reinterpretAsInt8">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="876" parent="1" name="reinterpretAsString">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="877" parent="1" name="reinterpretAsUInt128">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="878" parent="1" name="reinterpretAsUInt16">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="879" parent="1" name="reinterpretAsUInt256">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="880" parent="1" name="reinterpretAsUInt32">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="881" parent="1" name="reinterpretAsUInt64">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="882" parent="1" name="reinterpretAsUInt8">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="883" parent="1" name="reinterpretAsUUID">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="884" parent="1" name="repeat">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="885" parent="1" name="replace">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="886" parent="1" name="replaceAll">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="887" parent="1" name="replaceOne">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="888" parent="1" name="replaceRegexpAll">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="889" parent="1" name="replaceRegexpOne">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="890" parent="1" name="replicate">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="891" parent="1" name="retention">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="892" parent="1" name="reverse">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="893" parent="1" name="reverseUTF8">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="894" parent="1" name="revision">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="895" parent="1" name="right">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="896" parent="1" name="rightPad">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="897" parent="1" name="rightPadUTF8">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="898" parent="1" name="rightUTF8">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="899" parent="1" name="round">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="900" parent="1" name="roundAge">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="901" parent="1" name="roundBankers">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="902" parent="1" name="roundDown">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="903" parent="1" name="roundDuration">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="904" parent="1" name="roundToExp2">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="905" parent="1" name="rowNumberInAllBlocks">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="906" parent="1" name="rowNumberInBlock">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="907" parent="1" name="row_number">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="908" parent="1" name="rpad">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="909" parent="1" name="runningAccumulate">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="910" parent="1" name="runningConcurrency">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="911" parent="1" name="runningDifference">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="912" parent="1" name="runningDifferenceStartingWithFirstValue">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="913" parent="1" name="s2CapContains">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="914" parent="1" name="s2CapUnion">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="915" parent="1" name="s2CellsIntersect">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="916" parent="1" name="s2GetNeighbors">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="917" parent="1" name="s2RectAdd">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="918" parent="1" name="s2RectContains">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="919" parent="1" name="s2RectIntersection">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="920" parent="1" name="s2RectUnion">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="921" parent="1" name="s2ToGeo">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="922" parent="1" name="scalarProduct">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="923" parent="1" name="sequenceCount">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="924" parent="1" name="sequenceMatch">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="925" parent="1" name="sequenceNextNode">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="926" parent="1" name="serverUUID">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="927" parent="1" name="shardCount">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="928" parent="1" name="shardNum">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="929" parent="1" name="showCertificate">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="930" parent="1" name="sigmoid">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="931" parent="1" name="sign">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="932" parent="1" name="simpleJSONExtractBool">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="933" parent="1" name="simpleJSONExtractFloat">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="934" parent="1" name="simpleJSONExtractInt">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="935" parent="1" name="simpleJSONExtractRaw">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="936" parent="1" name="simpleJSONExtractString">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="937" parent="1" name="simpleJSONExtractUInt">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="938" parent="1" name="simpleJSONHas">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="939" parent="1" name="simpleLinearRegression">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="940" parent="1" name="sin">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="941" parent="1" name="singleValueOrNull">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="942" parent="1" name="sinh">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="943" parent="1" name="sipHash128">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="944" parent="1" name="sipHash64">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="945" parent="1" name="skewPop">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="946" parent="1" name="skewSamp">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="947" parent="1" name="sleep">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="948" parent="1" name="sleepEachRow">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="949" parent="1" name="snowflakeToDateTime">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="950" parent="1" name="snowflakeToDateTime64">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="951" parent="1" name="sparkbar">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="952" parent="1" name="splitByChar">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="953" parent="1" name="splitByNonAlpha">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="954" parent="1" name="splitByRegexp">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="955" parent="1" name="splitByString">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="956" parent="1" name="splitByWhitespace">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="957" parent="1" name="sqrt">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="958" parent="1" name="startsWith">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="959" parent="1" name="stddevPop">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="960" parent="1" name="stddevPopStable">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="961" parent="1" name="stddevSamp">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="962" parent="1" name="stddevSampStable">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="963" parent="1" name="stem">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="964" parent="1" name="stochasticLinearRegression">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="965" parent="1" name="stochasticLogisticRegression">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="966" parent="1" name="stringToH3">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="967" parent="1" name="studentTTest">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="968" parent="1" name="subBitmap">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="969" parent="1" name="substr">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="970" parent="1" name="substring">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="971" parent="1" name="substringUTF8">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="972" parent="1" name="subtractDays">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="973" parent="1" name="subtractHours">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="974" parent="1" name="subtractMicroseconds">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="975" parent="1" name="subtractMilliseconds">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="976" parent="1" name="subtractMinutes">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="977" parent="1" name="subtractMonths">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="978" parent="1" name="subtractNanoseconds">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="979" parent="1" name="subtractQuarters">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="980" parent="1" name="subtractSeconds">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="981" parent="1" name="subtractWeeks">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="982" parent="1" name="subtractYears">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="983" parent="1" name="sum">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="984" parent="1" name="sumCount">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="985" parent="1" name="sumKahan">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="986" parent="1" name="sumMapFiltered">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="987" parent="1" name="sumMapFilteredWithOverflow">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="988" parent="1" name="sumMapWithOverflow">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="989" parent="1" name="sumMappedArrays">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="990" parent="1" name="sumWithOverflow">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="991" parent="1" name="svg">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="992" parent="1" name="synonyms">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="993" parent="1" name="tan">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="994" parent="1" name="tanh">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="995" parent="1" name="tcpPort">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="996" parent="1" name="tgamma">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="997" parent="1" name="theilsU">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="998" parent="1" name="throwIf">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="999" parent="1" name="tid">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1000" parent="1" name="timeSlot">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1001" parent="1" name="timeSlots">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1002" parent="1" name="timeZone">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1003" parent="1" name="timeZoneOf">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1004" parent="1" name="timeZoneOffset">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1005" parent="1" name="timezone">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1006" parent="1" name="timezoneOf">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1007" parent="1" name="timezoneOffset">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1008" parent="1" name="toBool">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1009" parent="1" name="toColumnTypeName">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1010" parent="1" name="toDate">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1011" parent="1" name="toDate32">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1012" parent="1" name="toDate32OrDefault">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1013" parent="1" name="toDate32OrNull">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1014" parent="1" name="toDate32OrZero">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1015" parent="1" name="toDateOrDefault">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1016" parent="1" name="toDateOrNull">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1017" parent="1" name="toDateOrZero">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1018" parent="1" name="toDateTime">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1019" parent="1" name="toDateTime32">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1020" parent="1" name="toDateTime64">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1021" parent="1" name="toDateTime64OrDefault">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1022" parent="1" name="toDateTime64OrNull">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1023" parent="1" name="toDateTime64OrZero">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1024" parent="1" name="toDateTimeOrDefault">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1025" parent="1" name="toDateTimeOrNull">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1026" parent="1" name="toDateTimeOrZero">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1027" parent="1" name="toDayOfMonth">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1028" parent="1" name="toDayOfWeek">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1029" parent="1" name="toDayOfYear">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1030" parent="1" name="toDecimal128">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1031" parent="1" name="toDecimal128OrDefault">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1032" parent="1" name="toDecimal128OrNull">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1033" parent="1" name="toDecimal128OrZero">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1034" parent="1" name="toDecimal256">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1035" parent="1" name="toDecimal256OrDefault">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1036" parent="1" name="toDecimal256OrNull">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1037" parent="1" name="toDecimal256OrZero">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1038" parent="1" name="toDecimal32">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1039" parent="1" name="toDecimal32OrDefault">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1040" parent="1" name="toDecimal32OrNull">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1041" parent="1" name="toDecimal32OrZero">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1042" parent="1" name="toDecimal64">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1043" parent="1" name="toDecimal64OrDefault">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1044" parent="1" name="toDecimal64OrNull">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1045" parent="1" name="toDecimal64OrZero">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1046" parent="1" name="toFixedString">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1047" parent="1" name="toFloat32">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1048" parent="1" name="toFloat32OrDefault">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1049" parent="1" name="toFloat32OrNull">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1050" parent="1" name="toFloat32OrZero">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1051" parent="1" name="toFloat64">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1052" parent="1" name="toFloat64OrDefault">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1053" parent="1" name="toFloat64OrNull">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1054" parent="1" name="toFloat64OrZero">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1055" parent="1" name="toHour">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1056" parent="1" name="toIPv4">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1057" parent="1" name="toIPv4OrDefault">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1058" parent="1" name="toIPv4OrNull">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1059" parent="1" name="toIPv6">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1060" parent="1" name="toIPv6OrDefault">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1061" parent="1" name="toIPv6OrNull">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1062" parent="1" name="toISOWeek">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1063" parent="1" name="toISOYear">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1064" parent="1" name="toInt128">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1065" parent="1" name="toInt128OrDefault">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1066" parent="1" name="toInt128OrNull">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1067" parent="1" name="toInt128OrZero">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1068" parent="1" name="toInt16">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1069" parent="1" name="toInt16OrDefault">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1070" parent="1" name="toInt16OrNull">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1071" parent="1" name="toInt16OrZero">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1072" parent="1" name="toInt256">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1073" parent="1" name="toInt256OrDefault">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1074" parent="1" name="toInt256OrNull">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1075" parent="1" name="toInt256OrZero">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1076" parent="1" name="toInt32">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1077" parent="1" name="toInt32OrDefault">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1078" parent="1" name="toInt32OrNull">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1079" parent="1" name="toInt32OrZero">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1080" parent="1" name="toInt64">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1081" parent="1" name="toInt64OrDefault">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1082" parent="1" name="toInt64OrNull">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1083" parent="1" name="toInt64OrZero">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1084" parent="1" name="toInt8">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1085" parent="1" name="toInt8OrDefault">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1086" parent="1" name="toInt8OrNull">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1087" parent="1" name="toInt8OrZero">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1088" parent="1" name="toIntervalDay">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1089" parent="1" name="toIntervalHour">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1090" parent="1" name="toIntervalMicrosecond">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1091" parent="1" name="toIntervalMillisecond">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1092" parent="1" name="toIntervalMinute">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1093" parent="1" name="toIntervalMonth">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1094" parent="1" name="toIntervalNanosecond">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1095" parent="1" name="toIntervalQuarter">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1096" parent="1" name="toIntervalSecond">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1097" parent="1" name="toIntervalWeek">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1098" parent="1" name="toIntervalYear">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1099" parent="1" name="toJSONString">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1100" parent="1" name="toLastDayOfMonth">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1101" parent="1" name="toLowCardinality">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1102" parent="1" name="toMinute">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1103" parent="1" name="toModifiedJulianDay">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1104" parent="1" name="toModifiedJulianDayOrNull">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1105" parent="1" name="toMonday">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1106" parent="1" name="toMonth">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1107" parent="1" name="toNullable">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1108" parent="1" name="toQuarter">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1109" parent="1" name="toRelativeDayNum">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1110" parent="1" name="toRelativeHourNum">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1111" parent="1" name="toRelativeMinuteNum">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1112" parent="1" name="toRelativeMonthNum">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1113" parent="1" name="toRelativeQuarterNum">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1114" parent="1" name="toRelativeSecondNum">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1115" parent="1" name="toRelativeWeekNum">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1116" parent="1" name="toRelativeYearNum">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1117" parent="1" name="toSecond">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1118" parent="1" name="toStartOfDay">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1119" parent="1" name="toStartOfFifteenMinutes">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1120" parent="1" name="toStartOfFiveMinute">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1121" parent="1" name="toStartOfFiveMinutes">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1122" parent="1" name="toStartOfHour">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1123" parent="1" name="toStartOfISOYear">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1124" parent="1" name="toStartOfInterval">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1125" parent="1" name="toStartOfMicrosecond">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1126" parent="1" name="toStartOfMillisecond">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1127" parent="1" name="toStartOfMinute">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1128" parent="1" name="toStartOfMonth">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1129" parent="1" name="toStartOfNanosecond">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1130" parent="1" name="toStartOfQuarter">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1131" parent="1" name="toStartOfSecond">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1132" parent="1" name="toStartOfTenMinutes">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1133" parent="1" name="toStartOfWeek">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1134" parent="1" name="toStartOfYear">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1135" parent="1" name="toString">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1136" parent="1" name="toStringCutToZero">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1137" parent="1" name="toTime">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1138" parent="1" name="toTimeZone">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1139" parent="1" name="toTimezone">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1140" parent="1" name="toTypeName">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1141" parent="1" name="toUInt128">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1142" parent="1" name="toUInt128OrNull">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1143" parent="1" name="toUInt128OrZero">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1144" parent="1" name="toUInt16">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1145" parent="1" name="toUInt16OrDefault">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1146" parent="1" name="toUInt16OrNull">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1147" parent="1" name="toUInt16OrZero">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1148" parent="1" name="toUInt256">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1149" parent="1" name="toUInt256OrDefault">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1150" parent="1" name="toUInt256OrNull">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1151" parent="1" name="toUInt256OrZero">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1152" parent="1" name="toUInt32">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1153" parent="1" name="toUInt32OrDefault">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1154" parent="1" name="toUInt32OrNull">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1155" parent="1" name="toUInt32OrZero">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1156" parent="1" name="toUInt64">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1157" parent="1" name="toUInt64OrDefault">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1158" parent="1" name="toUInt64OrNull">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1159" parent="1" name="toUInt64OrZero">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1160" parent="1" name="toUInt8">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1161" parent="1" name="toUInt8OrDefault">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1162" parent="1" name="toUInt8OrNull">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1163" parent="1" name="toUInt8OrZero">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1164" parent="1" name="toUUID">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1165" parent="1" name="toUUIDOrDefault">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1166" parent="1" name="toUUIDOrNull">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1167" parent="1" name="toUUIDOrZero">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1168" parent="1" name="toUnixTimestamp">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1169" parent="1" name="toUnixTimestamp64Micro">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1170" parent="1" name="toUnixTimestamp64Milli">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1171" parent="1" name="toUnixTimestamp64Nano">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1172" parent="1" name="toValidUTF8">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1173" parent="1" name="toWeek">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1174" parent="1" name="toYYYYMM">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1175" parent="1" name="toYYYYMMDD">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1176" parent="1" name="toYYYYMMDDhhmmss">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1177" parent="1" name="toYear">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1178" parent="1" name="toYearWeek">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1179" parent="1" name="today">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1180" parent="1" name="tokens">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1181" parent="1" name="topK">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="1182" parent="1" name="topKWeighted">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="1183" parent="1" name="topLevelDomain">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1184" parent="1" name="transactionID">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1185" parent="1" name="transactionLatestSnapshot">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1186" parent="1" name="transactionOldestSnapshot">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1187" parent="1" name="transform">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1188" parent="1" name="translate">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1189" parent="1" name="translateUTF8">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1190" parent="1" name="trimBoth">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1191" parent="1" name="trimLeft">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1192" parent="1" name="trimRight">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1193" parent="1" name="trunc">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1194" parent="1" name="truncate">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1195" parent="1" name="tryBase64Decode">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1196" parent="1" name="tumble">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1197" parent="1" name="tumbleEnd">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1198" parent="1" name="tumbleStart">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1199" parent="1" name="tuple">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1200" parent="1" name="tupleDivide">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1201" parent="1" name="tupleDivideByNumber">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1202" parent="1" name="tupleElement">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1203" parent="1" name="tupleHammingDistance">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1204" parent="1" name="tupleMinus">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1205" parent="1" name="tupleMultiply">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1206" parent="1" name="tupleMultiplyByNumber">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1207" parent="1" name="tupleNegate">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1208" parent="1" name="tuplePlus">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1209" parent="1" name="tupleToNameValuePairs">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1210" parent="1" name="ucase">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1211" parent="1" name="unbin">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1212" parent="1" name="unhex">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1213" parent="1" name="uniq">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="1214" parent="1" name="uniqCombined">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="1215" parent="1" name="uniqCombined64">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="1216" parent="1" name="uniqExact">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="1217" parent="1" name="uniqHLL12">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="1218" parent="1" name="uniqTheta">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="1219" parent="1" name="uniqThetaIntersect">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1220" parent="1" name="uniqThetaNot">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1221" parent="1" name="uniqThetaUnion">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1222" parent="1" name="uniqUpTo">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="1223" parent="1" name="upper">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1224" parent="1" name="upperUTF8">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1225" parent="1" name="uptime">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1226" parent="1" name="user">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1227" parent="1" name="validateNestedArraySizes">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1228" parent="1" name="varPop">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="1229" parent="1" name="varPopStable">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="1230" parent="1" name="varSamp">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="1231" parent="1" name="varSampStable">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="1232" parent="1" name="vectorDifference">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1233" parent="1" name="vectorSum">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1234" parent="1" name="version">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1235" parent="1" name="visibleWidth">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1236" parent="1" name="visitParamExtractBool">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1237" parent="1" name="visitParamExtractFloat">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1238" parent="1" name="visitParamExtractInt">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1239" parent="1" name="visitParamExtractRaw">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1240" parent="1" name="visitParamExtractString">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1241" parent="1" name="visitParamExtractUInt">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1242" parent="1" name="visitParamHas">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1243" parent="1" name="week">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1244" parent="1" name="welchTTest">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="1245" parent="1" name="windowFunnel">
      <Aggregate>1</Aggregate>
    </routine>
    <routine id="1246" parent="1" name="windowID">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1247" parent="1" name="wkt">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1248" parent="1" name="wordShingleMinHash">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1249" parent="1" name="wordShingleMinHashArg">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1250" parent="1" name="wordShingleMinHashArgCaseInsensitive">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1251" parent="1" name="wordShingleMinHashArgCaseInsensitiveUTF8">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1252" parent="1" name="wordShingleMinHashArgUTF8">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1253" parent="1" name="wordShingleMinHashCaseInsensitive">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1254" parent="1" name="wordShingleMinHashCaseInsensitiveUTF8">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1255" parent="1" name="wordShingleMinHashUTF8">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1256" parent="1" name="wordShingleSimHash">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1257" parent="1" name="wordShingleSimHashCaseInsensitive">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1258" parent="1" name="wordShingleSimHashCaseInsensitiveUTF8">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1259" parent="1" name="wordShingleSimHashUTF8">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1260" parent="1" name="wyHash64">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1261" parent="1" name="xor">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1262" parent="1" name="xxHash32">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1263" parent="1" name="xxHash64">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1264" parent="1" name="yandexConsistentHash">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1265" parent="1" name="yearweek">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1266" parent="1" name="yesterday">
      <Aggregate>0</Aggregate>
    </routine>
    <routine id="1267" parent="1" name="zookeeperSessionUptime">
      <Aggregate>0</Aggregate>
    </routine>
    <table id="1268" parent="2" name="dis_activity_award_record">
      <Temporary>0</Temporary>
      <Engine>Distributed</Engine>
      <EngineParams>(&apos;default&apos;, &apos;langren&apos;, &apos;local_activity_award_record&apos;, rand())</EngineParams>
    </table>
    <table id="1269" parent="2" name="dis_activity_payment_record">
      <Temporary>0</Temporary>
      <Engine>Distributed</Engine>
      <EngineParams>(&apos;default&apos;, &apos;langren&apos;, &apos;local_activity_payment_record&apos;, rand())</EngineParams>
    </table>
    <table id="1270" parent="2" name="dis_game_room_over_detail_statistic">
      <Temporary>0</Temporary>
      <Engine>Distributed</Engine>
      <EngineParams>(&apos;default&apos;, &apos;langren&apos;, &apos;local_game_room_over_detail_statistic&apos;, rand())</EngineParams>
    </table>
    <table id="1271" parent="2" name="dis_game_room_user_match_join_record">
      <Temporary>0</Temporary>
      <Engine>Distributed</Engine>
      <EngineParams>(&apos;default&apos;, &apos;langren&apos;, &apos;local_game_room_user_match_join_record&apos;, rand())</EngineParams>
    </table>
    <table id="1272" parent="2" name="dis_game_room_user_match_record">
      <Temporary>0</Temporary>
      <Engine>Distributed</Engine>
      <EngineParams>(&apos;default&apos;, &apos;langren&apos;, &apos;local_game_room_user_match_record&apos;, rand())</EngineParams>
    </table>
    <table id="1273" parent="2" name="dis_game_room_waiting_detail_statistic">
      <Temporary>0</Temporary>
      <Engine>Distributed</Engine>
      <EngineParams>(&apos;default&apos;, &apos;langren&apos;, &apos;local_game_room_waiting_detail_statistic&apos;, rand())</EngineParams>
    </table>
    <table id="1274" parent="2" name="dis_game_statistic_record">
      <Temporary>0</Temporary>
      <Engine>Distributed</Engine>
      <EngineParams>(&apos;default&apos;, &apos;langren&apos;, &apos;local_game_statistic_record&apos;, rand())</EngineParams>
    </table>
    <table id="1275" parent="2" name="dis_profile_user_game_extra_record">
      <Temporary>0</Temporary>
      <Engine>Distributed</Engine>
      <EngineParams>(&apos;default&apos;, &apos;langren&apos;, &apos;local_profile_user_game_extra_record&apos;, rand())</EngineParams>
    </table>
    <table id="1276" parent="2" name="dis_recreation_room_detail_statistic">
      <Temporary>0</Temporary>
      <Engine>Distributed</Engine>
      <EngineParams>(&apos;default&apos;, &apos;langren&apos;, &apos;local_recreation_room_detail_statistic&apos;, rand())</EngineParams>
    </table>
    <table id="1277" parent="2" name="dis_recreation_room_entrance_click_record">
      <Temporary>0</Temporary>
      <Engine>Distributed</Engine>
      <EngineParams>(&apos;default&apos;, &apos;langren&apos;, &apos;local_recreation_room_entrance_click_record&apos;, rand())</EngineParams>
    </table>
    <table id="1278" parent="2" name="dis_recreation_room_name_type_statistic">
      <Temporary>0</Temporary>
      <Engine>Distributed</Engine>
      <EngineParams>(&apos;default&apos;, &apos;langren&apos;, &apos;local_recreation_room_name_type_statistic&apos;, rand())</EngineParams>
    </table>
    <table id="1279" parent="2" name="dis_recreation_room_tiny_game_count_statistic">
      <Temporary>0</Temporary>
      <Engine>Distributed</Engine>
      <EngineParams>(&apos;default&apos;, &apos;langren&apos;, &apos;local_recreation_room_tiny_game_count_statistic&apos;, rand())</EngineParams>
    </table>
    <table id="1280" parent="2" name="dis_recreation_room_tiny_game_duration_statistic">
      <Temporary>0</Temporary>
      <Engine>Distributed</Engine>
      <EngineParams>(&apos;default&apos;, &apos;langren&apos;, &apos;local_recreation_room_tiny_game_duration_statistic&apos;, rand())</EngineParams>
    </table>
    <table id="1281" parent="2" name="dis_recreation_room_user_count_record">
      <Temporary>0</Temporary>
      <Engine>Distributed</Engine>
      <EngineParams>(&apos;default&apos;, &apos;langren&apos;, &apos;local_recreation_room_user_count_record&apos;, rand())</EngineParams>
    </table>
    <table id="1282" parent="2" name="dis_recreation_room_user_retention_record">
      <Temporary>0</Temporary>
      <Engine>Distributed</Engine>
      <EngineParams>(&apos;default&apos;, &apos;langren&apos;, &apos;local_recreation_room_user_retention_record&apos;, rand())</EngineParams>
    </table>
    <table id="1283" parent="2" name="dis_robot_join_room_record">
      <Temporary>0</Temporary>
      <Engine>Distributed</Engine>
      <EngineParams>(&apos;default&apos;, &apos;langren&apos;, &apos;local_robot_join_room_record&apos;, rand())</EngineParams>
    </table>
    <table id="1284" parent="2" name="dis_room_call_up_record">
      <Temporary>0</Temporary>
      <Engine>Distributed</Engine>
      <EngineParams>(&apos;default&apos;, &apos;langren&apos;, &apos;local_room_call_up_record&apos;, rand())</EngineParams>
    </table>
    <table id="1285" parent="2" name="dis_room_content_fraud_record">
      <Temporary>0</Temporary>
      <Engine>Distributed</Engine>
      <EngineParams>(&apos;default&apos;, &apos;langren&apos;, &apos;local_room_content_fraud_record&apos;, rand())</EngineParams>
    </table>
    <table id="1286" parent="2" name="dis_room_content_record">
      <Temporary>0</Temporary>
      <Engine>Distributed</Engine>
      <EngineParams>(&apos;default&apos;, &apos;langren&apos;, &apos;local_room_content_record&apos;, rand())</EngineParams>
    </table>
    <table id="1287" parent="2" name="dis_user_accurate_push_condition_statistic_record">
      <Temporary>0</Temporary>
      <Engine>Distributed</Engine>
      <EngineParams>(&apos;default&apos;, &apos;langren&apos;, &apos;local_user_accurate_push_condition_statistic_record&apos;, rand())</EngineParams>
    </table>
    <table id="1288" parent="2" name="dis_user_change_reject_post_statistic_record">
      <Temporary>0</Temporary>
      <Engine>Distributed</Engine>
      <EngineParams>(&apos;default&apos;, &apos;langren&apos;, &apos;local_user_change_reject_post_statistic_record&apos;, rand())</EngineParams>
    </table>
    <table id="1289" parent="2" name="dis_user_invalid_game_record">
      <Temporary>0</Temporary>
      <Engine>Distributed</Engine>
      <EngineParams>(&apos;default&apos;, &apos;langren&apos;, &apos;local_user_invalid_game_record&apos;, rand())</EngineParams>
    </table>
    <table id="1290" parent="2" name="dis_user_kicked_room_statistic">
      <Temporary>0</Temporary>
      <Engine>Distributed</Engine>
      <EngineParams>(&apos;default&apos;, &apos;langren&apos;, &apos;local_user_kicked_room_statistic&apos;, rand())</EngineParams>
    </table>
    <table id="1291" parent="2" name="dis_user_present_record_statistic">
      <Temporary>0</Temporary>
      <Engine>Distributed</Engine>
      <EngineParams>(&apos;default&apos;, &apos;langren&apos;, &apos;local_user_present_record_statistic&apos;, rand())</EngineParams>
    </table>
    <table id="1292" parent="2" name="dis_user_quit_game_room_statistic">
      <Temporary>0</Temporary>
      <Engine>Distributed</Engine>
      <EngineParams>(&apos;default&apos;, &apos;langren&apos;, &apos;local_user_quit_game_room_statistic&apos;, rand())</EngineParams>
    </table>
    <table id="1293" parent="2" name="local_activity_award_record">
      <Temporary>0</Temporary>
      <Engine>ReplicatedMergeTree</Engine>
      <EngineParams>(&apos;/clickhouse/tables/langren/local_activity_award_record/{shard}&apos;, &apos;{replica}&apos;)
PARTITION BY toYYYYMM(createdOn)
ORDER BY createdOn
SETTINGS index_granularity = 8192</EngineParams>
    </table>
    <table id="1294" parent="2" name="local_activity_payment_record">
      <Temporary>0</Temporary>
      <Engine>ReplicatedMergeTree</Engine>
      <EngineParams>(&apos;/clickhouse/tables/langren/local_activity_payment_record/{shard}&apos;, &apos;{replica}&apos;)
PARTITION BY toYYYYMM(createdOn)
ORDER BY createdOn
SETTINGS index_granularity = 8192</EngineParams>
    </table>
    <table id="1295" parent="2" name="local_game_room_over_detail_statistic">
      <Temporary>0</Temporary>
      <Engine>ReplicatedMergeTree</Engine>
      <EngineParams>(&apos;/clickhouse/tables/langren/local_game_room_over_detail_statistic/{shard}&apos;, &apos;{replica}&apos;)
PARTITION BY toYYYYMM(createdOn)
ORDER BY createdOn
SETTINGS index_granularity = 8192</EngineParams>
    </table>
    <table id="1296" parent="2" name="local_game_room_user_match_join_record">
      <Temporary>0</Temporary>
      <Engine>ReplicatedMergeTree</Engine>
      <EngineParams>(&apos;/clickhouse/tables/langren/local_game_room_user_match_join_record/{shard}&apos;, &apos;{replica}&apos;)
PARTITION BY toYYYYMM(createdOn)
ORDER BY createdOn
SETTINGS index_granularity = 8192</EngineParams>
    </table>
    <table id="1297" parent="2" name="local_game_room_user_match_record">
      <Temporary>0</Temporary>
      <Engine>ReplicatedMergeTree</Engine>
      <EngineParams>(&apos;/clickhouse/tables/langren/local_game_room_user_match_record/{shard}&apos;, &apos;{replica}&apos;)
PARTITION BY toYYYYMM(createdOn)
ORDER BY createdOn
SETTINGS index_granularity = 8192</EngineParams>
    </table>
    <table id="1298" parent="2" name="local_game_room_waiting_detail_statistic">
      <Temporary>0</Temporary>
      <Engine>ReplicatedMergeTree</Engine>
      <EngineParams>(&apos;/clickhouse/tables/langren/local_game_room_waiting_detail_statistic/{shard}&apos;, &apos;{replica}&apos;)
PARTITION BY toYYYYMM(createdOn)
ORDER BY createdOn
SETTINGS index_granularity = 8192</EngineParams>
    </table>
    <table id="1299" parent="2" name="local_game_statistic_record">
      <Temporary>0</Temporary>
      <Engine>ReplicatedMergeTree</Engine>
      <EngineParams>(&apos;/clickhouse/tables/langren/local_game_statistic_record/{shard}&apos;, &apos;{replica}&apos;)
PARTITION BY toYYYYMM(createdOn)
ORDER BY createdOn
SETTINGS index_granularity = 8192</EngineParams>
    </table>
    <table id="1300" parent="2" name="local_profile_user_game_extra_record">
      <Temporary>0</Temporary>
      <Engine>ReplicatedMergeTree</Engine>
      <EngineParams>(&apos;/clickhouse/tables/langren/local_profile_user_game_extra_record/{shard}&apos;, &apos;{replica}&apos;)
PARTITION BY toYYYYMM(createdOn)
ORDER BY createdOn
SETTINGS index_granularity = 8192</EngineParams>
    </table>
    <table id="1301" parent="2" name="local_recreation_room_detail_statistic">
      <Temporary>0</Temporary>
      <Engine>ReplicatedMergeTree</Engine>
      <EngineParams>(&apos;/clickhouse/tables/langren/local_recreation_room_detail_statistic/{shard}&apos;, &apos;{replica}&apos;)
PARTITION BY toYYYYMM(createdOn)
ORDER BY createdOn
SETTINGS index_granularity = 8192</EngineParams>
    </table>
    <table id="1302" parent="2" name="local_recreation_room_entrance_click_record">
      <Temporary>0</Temporary>
      <Engine>ReplicatedMergeTree</Engine>
      <EngineParams>(&apos;/clickhouse/tables/langren/local_recreation_room_entrance_click_record/{shard}&apos;, &apos;{replica}&apos;)
PARTITION BY toYYYYMM(createdOn)
ORDER BY createdOn
SETTINGS index_granularity = 8192</EngineParams>
    </table>
    <table id="1303" parent="2" name="local_recreation_room_name_type_statistic">
      <Temporary>0</Temporary>
      <Engine>ReplicatedMergeTree</Engine>
      <EngineParams>(&apos;/clickhouse/tables/langren/local_recreation_room_name_type_statistic/{shard}&apos;, &apos;{replica}&apos;)
PARTITION BY toYYYYMM(createdOn)
ORDER BY createdOn
SETTINGS index_granularity = 8192</EngineParams>
    </table>
    <table id="1304" parent="2" name="local_recreation_room_tiny_game_count_statistic">
      <Temporary>0</Temporary>
      <Engine>ReplicatedMergeTree</Engine>
      <EngineParams>(&apos;/clickhouse/tables/langren/local_recreation_room_tiny_game_count_statistic/{shard}&apos;, &apos;{replica}&apos;)
PARTITION BY toYYYYMM(createdOn)
ORDER BY createdOn
SETTINGS index_granularity = 8192</EngineParams>
    </table>
    <table id="1305" parent="2" name="local_recreation_room_tiny_game_duration_statistic">
      <Temporary>0</Temporary>
      <Engine>ReplicatedMergeTree</Engine>
      <EngineParams>(&apos;/clickhouse/tables/langren/local_recreation_room_tiny_game_duration_statistic/{shard}&apos;, &apos;{replica}&apos;)
PARTITION BY toYYYYMM(createdOn)
ORDER BY createdOn
SETTINGS index_granularity = 8192</EngineParams>
    </table>
    <table id="1306" parent="2" name="local_recreation_room_user_count_record">
      <Temporary>0</Temporary>
      <Engine>ReplicatedMergeTree</Engine>
      <EngineParams>(&apos;/clickhouse/tables/langren/local_recreation_room_user_count_record/{shard}&apos;, &apos;{replica}&apos;)
PARTITION BY toYYYYMM(createdOn)
ORDER BY createdOn
SETTINGS index_granularity = 8192</EngineParams>
    </table>
    <table id="1307" parent="2" name="local_recreation_room_user_retention_record">
      <Temporary>0</Temporary>
      <Engine>ReplicatedMergeTree</Engine>
      <EngineParams>(&apos;/clickhouse/tables/langren/local_recreation_room_user_retention_record/{shard}&apos;, &apos;{replica}&apos;)
PARTITION BY toYYYYMM(createdOn)
ORDER BY createdOn
SETTINGS index_granularity = 8192</EngineParams>
    </table>
    <table id="1308" parent="2" name="local_robot_join_room_record">
      <Temporary>0</Temporary>
      <Engine>ReplicatedMergeTree</Engine>
      <EngineParams>(&apos;/clickhouse/tables/langren/local_robot_join_room_record/{shard}&apos;, &apos;{replica}&apos;)
PARTITION BY toYYYYMM(createdOn)
ORDER BY createdOn
SETTINGS index_granularity = 8192</EngineParams>
    </table>
    <table id="1309" parent="2" name="local_room_call_up_record">
      <Temporary>0</Temporary>
      <Engine>ReplicatedMergeTree</Engine>
      <EngineParams>(&apos;/clickhouse/tables/langren/local_room_call_up_record/{shard}&apos;, &apos;{replica}&apos;)
PARTITION BY toYYYYMM(createdOn)
ORDER BY createdOn
SETTINGS index_granularity = 8192</EngineParams>
    </table>
    <table id="1310" parent="2" name="local_room_content_fraud_record">
      <Temporary>0</Temporary>
      <Engine>ReplicatedMergeTree</Engine>
      <EngineParams>(&apos;/clickhouse/tables/langren/local_room_content_fraud_record/{shard}&apos;, &apos;{replica}&apos;)
PARTITION BY toYYYYMM(createdOn)
ORDER BY createdOn
SETTINGS index_granularity = 8192</EngineParams>
    </table>
    <table id="1311" parent="2" name="local_room_content_record">
      <Temporary>0</Temporary>
      <Engine>ReplicatedMergeTree</Engine>
      <EngineParams>(&apos;/clickhouse/tables/langren/local_room_content_record/{shard}&apos;, &apos;{replica}&apos;)
PARTITION BY toYYYYMM(createdOn)
ORDER BY createdOn
SETTINGS index_granularity = 8192</EngineParams>
    </table>
    <table id="1312" parent="2" name="local_user_accurate_push_condition_statistic_record">
      <Temporary>0</Temporary>
      <Engine>ReplicatedMergeTree</Engine>
      <EngineParams>(&apos;/clickhouse/tables/langren/local_user_accurate_push_condition_statistic_record/{shard}&apos;, &apos;{replica}&apos;)
PARTITION BY toYYYYMM(createdOn)
ORDER BY createdOn
SETTINGS index_granularity = 8192</EngineParams>
    </table>
    <table id="1313" parent="2" name="local_user_change_reject_post_statistic_record">
      <Temporary>0</Temporary>
      <Engine>ReplicatedMergeTree</Engine>
      <EngineParams>(&apos;/clickhouse/tables/langren/local_user_change_reject_post_statistic_record/{shard}&apos;, &apos;{replica}&apos;)
PARTITION BY toYYYYMM(createdOn)
ORDER BY createdOn
SETTINGS index_granularity = 8192</EngineParams>
    </table>
    <table id="1314" parent="2" name="local_user_invalid_game_record">
      <Temporary>0</Temporary>
      <Engine>ReplicatedMergeTree</Engine>
      <EngineParams>(&apos;/clickhouse/tables/langren/local_user_invalid_game_record/{shard}&apos;, &apos;{replica}&apos;)
PARTITION BY toYYYYMM(createdOn)
ORDER BY createdOn
SETTINGS index_granularity = 8192</EngineParams>
    </table>
    <table id="1315" parent="2" name="local_user_kicked_room_statistic">
      <Temporary>0</Temporary>
      <Engine>ReplicatedMergeTree</Engine>
      <EngineParams>(&apos;/clickhouse/tables/langren/local_user_kicked_room_statistic/{shard}&apos;, &apos;{replica}&apos;)
PARTITION BY toYYYYMM(createdOn)
ORDER BY createdOn
SETTINGS index_granularity = 8192</EngineParams>
    </table>
    <table id="1316" parent="2" name="local_user_present_record_statistic">
      <Temporary>0</Temporary>
      <Engine>ReplicatedMergeTree</Engine>
      <EngineParams>(&apos;/clickhouse/tables/langren/local_user_present_record_statistic/{shard}&apos;, &apos;{replica}&apos;)
PARTITION BY toYYYYMM(createdOn)
ORDER BY createdOn
SETTINGS index_granularity = 8192</EngineParams>
    </table>
    <table id="1317" parent="2" name="local_user_quit_game_room_statistic">
      <Temporary>0</Temporary>
      <Engine>ReplicatedMergeTree</Engine>
      <EngineParams>(&apos;/clickhouse/tables/langren/local_user_quit_game_room_statistic/{shard}&apos;, &apos;{replica}&apos;)
PARTITION BY toYYYYMM(createdOn)
ORDER BY createdOn
SETTINGS index_granularity = 8192</EngineParams>
    </table>
    <column id="1318" parent="1268" name="activityId">
      <Position>1</Position>
      <DataType>Int16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1319" parent="1268" name="boxConfigId">
      <Position>2</Position>
      <DataType>Int16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1320" parent="1268" name="awardId">
      <Position>3</Position>
      <DataType>Int16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1321" parent="1268" name="awardType">
      <Position>4</Position>
      <DataType>Int8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1322" parent="1268" name="forever">
      <Position>5</Position>
      <DataType>Int8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1323" parent="1268" name="userId">
      <Position>6</Position>
      <DataType>Int64|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1324" parent="1268" name="awardCount">
      <Position>7</Position>
      <DataType>Int16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1325" parent="1268" name="createdOn">
      <Position>8</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1326" parent="1269" name="activityId">
      <Position>1</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1327" parent="1269" name="boxConfigId">
      <Position>2</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1328" parent="1269" name="userId">
      <Position>3</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1329" parent="1269" name="price">
      <Position>4</Position>
      <DataType>Decimal(10,2 digit)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0.</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1330" parent="1269" name="level">
      <Position>5</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1331" parent="1269" name="paymentSourceType">
      <Position>6</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1332" parent="1269" name="priceType">
      <Position>7</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1333" parent="1269" name="createdOn">
      <Position>8</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1334" parent="1270" name="gameUuid">
      <Position>1</Position>
      <DataType>String|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1335" parent="1270" name="gameNum">
      <Position>2</Position>
      <DataType>UInt32|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1336" parent="1270" name="userId">
      <Position>3</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1337" parent="1270" name="endTime">
      <Position>4</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1338" parent="1270" name="gameRoomType">
      <Position>5</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1339" parent="1270" name="resultContent">
      <Position>6</Position>
      <DataType>String|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1340" parent="1270" name="remainUserCount">
      <Position>7</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1341" parent="1270" name="startRecordId">
      <Position>8</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1342" parent="1270" name="gameOverRound">
      <Position>9</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1343" parent="1270" name="deathRound">
      <Position>10</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1344" parent="1270" name="voiceDuration">
      <Position>11</Position>
      <DataType>Decimal(10,2 digit)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0.</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1345" parent="1270" name="voiceDayCount">
      <Position>12</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1346" parent="1270" name="createdOn">
      <Position>13</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1347" parent="1271" name="joinRecordId">
      <Position>1</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1348" parent="1271" name="joinId">
      <Position>2</Position>
      <DataType>String|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1349" parent="1271" name="type">
      <Position>3</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1350" parent="1271" name="userId">
      <Position>4</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1351" parent="1271" name="level">
      <Position>5</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1352" parent="1271" name="waitTime">
      <Position>6</Position>
      <DataType>Int64|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1353" parent="1271" name="waitUserNum">
      <Position>7</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1354" parent="1271" name="gameRoomType">
      <Position>8</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1355" parent="1271" name="roomNum">
      <Position>9</Position>
      <DataType>UInt32|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1356" parent="1271" name="roomUserCount">
      <Position>10</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1357" parent="1271" name="roomRemainSeatCount">
      <Position>11</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1358" parent="1271" name="roomLevel">
      <Position>12</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1359" parent="1271" name="callUp">
      <Position>13</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1360" parent="1271" name="readyUserCount">
      <Position>14</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1361" parent="1271" name="latestGameEndTime">
      <Position>15</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1362" parent="1271" name="joinTime">
      <Position>16</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1363" parent="1271" name="joinSuccess">
      <Position>17</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1364" parent="1271" name="triggerSpecialType">
      <Position>18</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1365" parent="1271" name="triggerX">
      <Position>19</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1366" parent="1271" name="createdOn">
      <Position>20</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1367" parent="1272" name="matchId">
      <Position>1</Position>
      <DataType>String|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1368" parent="1272" name="userId">
      <Position>2</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1369" parent="1272" name="type">
      <Position>3</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1370" parent="1272" name="level">
      <Position>4</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1371" parent="1272" name="gameRoomType">
      <Position>5</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1372" parent="1272" name="inSpecial">
      <Position>6</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1373" parent="1272" name="specialLevel">
      <Position>7</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1374" parent="1272" name="specialCount">
      <Position>8</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1375" parent="1272" name="notSpecialCount">
      <Position>9</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1376" parent="1272" name="specialX">
      <Position>10</Position>
      <DataType>Int16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1377" parent="1272" name="notSpecialX">
      <Position>11</Position>
      <DataType>Int16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1378" parent="1272" name="waitTime">
      <Position>12</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1379" parent="1272" name="waitUserNum">
      <Position>13</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1380" parent="1272" name="operationTime">
      <Position>14</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1381" parent="1272" name="createdOn">
      <Position>15</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1382" parent="1273" name="roomKey">
      <Position>1</Position>
      <DataType>String|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1383" parent="1273" name="gameRoomType">
      <Position>2</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1384" parent="1273" name="hasPassword">
      <Position>3</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1385" parent="1273" name="callUpRoom">
      <Position>4</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1386" parent="1273" name="inMatch">
      <Position>5</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1387" parent="1273" name="limitLevel">
      <Position>6</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1388" parent="1273" name="roomUserCount">
      <Position>7</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1389" parent="1273" name="remainSeatCount">
      <Position>8</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1390" parent="1273" name="closeSeatCount">
      <Position>9</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1391" parent="1273" name="createdOn">
      <Position>10</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1392" parent="1274" name="gameRoomType">
      <Position>1</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1393" parent="1274" name="userId">
      <Position>2</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1394" parent="1274" name="type">
      <Position>3</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1395" parent="1274" name="createdOn">
      <Position>4</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1396" parent="1275" name="userId">
      <Position>1</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1397" parent="1275" name="gameType">
      <Position>2</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>1</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1398" parent="1275" name="gameRoomType">
      <Position>3</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1399" parent="1275" name="villagerVictories">
      <Position>4</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1400" parent="1275" name="werewolfVictories">
      <Position>5</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1401" parent="1275" name="godVictories">
      <Position>6</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1402" parent="1275" name="villagerDefeats">
      <Position>7</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1403" parent="1275" name="werewolfDefeats">
      <Position>8</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1404" parent="1275" name="godDefeats">
      <Position>9</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1405" parent="1275" name="createdOn">
      <Position>10</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1406" parent="1276" name="roomKey">
      <Position>1</Position>
      <DataType>String|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1407" parent="1276" name="ownerId">
      <Position>2</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1408" parent="1276" name="roomType">
      <Position>3</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1409" parent="1276" name="callupStatus">
      <Position>4</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1410" parent="1276" name="redBagStatus">
      <Position>5</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1411" parent="1276" name="passwordStatus">
      <Position>6</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1412" parent="1276" name="roomName">
      <Position>7</Position>
      <DataType>String|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1413" parent="1276" name="roomTag">
      <Position>8</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1414" parent="1276" name="seatUserCount">
      <Position>9</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1415" parent="1276" name="remainSeatCount">
      <Position>10</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1416" parent="1276" name="roomDurationTime">
      <Position>11</Position>
      <DataType>UInt32|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1417" parent="1276" name="audienceCount">
      <Position>12</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1418" parent="1276" name="createdOn">
      <Position>13</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1419" parent="1277" name="userId">
      <Position>1</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1420" parent="1277" name="level">
      <Position>2</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1421" parent="1277" name="createdOn">
      <Position>3</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1422" parent="1278" name="statisticDate">
      <Position>1</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1423" parent="1278" name="roomNameType">
      <Position>2</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1424" parent="1278" name="roomNameCount">
      <Position>3</Position>
      <DataType>UInt32|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1425" parent="1278" name="roomNameDuration">
      <Position>4</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1426" parent="1278" name="showCount">
      <Position>5</Position>
      <DataType>UInt32|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1427" parent="1278" name="joinRoomCount">
      <Position>6</Position>
      <DataType>UInt32|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1428" parent="1278" name="stayTime">
      <Position>7</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1429" parent="1278" name="callUpShowCount">
      <Position>8</Position>
      <DataType>UInt32|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1430" parent="1278" name="joinCallUpRoomCount">
      <Position>9</Position>
      <DataType>UInt32|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1431" parent="1278" name="callUpStayTime">
      <Position>10</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1432" parent="1278" name="createdOn">
      <Position>11</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1433" parent="1279" name="turtleSoupCount">
      <Position>1</Position>
      <DataType>UInt32|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1434" parent="1279" name="knowledgeQuizCount">
      <Position>2</Position>
      <DataType>UInt32|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1435" parent="1279" name="createdOn">
      <Position>3</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1436" parent="1280" name="roomKey">
      <Position>1</Position>
      <DataType>String|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1437" parent="1280" name="tinyGameType">
      <Position>2</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1438" parent="1280" name="duration">
      <Position>3</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1439" parent="1280" name="createdOn">
      <Position>4</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1440" parent="1281" name="one">
      <Position>1</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1441" parent="1281" name="two">
      <Position>2</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1442" parent="1281" name="three">
      <Position>3</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1443" parent="1281" name="four">
      <Position>4</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1444" parent="1281" name="five">
      <Position>5</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1445" parent="1281" name="six">
      <Position>6</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1446" parent="1281" name="seven">
      <Position>7</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1447" parent="1281" name="eight">
      <Position>8</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1448" parent="1281" name="nine">
      <Position>9</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1449" parent="1281" name="ten">
      <Position>10</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1450" parent="1281" name="eleven">
      <Position>11</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1451" parent="1281" name="passwordRoomCount">
      <Position>12</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1452" parent="1281" name="notPasswordRoomCount">
      <Position>13</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1453" parent="1281" name="callUpRoomCount">
      <Position>14</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1454" parent="1281" name="notCallUpRoomCount">
      <Position>15</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1455" parent="1281" name="createdOn">
      <Position>16</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1456" parent="1282" name="newUserCount">
      <Position>1</Position>
      <DataType>UInt32|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1457" parent="1282" name="retentionNewUserCount">
      <Position>2</Position>
      <DataType>UInt32|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1458" parent="1282" name="activeUserCount">
      <Position>3</Position>
      <DataType>UInt32|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1459" parent="1282" name="retentionActiveUserCount">
      <Position>4</Position>
      <DataType>UInt32|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1460" parent="1282" name="date">
      <Position>5</Position>
      <DataType>Date|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1461" parent="1282" name="createdOn">
      <Position>6</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1462" parent="1283" name="userId">
      <Position>1</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1463" parent="1283" name="roomNum">
      <Position>2</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1464" parent="1283" name="gameRoomType">
      <Position>3</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1465" parent="1283" name="callUp">
      <Position>4</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1466" parent="1283" name="createdOn">
      <Position>5</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1467" parent="1284" name="userId">
      <Position>1</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1468" parent="1284" name="roomNum">
      <Position>2</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1469" parent="1284" name="memberType">
      <Position>3</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1470" parent="1284" name="purchaseType">
      <Position>4</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1471" parent="1284" name="roomType">
      <Position>5</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1472" parent="1284" name="createdOn">
      <Position>6</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1473" parent="1285" name="userId">
      <Position>1</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1474" parent="1285" name="roomNum">
      <Position>2</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1475" parent="1285" name="sceneType">
      <Position>3</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1476" parent="1285" name="content">
      <Position>4</Position>
      <DataType>String|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1477" parent="1285" name="createdOn">
      <Position>5</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1478" parent="1286" name="userId">
      <Position>1</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1479" parent="1286" name="roomNum">
      <Position>2</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1480" parent="1286" name="roomName">
      <Position>3</Position>
      <DataType>String|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1481" parent="1286" name="createdOn">
      <Position>4</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1482" parent="1287" name="conditionType">
      <Position>1</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1483" parent="1287" name="userId">
      <Position>2</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1484" parent="1287" name="amount">
      <Position>3</Position>
      <DataType>Decimal(18,3 digit)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1485" parent="1287" name="createdOn">
      <Position>4</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1486" parent="1288" name="userId">
      <Position>1</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1487" parent="1288" name="rejectPostStatus">
      <Position>2</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1488" parent="1288" name="changeDate">
      <Position>3</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1489" parent="1288" name="createdOn">
      <Position>4</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1490" parent="1289" name="userId">
      <Position>1</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1491" parent="1289" name="roomKey">
      <Position>2</Position>
      <DataType>String|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1492" parent="1289" name="playerNum">
      <Position>3</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1493" parent="1289" name="gameTime">
      <Position>4</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1494" parent="1289" name="type">
      <Position>5</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1495" parent="1289" name="createdOn">
      <Position>6</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1496" parent="1290" name="userId">
      <Position>1</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1497" parent="1290" name="roomKey">
      <Position>2</Position>
      <DataType>String|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1498" parent="1290" name="gameRoomType">
      <Position>3</Position>
      <DataType>Int8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>-1</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1499" parent="1290" name="joinRecordId">
      <Position>4</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1500" parent="1290" name="ownerId">
      <Position>5</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1501" parent="1290" name="ownerLv">
      <Position>6</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1502" parent="1290" name="ownerGameNum">
      <Position>7</Position>
      <DataType>UInt32|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1503" parent="1290" name="ownerWinRate">
      <Position>8</Position>
      <DataType>Decimal(5,2 digit)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1504" parent="1290" name="remainUserCount">
      <Position>9</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1505" parent="1290" name="createdOn">
      <Position>10</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1506" parent="1291" name="presentKey">
      <Position>1</Position>
      <DataType>String|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1507" parent="1291" name="presentConfigId">
      <Position>2</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1508" parent="1291" name="fromUserId">
      <Position>3</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1509" parent="1291" name="toUserId">
      <Position>4</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1510" parent="1291" name="sourceType">
      <Position>5</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1511" parent="1291" name="roomName">
      <Position>6</Position>
      <DataType>String|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1512" parent="1291" name="roomUserCount">
      <Position>7</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1513" parent="1291" name="fromUserRoleType">
      <Position>8</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1514" parent="1291" name="fromUserSeatNum">
      <Position>9</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1515" parent="1291" name="toUserRoleType">
      <Position>10</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1516" parent="1291" name="toUserSeatNum">
      <Position>11</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1517" parent="1291" name="roomNameType">
      <Position>12</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1518" parent="1291" name="duration">
      <Position>13</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1519" parent="1291" name="remainSeatCount">
      <Position>14</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1520" parent="1291" name="passwordStatus">
      <Position>15</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1521" parent="1291" name="createdOn">
      <Position>16</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1522" parent="1292" name="roomKey">
      <Position>1</Position>
      <DataType>String|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1523" parent="1292" name="gameRoomType">
      <Position>2</Position>
      <DataType>Int8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>-1</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1524" parent="1292" name="userId">
      <Position>3</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1525" parent="1292" name="joinRecordId">
      <Position>4</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1526" parent="1292" name="roomUserCount">
      <Position>5</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1527" parent="1292" name="roomRemainSeat">
      <Position>6</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1528" parent="1292" name="createdOn">
      <Position>7</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1529" parent="1293" name="activityId">
      <Position>1</Position>
      <DataType>Int16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1530" parent="1293" name="boxConfigId">
      <Position>2</Position>
      <DataType>Int16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1531" parent="1293" name="awardId">
      <Position>3</Position>
      <DataType>Int16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1532" parent="1293" name="awardType">
      <Position>4</Position>
      <DataType>Int8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1533" parent="1293" name="forever">
      <Position>5</Position>
      <DataType>Int8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1534" parent="1293" name="userId">
      <Position>6</Position>
      <DataType>Int64|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1535" parent="1293" name="awardCount">
      <Position>7</Position>
      <DataType>Int16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1536" parent="1293" name="createdOn">
      <Position>8</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1537" parent="1294" name="activityId">
      <Position>1</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1538" parent="1294" name="boxConfigId">
      <Position>2</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1539" parent="1294" name="userId">
      <Position>3</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1540" parent="1294" name="price">
      <Position>4</Position>
      <DataType>Decimal(10,2 digit)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0.</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1541" parent="1294" name="level">
      <Position>5</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1542" parent="1294" name="paymentSourceType">
      <Position>6</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1543" parent="1294" name="priceType">
      <Position>7</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1544" parent="1294" name="createdOn">
      <Position>8</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1545" parent="1295" name="gameUuid">
      <Position>1</Position>
      <DataType>String|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1546" parent="1295" name="gameNum">
      <Position>2</Position>
      <DataType>UInt32|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1547" parent="1295" name="userId">
      <Position>3</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1548" parent="1295" name="endTime">
      <Position>4</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1549" parent="1295" name="gameRoomType">
      <Position>5</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1550" parent="1295" name="resultContent">
      <Position>6</Position>
      <DataType>String|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1551" parent="1295" name="remainUserCount">
      <Position>7</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1552" parent="1295" name="startRecordId">
      <Position>8</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1553" parent="1295" name="gameOverRound">
      <Position>9</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1554" parent="1295" name="deathRound">
      <Position>10</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1555" parent="1295" name="voiceDuration">
      <Position>11</Position>
      <DataType>Decimal(10,2 digit)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0.</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1556" parent="1295" name="voiceDayCount">
      <Position>12</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1557" parent="1295" name="createdOn">
      <Position>13</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1558" parent="1296" name="joinRecordId">
      <Position>1</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1559" parent="1296" name="joinId">
      <Position>2</Position>
      <DataType>String|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1560" parent="1296" name="type">
      <Position>3</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1561" parent="1296" name="userId">
      <Position>4</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1562" parent="1296" name="level">
      <Position>5</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1563" parent="1296" name="waitTime">
      <Position>6</Position>
      <DataType>Int64|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1564" parent="1296" name="waitUserNum">
      <Position>7</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1565" parent="1296" name="gameRoomType">
      <Position>8</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1566" parent="1296" name="roomNum">
      <Position>9</Position>
      <DataType>UInt32|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1567" parent="1296" name="roomUserCount">
      <Position>10</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1568" parent="1296" name="roomRemainSeatCount">
      <Position>11</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1569" parent="1296" name="roomLevel">
      <Position>12</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1570" parent="1296" name="callUp">
      <Position>13</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1571" parent="1296" name="readyUserCount">
      <Position>14</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1572" parent="1296" name="latestGameEndTime">
      <Position>15</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1573" parent="1296" name="joinTime">
      <Position>16</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1574" parent="1296" name="joinSuccess">
      <Position>17</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1575" parent="1296" name="triggerSpecialType">
      <Position>18</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1576" parent="1296" name="triggerX">
      <Position>19</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1577" parent="1296" name="createdOn">
      <Position>20</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1578" parent="1297" name="matchId">
      <Position>1</Position>
      <DataType>String|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1579" parent="1297" name="userId">
      <Position>2</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1580" parent="1297" name="type">
      <Position>3</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1581" parent="1297" name="level">
      <Position>4</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1582" parent="1297" name="gameRoomType">
      <Position>5</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1583" parent="1297" name="inSpecial">
      <Position>6</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1584" parent="1297" name="specialLevel">
      <Position>7</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1585" parent="1297" name="specialCount">
      <Position>8</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1586" parent="1297" name="notSpecialCount">
      <Position>9</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1587" parent="1297" name="specialX">
      <Position>10</Position>
      <DataType>Int16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1588" parent="1297" name="notSpecialX">
      <Position>11</Position>
      <DataType>Int16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1589" parent="1297" name="waitTime">
      <Position>12</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1590" parent="1297" name="waitUserNum">
      <Position>13</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1591" parent="1297" name="operationTime">
      <Position>14</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1592" parent="1297" name="createdOn">
      <Position>15</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1593" parent="1298" name="roomKey">
      <Position>1</Position>
      <DataType>String|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1594" parent="1298" name="gameRoomType">
      <Position>2</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1595" parent="1298" name="hasPassword">
      <Position>3</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1596" parent="1298" name="callUpRoom">
      <Position>4</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1597" parent="1298" name="inMatch">
      <Position>5</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1598" parent="1298" name="limitLevel">
      <Position>6</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1599" parent="1298" name="roomUserCount">
      <Position>7</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1600" parent="1298" name="remainSeatCount">
      <Position>8</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1601" parent="1298" name="closeSeatCount">
      <Position>9</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1602" parent="1298" name="createdOn">
      <Position>10</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1603" parent="1299" name="gameRoomType">
      <Position>1</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1604" parent="1299" name="userId">
      <Position>2</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1605" parent="1299" name="type">
      <Position>3</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1606" parent="1299" name="createdOn">
      <Position>4</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1607" parent="1300" name="userId">
      <Position>1</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1608" parent="1300" name="gameType">
      <Position>2</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>1</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1609" parent="1300" name="gameRoomType">
      <Position>3</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1610" parent="1300" name="villagerVictories">
      <Position>4</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1611" parent="1300" name="werewolfVictories">
      <Position>5</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1612" parent="1300" name="godVictories">
      <Position>6</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1613" parent="1300" name="villagerDefeats">
      <Position>7</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1614" parent="1300" name="werewolfDefeats">
      <Position>8</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1615" parent="1300" name="godDefeats">
      <Position>9</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1616" parent="1300" name="createdOn">
      <Position>10</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1617" parent="1301" name="roomKey">
      <Position>1</Position>
      <DataType>String|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1618" parent="1301" name="ownerId">
      <Position>2</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1619" parent="1301" name="roomType">
      <Position>3</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1620" parent="1301" name="callupStatus">
      <Position>4</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1621" parent="1301" name="redBagStatus">
      <Position>5</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1622" parent="1301" name="passwordStatus">
      <Position>6</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1623" parent="1301" name="roomName">
      <Position>7</Position>
      <DataType>String|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1624" parent="1301" name="roomTag">
      <Position>8</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1625" parent="1301" name="seatUserCount">
      <Position>9</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1626" parent="1301" name="remainSeatCount">
      <Position>10</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1627" parent="1301" name="roomDurationTime">
      <Position>11</Position>
      <DataType>UInt32|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1628" parent="1301" name="audienceCount">
      <Position>12</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1629" parent="1301" name="createdOn">
      <Position>13</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1630" parent="1302" name="userId">
      <Position>1</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1631" parent="1302" name="level">
      <Position>2</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1632" parent="1302" name="createdOn">
      <Position>3</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1633" parent="1303" name="statisticDate">
      <Position>1</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1634" parent="1303" name="roomNameType">
      <Position>2</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1635" parent="1303" name="roomNameCount">
      <Position>3</Position>
      <DataType>UInt32|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1636" parent="1303" name="roomNameDuration">
      <Position>4</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1637" parent="1303" name="showCount">
      <Position>5</Position>
      <DataType>UInt32|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1638" parent="1303" name="joinRoomCount">
      <Position>6</Position>
      <DataType>UInt32|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1639" parent="1303" name="stayTime">
      <Position>7</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1640" parent="1303" name="callUpShowCount">
      <Position>8</Position>
      <DataType>UInt32|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1641" parent="1303" name="joinCallUpRoomCount">
      <Position>9</Position>
      <DataType>UInt32|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1642" parent="1303" name="callUpStayTime">
      <Position>10</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1643" parent="1303" name="createdOn">
      <Position>11</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1644" parent="1304" name="turtleSoupCount">
      <Position>1</Position>
      <DataType>UInt32|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1645" parent="1304" name="knowledgeQuizCount">
      <Position>2</Position>
      <DataType>UInt32|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1646" parent="1304" name="createdOn">
      <Position>3</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1647" parent="1305" name="roomKey">
      <Position>1</Position>
      <DataType>String|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1648" parent="1305" name="tinyGameType">
      <Position>2</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1649" parent="1305" name="duration">
      <Position>3</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1650" parent="1305" name="createdOn">
      <Position>4</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1651" parent="1306" name="one">
      <Position>1</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1652" parent="1306" name="two">
      <Position>2</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1653" parent="1306" name="three">
      <Position>3</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1654" parent="1306" name="four">
      <Position>4</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1655" parent="1306" name="five">
      <Position>5</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1656" parent="1306" name="six">
      <Position>6</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1657" parent="1306" name="seven">
      <Position>7</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1658" parent="1306" name="eight">
      <Position>8</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1659" parent="1306" name="nine">
      <Position>9</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1660" parent="1306" name="ten">
      <Position>10</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1661" parent="1306" name="eleven">
      <Position>11</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1662" parent="1306" name="passwordRoomCount">
      <Position>12</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1663" parent="1306" name="notPasswordRoomCount">
      <Position>13</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1664" parent="1306" name="callUpRoomCount">
      <Position>14</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1665" parent="1306" name="notCallUpRoomCount">
      <Position>15</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1666" parent="1306" name="createdOn">
      <Position>16</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1667" parent="1307" name="newUserCount">
      <Position>1</Position>
      <DataType>UInt32|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1668" parent="1307" name="retentionNewUserCount">
      <Position>2</Position>
      <DataType>UInt32|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1669" parent="1307" name="activeUserCount">
      <Position>3</Position>
      <DataType>UInt32|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1670" parent="1307" name="retentionActiveUserCount">
      <Position>4</Position>
      <DataType>UInt32|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1671" parent="1307" name="date">
      <Position>5</Position>
      <DataType>Date|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1672" parent="1307" name="createdOn">
      <Position>6</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1673" parent="1308" name="userId">
      <Position>1</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1674" parent="1308" name="roomNum">
      <Position>2</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1675" parent="1308" name="gameRoomType">
      <Position>3</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1676" parent="1308" name="callUp">
      <Position>4</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1677" parent="1308" name="createdOn">
      <Position>5</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1678" parent="1309" name="userId">
      <Position>1</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1679" parent="1309" name="roomNum">
      <Position>2</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1680" parent="1309" name="memberType">
      <Position>3</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1681" parent="1309" name="purchaseType">
      <Position>4</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1682" parent="1309" name="roomType">
      <Position>5</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1683" parent="1309" name="createdOn">
      <Position>6</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1684" parent="1310" name="userId">
      <Position>1</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1685" parent="1310" name="roomNum">
      <Position>2</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1686" parent="1310" name="sceneType">
      <Position>3</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1687" parent="1310" name="content">
      <Position>4</Position>
      <DataType>String|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1688" parent="1310" name="createdOn">
      <Position>5</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1689" parent="1311" name="userId">
      <Position>1</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1690" parent="1311" name="roomNum">
      <Position>2</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1691" parent="1311" name="roomName">
      <Position>3</Position>
      <DataType>String|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1692" parent="1311" name="createdOn">
      <Position>4</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1693" parent="1312" name="conditionType">
      <Position>1</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1694" parent="1312" name="userId">
      <Position>2</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1695" parent="1312" name="amount">
      <Position>3</Position>
      <DataType>Decimal(18,3 digit)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1696" parent="1312" name="createdOn">
      <Position>4</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1697" parent="1313" name="userId">
      <Position>1</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1698" parent="1313" name="rejectPostStatus">
      <Position>2</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1699" parent="1313" name="changeDate">
      <Position>3</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1700" parent="1313" name="createdOn">
      <Position>4</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1701" parent="1314" name="userId">
      <Position>1</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1702" parent="1314" name="roomKey">
      <Position>2</Position>
      <DataType>String|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1703" parent="1314" name="playerNum">
      <Position>3</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1704" parent="1314" name="gameTime">
      <Position>4</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1705" parent="1314" name="type">
      <Position>5</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1706" parent="1314" name="createdOn">
      <Position>6</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1707" parent="1315" name="userId">
      <Position>1</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1708" parent="1315" name="roomKey">
      <Position>2</Position>
      <DataType>String|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1709" parent="1315" name="gameRoomType">
      <Position>3</Position>
      <DataType>Int8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>-1</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1710" parent="1315" name="joinRecordId">
      <Position>4</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1711" parent="1315" name="ownerId">
      <Position>5</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1712" parent="1315" name="ownerLv">
      <Position>6</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1713" parent="1315" name="ownerGameNum">
      <Position>7</Position>
      <DataType>UInt32|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1714" parent="1315" name="ownerWinRate">
      <Position>8</Position>
      <DataType>Decimal(5,2 digit)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1715" parent="1315" name="remainUserCount">
      <Position>9</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1716" parent="1315" name="createdOn">
      <Position>10</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1717" parent="1316" name="presentKey">
      <Position>1</Position>
      <DataType>String|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1718" parent="1316" name="presentConfigId">
      <Position>2</Position>
      <DataType>UInt16|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1719" parent="1316" name="fromUserId">
      <Position>3</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1720" parent="1316" name="toUserId">
      <Position>4</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1721" parent="1316" name="sourceType">
      <Position>5</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1722" parent="1316" name="roomName">
      <Position>6</Position>
      <DataType>String|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1723" parent="1316" name="roomUserCount">
      <Position>7</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1724" parent="1316" name="fromUserRoleType">
      <Position>8</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1725" parent="1316" name="fromUserSeatNum">
      <Position>9</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1726" parent="1316" name="toUserRoleType">
      <Position>10</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1727" parent="1316" name="toUserSeatNum">
      <Position>11</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1728" parent="1316" name="roomNameType">
      <Position>12</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1729" parent="1316" name="duration">
      <Position>13</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1730" parent="1316" name="remainSeatCount">
      <Position>14</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1731" parent="1316" name="passwordStatus">
      <Position>15</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1732" parent="1316" name="createdOn">
      <Position>16</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1733" parent="1317" name="roomKey">
      <Position>1</Position>
      <DataType>String|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1734" parent="1317" name="gameRoomType">
      <Position>2</Position>
      <DataType>Int8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>-1</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1735" parent="1317" name="userId">
      <Position>3</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1736" parent="1317" name="joinRecordId">
      <Position>4</Position>
      <DataType>UInt64|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1737" parent="1317" name="roomUserCount">
      <Position>5</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1738" parent="1317" name="roomRemainSeat">
      <Position>6</Position>
      <DataType>UInt8|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="1739" parent="1317" name="createdOn">
      <Position>7</Position>
      <DataType>DateTime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
  </database-model>
</dataSource>