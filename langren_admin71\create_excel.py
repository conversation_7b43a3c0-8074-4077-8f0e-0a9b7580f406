import requests
import datetime
import openpyxl
import json

baseurl = {"qa1": "https://qa-rancher.langren001.com:90",  # qa1 URL
           "qa3": "https://lrqa.langren001.com:90"}  # qa3 URL


def getswaggar(qax):
    if qax == 'qa1':
        url = 'https://qa-rancher.langren001.com:90/v2/api-docs'
    else:
        url = 'https://lrqa.langren001.com:90/v2/api-docs'
    response = requests.get(url)
    return response.json()




def generate_excel(qax):
    api_descriptions=getswaggar(qax)['paths']
    # 获取当前时间并格式化为指定字符串
    t = datetime.datetime.now().strftime("%Y%m%d%H%M")
    file_name = f'{qax}_admin_test_{t}.xlsx'

    workbook = openpyxl.Workbook()
    sheet = workbook.active

    # 写入列名
    sheet['A1'] = '接口概述'
    sheet['B1'] = '接口URL'
    sheet['C1'] = '接口方法'
    sheet['D1'] = '接口参数'
    sheet['E1'] = '预期结果'
    sheet['F1']='是否测试'
    sheet['G1']='content-type'

    row_num = 2
    for path, methods in api_descriptions.items():
        for method, details in methods.items():
            method = method.upper()
            url = f"{baseurl[qax]}{path}"
            summary = details['summary']

            # 处理接口参数并转换为JSON格式
            params_json = {}
            if 'parameters' in details:
                for param in details['parameters']:
                    param_name = param['name']
                    # param_description = param['description']
                    # param_required = param['required']
                    params_json[param_name] = f"{param_name}"

            # 将参数JSON对象转换为字符串写入Excel
            params_str = json.dumps(params_json, indent=4)

            # 写入数据行
            sheet[f'A{row_num}'] = summary
            sheet[f'B{row_num}'] = url
            sheet[f'C{row_num}'] = method
            sheet[f'D{row_num}'] = params_str
            sheet[f'E{row_num}'] = ""  # 这里预期结果可根据实际情况进一步明确

            row_num += 1

    workbook.save(file_name)
    print(f"数据已成功写入{file_name}中。")


generate_excel('qa1')
# generate_excel('qa3')
