"""
<AUTHOR>
@date 2020/7/13 0013
"""
from typing import Dict, List, Tuple

import requests

from domain import GiftConfig


class HttpCall(object):

    def __init__(self, host: str, port: int = 80):
        self.host = host
        self.port = port
        self.session = requests.Session()

    def __str__(self):
        return f'HttpCall host: {self.host} port: {self.port}'

    def login(self, user_identity: str, access_token: str, device_id: str, user_type: int = 1, phone_type: int = 2,
              version: str = '2680') -> Tuple[int, str]:
        """
        登录
        :param user_identity:
        :param access_token:
        :param device_id:
        :param user_type:
        :param phone_type:
        :param version:
        :return: user_id im_token
        """
        data = {
            'userIdentity': user_identity,
            'accessToken': access_token,
            'deviceId': device_id,
            'userType': user_type,
            'phoneType': phone_type,
            'version': version,
        }
        resp = self.session.post(self.__assemble_url('securities/login'), data=data)
        j = resp.json()
        return j['user']['userBasicInfo']['userId'], j['imToken']

    def __assemble_url(self, path: str) -> str:
        return 'http://%s:%s/langren/%s' % (self.host, self.port, path)

    def get_gift_config(self, gift_id: int) -> GiftConfig:
        """
        获取礼物配置
        :param gift_id:
        :return:
        """
        resp = self.session.get(self.__assemble_url('h5/present/%s/config' % gift_id))
        result = resp.json()['result']
        return GiftConfig(result['presentConfigId'], result['presentName'], result['presentThumb'],
                          result['popularity'], result['amount'],
                          result['effect'])

    def get_balance(self) -> int:
        """
        获取金币余额
        :return:
        """
        resp = self.session.get(self.__assemble_url('account/balance'))
        return int(resp.json()['balance'])

    def get_vip_expire_time(self) -> int:
        """
        获取VIP到期时间
        :return: 单位ms 不是VIP返回0
        """
        resp = self.session.get(self.__assemble_url('users/vipInfo'))
        return int(resp.json()['vip']['expireDate'])

    def get_popularity(self, user_id: int) -> int:
        """
        获取人气
        :param user_id:
        :return:
        """
        resp = self.session.get(self.__assemble_url('users/%s/popularity' % user_id))
        return resp.json()['popularity']['popularity']

    def __get_decorations(self, url: str) -> Dict[str, int]:
        resp = self.session.get(self.__assemble_url(url))
        result = resp.json()['result']

        deco_list: List = result['activityDecorations']
        deco_list.extend(result['defaultDecorations'])

        decorations = {d['name']: -1 if d['forever'] else d['expireDate'] for d in deco_list}
        return decorations

    def get_decorations(self) -> Dict[str, int]:
        """
        获取装扮
        :return: -1 means forever
        """
        decorations = {}
        decorations.update(self.__get_decorations('h5/decoration/dynamic'))
        decorations.update(self.__get_decorations('h5/decoration/chatBubble'))
        decorations.update(self.__get_decorations('h5/decoration/headFrame'))
        decorations.update(self.__get_decorations('h5/decoration/microphone'))
        return decorations

    def get_stored_gifts(self) -> Dict[str, int]:
        """
        获取存量礼物配置
        :return:
        """
        resp = self.session.get(self.__assemble_url('h5/present/stored'))
        result = resp.json()['result']

        return {s['storedPresent']['presentName']: s['count'] for s in result}

    def get_props(self) -> Dict[str, int]:
        """
        获取道具卡配置
        :return:
        """
        resp = self.session.get(self.__assemble_url('h5/prop/userProps'))
        result = resp.json()['result']

        return {s['prop']['propName']: s['count'] for s in result}
