{"name": "狼人杀", "version": 1, "swaggerUrl": "http://lrqa.langren001.com/langren/v2/api-docs", "profiles": [{"name": "qa3", "script": "def init(ctx):\n  #APOLLO配置\n  ctx['$APOLLO_ENV']='UAT'\n  ctx['$APOLLO_CLUSTER']='qa3'\n  ctx['$APOLLO_PORTAL_URL']='************:8070'\n  ctx['$APOLLO_PORTAL_TOKEN']='93282e8a772a70d88038bf67d285dacda6c3a9d9'\n  \n  ctx['host']='http://lrqa.langren001.com/langren'\n\ndef before(ctx, test, req):\n  req.headers['ptype'] = '2'\n  req.headers['pcode'] = '2.9.6.0x'\n\ndef after(ctx, test, req, resp):\n  test.assertEqual(resp.status_code, 200)"}], "groups": [{"name": "登录", "requests": [{"uuid": "7ab0e351-a4df-7a36-3908-c462801f4713", "enabled": false, "name": "用户状态—login.code.switch=false", "method": "GET", "url": "${host}/securities/status", "params": [{"key": "areaId", "value": "0"}, {"key": "userIdentity", "value": "fa6b721872bf53f6dccf8b5716ea9fa7"}], "headers": [], "body": {"type": "none", "formdata": []}, "script": "def before(ctx, test, req):\n  fn.update_apollo_config('langren_user','langren.config','login.code.switch','false')\n  import time\n  time.sleep(1)\n\ndef after(ctx, test, req, resp):\n  result = resp.json()['result']['codeLoginSwitch']\n  test.assertFalse(result)"}, {"uuid": "24c9edae-407d-743c-6632-75e42bc1e9fe", "enabled": false, "name": "用户状态—login.code.switch = true", "method": "GET", "url": "${host}/securities/status", "params": [{"key": "areaId", "value": "0"}, {"key": "userIdentity", "value": "fa6b721872bf53f6dccf8b5716ea9fa7"}], "headers": [], "body": {"type": "none", "formdata": []}, "script": "def before(ctx, test, req):\n  fn.update_apollo_config('langren_user','langren.config','login.code.switch','true')\n  import time\n  time.sleep(1)\n\ndef after(ctx, test, req, resp):\n  result = resp.json()['result']['codeLoginSwitch']\n  test.assertTrue(result)"}, {"uuid": "8d0451ee-7fc0-4168-b267-a0cea651feba", "enabled": true, "name": "获取验证码", "method": "GET", "url": "${host}/securities/code", "params": [{"key": "areaId", "value": "0"}, {"key": "codeType", "value": "1"}, {"key": "codeVerificationType", "value": "6"}, {"key": "deviceId", "value": "811f443797d15aa8f2c1e058425ea93057ada855"}, {"key": "phoneNumber", "value": "18811111112"}, {"key": "version", "value": "2.9.6.0x"}], "headers": [], "body": {"type": "none", "formdata": []}, "script": "def before(ctx, test, req):\n  pass\n\ndef after(ctx, test, req, resp):\n  result = resp.json()['result']\n  test.assertTrue(result)"}, {"uuid": "05ebc5bb-4d85-2a38-d99c-c9edd2fe896c", "enabled": true, "name": "登录", "method": "POST", "url": "${host}/securities/login", "params": [], "headers": [], "body": {"type": "formdata", "formdata": [{"key": "accessToken", "value": "", "type": "text"}, {"key": "areaId", "value": "0", "type": "text"}, {"key": "autoType", "value": "0", "type": "text"}, {"key": "channel", "value": "appstore", "type": "text"}, {"key": "deviceId", "value": "811f443797d15aa8f2c1e058425ea93057ada855", "type": "text"}, {"key": "deviceType", "value": "iPhone 7", "type": "text"}, {"key": "idfa", "value": "6BE1AA9A-A15C-4DCD-A32D-D6431A68DF1D", "type": "text"}, {"key": "phoneType", "value": "2", "type": "text"}, {"key": "userIdentity", "value": "fa6b721872bf53f6dccf8b5716ea9fa7", "type": "text"}, {"key": "userType", "value": "1", "type": "text"}, {"key": "verificationCode", "value": "1234", "type": "text"}, {"key": "version", "value": "2.9.6.0x", "type": "text"}]}, "script": "def before(ctx, test, req):\n  pass\n\ndef after(ctx, test, req, resp):\n  test.assertIn('user',resp.json())"}, {"uuid": "136c50b3-5016-99ba-1dab-ccfc211a5183", "enabled": true, "name": "修改昵称", "method": "POST", "url": "${host}/users/nickName/setting", "params": [], "headers": [], "body": {"type": "formdata", "formdata": [{"key": "nick<PERSON><PERSON>", "value": "${newNickName}", "type": "text"}]}, "script": "def init(ctx, test):\n  ctx['newNickName'] = fn.random_str('昵称')\n\ndef before(ctx, test, req):\n  pass\n\ndef after(ctx, test, req, resp):\n  result = resp.json()['result']\n  test.assertTrue(ctx['newNickName'] == result)"}, {"uuid": "1fae585a-09f5-e140-bc86-bcc677524d1e", "enabled": false, "name": "修改昵称——空", "method": "POST", "url": "${host}/users/nickName/setting", "params": [], "headers": [], "body": {"type": "formdata", "formdata": [{"key": "nick<PERSON><PERSON>", "value": "", "type": "text"}]}, "script": "def before(ctx, test, req):\n  pass\n\ndef after(ctx, test, req, resp):\n  result = resp.json()['err_msg']\n  test.assertTrue(result == '昵称不能为空')"}, {"uuid": "9950195e-f4ee-46fd-a718-abfcd1636a3e", "enabled": false, "name": "修改昵称 ——纯空格", "method": "POST", "url": "${host}/users/nickName/setting", "params": [], "headers": [], "body": {"type": "formdata", "formdata": [{"key": "nick<PERSON><PERSON>", "value": "          ", "type": "text"}]}, "script": "def before(ctx, test, req):\n  pass\n\ndef after(ctx, test, req, resp):\n  result = resp.json()['err_msg']\n  test.assertTrue(result == '昵称不能为空')"}, {"uuid": "84b04968-ca61-0bd8-bd71-78cb5d72785b", "enabled": false, "name": "修改昵称 —10位", "method": "POST", "url": "${host}/users/nickName/setting", "params": [], "headers": [], "body": {"type": "formdata", "formdata": [{"key": "nick<PERSON><PERSON>", "value": "昵称最大长度不超过十", "type": "text"}]}, "script": "def before(ctx, test, req):\n  pass\n\ndef after(ctx, test, req, resp):\n  result = resp.json()['result']\n  test.assertTrue(result == req.body.formdata['nickName'])"}, {"uuid": "59637bef-5462-beb6-b595-39a676967a51", "enabled": false, "name": "修改昵称 —11位", "method": "POST", "url": "${host}/users/nickName/setting", "params": [], "headers": [], "body": {"type": "formdata", "formdata": [{"key": "nick<PERSON><PERSON>", "value": "昵称最大长度不超过十超", "type": "text"}]}, "script": "def before(ctx, test, req):\n  pass\n\ndef after(ctx, test, req, resp):\n  result = resp.json()['err_msg']\n  test.assertTrue(result == '昵称不能超过10位')"}]}, {"name": "房间", "requests": [{"uuid": "a9991410-926b-60e8-47b8-5fc2738f1411", "enabled": true, "name": "进入房间", "method": "POST", "url": "${host}/room/quickStart", "params": [], "headers": [], "body": {"type": "formdata", "formdata": [{"key": "gameRoomType", "value": "0", "type": "text"}]}, "script": "def before(ctx, test, req):\n  pass\n\ndef after(ctx, test, req, resp):\n  room = resp.json()['room']['roomKey']\n  ctx['roomkey'] = room"}, {"uuid": "633737e0-7ad4-aab6-c1f0-3f18b6588b04", "enabled": true, "name": "准备", "method": "POST", "url": "${host}/room/${roomkey}/ready/1", "params": [], "headers": [], "body": {"type": "none", "formdata": []}, "script": "def before(ctx, test, req):\n  pass\n\ndef after(ctx, test, req, resp):\n  pass"}]}]}