"""
<AUTHOR>
@date 2020/6/18 0018
"""
import time
from typing import List, TypeVar, Optional, Tuple

from biz.game.domain import GameContext
from biz.game.engine import Engine, on_state, Stateful
from role.game_role import Witch, GameR<PERSON>, Werewolf
from util.assert_tool import assert_is_true
from util.log import get_logger

T = TypeVar('T')

logger = get_logger("Game")


class Day(Stateful):
    class DayState(object):
        DAY_INITIAL = 'day_initial'
        SPEAK = 'speak'
        VOTE = 'vote'
        DAY_END = 'day_end'
        LAST_WORDS = 'last_words'
        WEREWOLF_WIN = 'werewolf_win'
        MAN_WIN = 'man_win'

    class DayEvent(object):
        DAYBREAK = 'daybreak'
        DAY_START = 'day_start'
        SPEAK_END = 'speak_end'
        ALL_SPEAK_END = 'all_speak_end'
        VOTE_KILLED = 'vote_killed'
        VOTE_NOT_KILLED = 'vote_not_killed'
        LAST_WORDS_END = 'last_words_end'
        ALL_WEREWOLF_DIED = 'all_werewolf_died'
        ALL_MAN_DIED = 'all_man_died'
        TIMEOUT = 'timeout'

    def define_state(self, engine: Engine):
        engine.define(Day.DayState.DAY_INITIAL, Day.DayEvent.ALL_WEREWOLF_DIED, Day.DayState.MAN_WIN)
        engine.define(Day.DayState.DAY_INITIAL, Day.DayEvent.ALL_MAN_DIED, Day.DayState.WEREWOLF_WIN)
        engine.define(Day.DayState.DAY_INITIAL, Day.DayEvent.DAY_START, Day.DayState.SPEAK)
        engine.define(Day.DayState.SPEAK, Day.DayEvent.SPEAK_END, Day.DayState.SPEAK)
        engine.define(Day.DayState.SPEAK, Day.DayEvent.ALL_SPEAK_END, Day.DayState.VOTE)
        engine.define(Day.DayState.VOTE, Day.DayEvent.VOTE_KILLED, Day.DayState.LAST_WORDS)
        engine.define(Day.DayState.VOTE, Day.DayEvent.VOTE_NOT_KILLED, Day.DayState.DAY_END)
        engine.define(Day.DayState.VOTE, Day.DayEvent.TIMEOUT, Day.DayState.DAY_END)
        engine.define(Day.DayState.VOTE, Day.DayEvent.ALL_WEREWOLF_DIED, Day.DayState.MAN_WIN)
        engine.define(Day.DayState.VOTE, Day.DayEvent.ALL_MAN_DIED, Day.DayState.WEREWOLF_WIN)
        engine.define(Day.DayState.LAST_WORDS, Day.DayEvent.LAST_WORDS_END, Night.NightState.NIGHT_INITIAL)
        engine.define(Day.DayState.DAY_END, Day.DayEvent.TIMEOUT, Night.NightState.NIGHT_INITIAL)

    # 天亮
    @on_state(DayState.DAY_INITIAL)
    def day_initial(self, engine: Engine[GameContext], state_id: int):
        ctx = engine.context
        ctx.current_turn += 1

        time.sleep(1)
        if self.trigger_if_game_end(engine, state_id):
            return

        death_seat_nums = ctx.infer_death_seat_nums()
        ctx.death_seat_nums.extend(death_seat_nums)

        # 断言昨晚被杀
        ap = ctx.action_provider
        case_asserts = ap.asserts
        m = 'last_night_death'
        if m in case_asserts:
            args = case_asserts[m]
            assert_name = '[%s] -> %s[%s]' % (ctx.status(), m, args)
            if args == '':
                assert_is_true(len(death_seat_nums) == 0, assert_name)
            else:
                predict_death_seat_nums: List[int] = args
                assert_is_true(all(sn in predict_death_seat_nums for sn in death_seat_nums) and all(
                    sn in death_seat_nums for sn in predict_death_seat_nums), assert_name)

        engine.trigger(state_id, Day.DayEvent.DAY_START)

    # 判断游戏是否已经结束
    @staticmethod
    def trigger_if_game_end(engine: Engine[GameContext], state_id: int) -> bool:
        ctx = engine.context
        time.sleep(1)

        # 有时候游戏结果弹框弹得慢，多视角检查，有一个弹出来就行
        def infer_is_game_end(sn: int) -> Optional[Tuple[bool, int]]:
            e, w = ctx.proxy(GameRole, sn).infer_is_game_end()
            if e:
                return True, w

        result = ctx.thread_tool.run_race(infer_is_game_end, [(sn,) for sn in ctx.seat_nums])
        if result is None:
            return False

        end, win_sn = result
        if not end:
            return False

        werewolf_win = int(win_sn) in ctx.werewolf_seat_nums
        # 断言获胜方
        case_asserts = ctx.action_provider.asserts
        m = 'win'
        if m in case_asserts:
            args = case_asserts[m]
            assert_name = '[%s] -> %s[%s]' % (ctx.status(), m, args)
            assert_is_true((not werewolf_win and args == 'man') or (
                    werewolf_win and args == 'werewolf'), assert_name)

        if werewolf_win:
            engine.trigger(state_id, Day.DayEvent.ALL_MAN_DIED)
        else:
            engine.trigger(state_id, Day.DayEvent.ALL_WEREWOLF_DIED)
        return True

    # 发言
    @on_state(DayState.SPEAK)
    def speak(self, engine: Engine[GameContext], state_id: int):
        ctx = engine.context

        sp = ctx.seat_profiles
        turn: int = ctx.current_turn
        ctx.turn_current_speaker.setdefault(turn, -1)
        ctx.turn_spoken_speakers.setdefault(turn, [])

        evt = Day.DayEvent.SPEAK_END

        spoken_speakers = ctx.turn_spoken_speakers[turn]
        if len(spoken_speakers) + 1 >= len(ctx.get_alive_seat_nums()):
            evt = Day.DayEvent.ALL_SPEAK_END

        # 45秒后超时
        ctx.thread_tool.run_scheduled(lambda: engine.trigger(state_id, evt), (), 45)
        previous_speaker = -1
        if len(spoken_speakers) > 0:
            previous_speaker = spoken_speakers[-1]

        # 发言
        speaker = ctx.infer_current_speaker(previous_speaker)
        spoken_speakers.append(speaker)

        case_speak_end = ctx.action_provider.speak_end
        if speaker in case_speak_end:
            sp[speaker].rpc_client.get_proxy(GameRole).speak_end()
            engine.trigger(state_id, evt)

    # 投票
    @on_state(DayState.VOTE)
    def vote(self, engine: Engine[GameContext], state_id: int):
        ctx = engine.context

        ap = ctx.action_provider
        case_vote_select = ap.vote_select
        case_vote_confirm = ap.vote_confirm
        case_vote_cancel = ap.vote_cancel

        def handle_vote_result():
            if engine.state_id != state_id:
                return

            if self.trigger_if_game_end(engine, state_id):
                return
            sn = ctx.infer_vote_kill_seat_num()
            ctx.death_seat_nums.append(sn)
            ctx.turn_vote_kill_seat_nums[ctx.current_turn] = sn

            # 断言投票处决
            case_asserts = ap.asserts
            m = 'vote_death'
            if m in case_asserts:
                args = case_asserts[m]
                assert_name = '[%s] -> %s[%s]' % (ctx.status(), m, args)
                assert_sn = args
                assert_is_true((args == '' and sn == -1) or (args != '' and sn == assert_sn), assert_name)

            if sn != -1:
                engine.trigger(state_id, Day.DayEvent.VOTE_KILLED)
            else:
                engine.trigger(state_id, Day.DayEvent.VOTE_NOT_KILLED)

        # 有人放弃或者不操作，超时后处理
        wait_to_timeout = any(
            sn not in set(case_vote_select).intersection(set(case_vote_confirm)) for sn in ctx.get_alive_seat_nums())
        if wait_to_timeout:
            ctx.thread_tool.run_scheduled(handle_vote_result, (), 15)
        # 投票选择
        if len(case_vote_select) > 0:
            ctx.ensure_no_deaths(list(case_vote_select.keys()))
            ctx.ensure_no_deaths(list(case_vote_select.values()))
            ctx.thread_tool.run_parallel(lambda sn, select_sn: ctx.proxy(GameRole, sn).vote_select(select_sn),
                                         [(sn, case_vote_select[sn]) for sn in case_vote_select])
        # 确认投票
        if len(case_vote_confirm) > 0:
            ctx.ensure_no_deaths(case_vote_confirm)
            ctx.thread_tool.run_parallel(lambda sn: ctx.proxy(GameRole, sn).vote_confirm(),
                                         [(sn,) for sn in case_vote_confirm])
        # 放弃投票
        if len(case_vote_cancel) > 0:
            ctx.ensure_no_deaths(case_vote_cancel)
            ctx.thread_tool.run_parallel(lambda sn: ctx.proxy(GameRole, sn).vote_cancel(),
                                         [(sn,) for sn in case_vote_cancel])
        if not wait_to_timeout:
            handle_vote_result()

    @on_state(DayState.DAY_END)
    def day_end(self, engine: Engine[GameContext], state_id: int):
        engine.context.thread_tool.run_scheduled(lambda: engine.trigger(state_id, Day.DayEvent.TIMEOUT), (), 15)

    # 遗言
    @on_state(DayState.LAST_WORDS)
    def last_words(self, engine: Engine[GameContext], state_id: int):
        ctx = engine.context
        ctx.thread_tool.run_scheduled(lambda: engine.trigger(state_id, Day.DayEvent.LAST_WORDS_END), (), 30)

        sn = ctx.turn_vote_kill_seat_nums[ctx.current_turn]
        if sn == -1:
            # 走到此流程，预期应该有人被处决
            logger.error('expect last_words_seat_num')
            return

        case_last_words_end = ctx.action_provider.last_words_end
        if case_last_words_end is not None:
            assert_name = '[%s] -> %s[%s]' % (ctx.status(), 'last words seat num match', case_last_words_end)
            assert_is_true(case_last_words_end == sn, assert_name)
            ctx.proxy(GameRole, sn).say_last_words()
            engine.trigger(engine.state_id, Day.DayEvent.LAST_WORDS_END)


class Night(Stateful):
    class NightState(object):
        NIGHT_INITIAL = 'night_initial'
        WEREWOLF_KILL = 'werewolf_kill'
        WITCH_RESCUE = 'witch_rescue'
        WITCH_POISON = 'witch_poison'
        NIGHT_END = 'night_end'

    class NightEvent(object):
        NIGHT_START = 'night_start'
        WEREWOLF_KILLED = 'werewolf_killed'
        WEREWOLF_NOT_KILLED = 'werewolf_not_killed'
        WITCH_DIED = 'witch_died'
        WITCH_RESCUED = 'witch_rescued'
        WITCH_NOT_RESCUED = 'witch_not_rescued'
        WITCH_NO_RESCUE = 'witch_no_rescue'
        WITCH_USED_POISON = 'witch_used_poison'
        WITCH_NOT_USED_POISON = 'witch_not_used_poison'
        WITCH_NO_POISON = 'witch_no_poison'
        TIMEOUT = 'timeout'

    def define_state(self, engine: Engine):
        engine.define(Night.NightState.NIGHT_INITIAL, Night.NightEvent.NIGHT_START, Night.NightState.WEREWOLF_KILL)
        engine.define(Night.NightState.WEREWOLF_KILL, Night.NightEvent.WEREWOLF_KILLED, Night.NightState.WITCH_RESCUE)
        engine.define(Night.NightState.WEREWOLF_KILL, Night.NightEvent.WEREWOLF_NOT_KILLED,
                      Night.NightState.WITCH_POISON)
        engine.define(Night.NightState.WEREWOLF_KILL, Night.NightEvent.TIMEOUT,
                      Night.NightState.WITCH_POISON)
        engine.define(Night.NightState.WITCH_RESCUE, Night.NightEvent.WITCH_DIED, Night.NightState.NIGHT_END)
        engine.define(Night.NightState.WITCH_RESCUE, Night.NightEvent.WITCH_RESCUED, Night.NightState.NIGHT_END)
        engine.define(Night.NightState.WITCH_RESCUE, Night.NightEvent.WITCH_NOT_RESCUED, Night.NightState.WITCH_POISON)
        engine.define(Night.NightState.WITCH_RESCUE, Night.NightEvent.WITCH_NO_RESCUE, Night.NightState.WITCH_POISON)
        engine.define(Night.NightState.WITCH_RESCUE, Night.NightEvent.TIMEOUT, Night.NightState.WITCH_POISON)
        engine.define(Night.NightState.WITCH_POISON, Night.NightEvent.WITCH_USED_POISON, Night.NightState.NIGHT_END)
        engine.define(Night.NightState.WITCH_POISON, Night.NightEvent.WITCH_NOT_USED_POISON, Night.NightState.NIGHT_END)
        engine.define(Night.NightState.WITCH_POISON, Night.NightEvent.WITCH_NO_POISON, Night.NightState.NIGHT_END)
        engine.define(Night.NightState.WITCH_POISON, Night.NightEvent.TIMEOUT, Night.NightState.NIGHT_END)
        engine.define(Night.NightState.WITCH_POISON, Night.NightEvent.WITCH_DIED, Night.NightState.NIGHT_END)
        engine.define(Night.NightState.NIGHT_END, Day.DayEvent.DAYBREAK, Day.DayState.DAY_INITIAL)

    # 天黑
    @on_state(NightState.NIGHT_INITIAL)
    def night_initial(self, engine: Engine[GameContext], state_id: int):
        ctx = engine.context

        if ctx.current_turn == 1:
            ctx.proxy(GameRole).wait_game_start()
            ctx.start_poll_msg()

        ctx.turn_night_start_time[ctx.current_turn] = time.time()
        engine.trigger(state_id, Night.NightEvent.NIGHT_START)

    # 杀人
    @on_state(NightState.WEREWOLF_KILL)
    def werewolf_kill(self, engine: Engine[GameContext], state_id: int):
        ctx = engine.context
        case_kill = ctx.action_provider.kill_seat_nums
        if len(case_kill) == 0:
            # 20秒后超时
            ctx.thread_tool.run_scheduled(lambda: engine.trigger(state_id, Night.NightEvent.TIMEOUT), (), 20)
            return

        ctx.ensure_no_deaths(list(case_kill.keys()))
        ctx.ensure_no_deaths(list(case_kill.values()))
        # 如果所有狼人意见一致，立即结束
        same_target = True
        select_sn: Optional[int] = None
        for alive_sn in ctx.get_alive_werewolf():
            if alive_sn not in case_kill:
                same_target = False
                break
            if select_sn is None:
                select_sn = case_kill[alive_sn]
            else:
                if select_sn != case_kill[alive_sn]:
                    same_target = False
                    break
        if not same_target:
            ctx.thread_tool.run_scheduled(lambda: engine.trigger(state_id, Night.NightEvent.WEREWOLF_KILLED), (), 20)
        ctx.thread_tool.run_parallel(
            lambda sn, kill_sn: ctx.proxy(Werewolf, sn).kill_select(kill_sn),
            [(sn, case_kill[sn]) for sn in case_kill])
        if same_target:
            engine.trigger(state_id, Night.NightEvent.WEREWOLF_KILLED)

    # 救人
    @on_state(NightState.WITCH_RESCUE)
    def witch_rescue(self, engine: Engine[GameContext], state_id: int):
        ctx = engine.context
        # 女巫已死
        if ctx.is_witch_died():
            engine.trigger(state_id, Night.NightEvent.WITCH_DIED)
            return
        # 解药已经用过
        if ctx.witch_rescue_used:
            engine.trigger(state_id, Night.NightEvent.WITCH_NO_RESCUE)
            return

        # 10秒后超时
        ctx.thread_tool.run_scheduled(lambda: engine.trigger(state_id, Night.NightEvent.TIMEOUT), (), 10)

        ap = ctx.action_provider
        witch = ctx.proxy(Witch, ctx.witch_seat_num)
        # 确认救
        if ap.rescue_confirm is not None:
            witch.rescue_confirm()
            ctx.witch_rescue_used = True
            ctx.witch_rescue_used_turn = ctx.current_turn
            engine.trigger(state_id, Night.NightEvent.WITCH_RESCUED)
        # 放弃救
        if ap.rescue_cancel is not None:
            witch.rescue_cancel()
            engine.trigger(state_id, Night.NightEvent.WITCH_NOT_RESCUED)

    # 下毒
    @on_state(NightState.WITCH_POISON)
    def witch_poison(self, engine: Engine[GameContext], state_id: int):
        ctx = engine.context

        # 女巫已死
        if ctx.is_witch_died():
            engine.trigger(state_id, Night.NightEvent.WITCH_DIED)
            return
        # 毒药已经用过
        if ctx.witch_poison_used:
            engine.trigger(state_id, Night.NightEvent.WITCH_NO_POISON)
            return

        # 10秒后超时
        ctx.thread_tool.run_scheduled(lambda: engine.trigger(state_id, Night.NightEvent.TIMEOUT), (), 10)

        ap = ctx.action_provider
        witch = ctx.proxy(Witch, ctx.witch_seat_num)
        selected = False
        # 选择毒
        if ap.poison_select is not None:
            sn, target = ap.poison_select
            ctx.ensure_no_deaths([target])
            witch.poison_select(target)
            selected = True
        # 确认毒
        if ap.poison_confirm is not None:
            witch.poison_confirm()
            # 选择了才能成功毒
            if selected:
                ctx.witch_poison_used = True
                engine.trigger(state_id, Night.NightEvent.WITCH_USED_POISON)
        # 放弃毒
        if ap.poison_cancel is not None:
            witch.poison_cancel()
            engine.trigger(state_id, Night.NightEvent.WITCH_NOT_USED_POISON)

    @on_state(NightState.NIGHT_END)
    def night_end(self, engine: Engine[GameContext], state_id: int):
        ctx = engine.context
        # 至少30秒后天亮
        night_countdown = 30
        time_to_day_break = night_countdown - (time.time() - ctx.turn_night_start_time[ctx.current_turn])

        # 进入下一个白天
        if time_to_day_break <= 0:
            engine.trigger(engine.state_id, Day.DayEvent.DAYBREAK)
        else:
            ctx.thread_tool.run_scheduled(lambda: engine.trigger(engine.state_id, Day.DayEvent.DAYBREAK), (),
                                          time_to_day_break)
