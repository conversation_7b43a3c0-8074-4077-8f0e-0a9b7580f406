<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="08b5e37c-c1af-4836-8eb9-68ca65ae1201" name="Default Changelist" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ProjectId" id="2IoC36ypsF3uMw3upJtr2XdLwKv" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showExcludedFiles" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">
    <property name="RunOnceActivity.ShowReadmeOnStart" value="true" />
    <property name="WebServerToolWindowFactoryState" value="false" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$/.." />
  </component>
  <component name="SvnConfiguration">
    <configuration />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="08b5e37c-c1af-4836-8eb9-68ca65ae1201" name="Default Changelist" comment="" />
      <created>1670838346349</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1670838346349</updated>
      <workItem from="1670838348163" duration="20000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="1" />
  </component>
  <component name="WindowStateProjectService">
    <state x="788" y="415" key="FileChooserDialogImpl" timestamp="1670838368281">
      <screen x="0" y="0" width="1920" height="1040" />
    </state>
    <state x="788" y="415" key="FileChooserDialogImpl/0.0.1920.1040@0.0.1920.1040" timestamp="1670838368281" />
  </component>
</project>