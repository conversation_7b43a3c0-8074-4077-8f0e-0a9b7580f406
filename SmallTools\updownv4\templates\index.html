<!DOCTYPE html>
<html>

<head>
    <title>文件上传系统</title>
    <style>
        .qr-code {
            max-width: 150px;
            /* 控制二维码最大宽度 */
            height: auto;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .section {
            margin-bottom: 30px;
        }

        #uploadResult {
            margin-top: 10px;
            color: green;
        }

        /* 进度条样式 */
        .progress-container {
            width: 100%;
            background-color: #f0f0f0;
            border-radius: 10px;
            margin: 10px 0;
            display: none;
        }

        .progress-bar {
            height: 20px;
            background-color: #4CAF50;
            border-radius: 10px;
            text-align: center;
            line-height: 20px;
            color: white;
            font-size: 12px;
            width: 0%;
            transition: width 0.3s ease;
        }

        .upload-status {
            margin-top: 5px;
            font-size: 14px;
            color: #666;
        }

        /* 添加新的样式 */
        .file-input {
            padding: 15px;
            font-size: 24px;
            width: 400px;
            margin-right: 15px;
            border: 2px solid #ddd;
            border-radius: 6px;
        }

        .upload-btn {
            padding: 18px 36px;
            font-size: 24px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            min-width: 120px;
        }

        .upload-btn:hover {
            background-color: #45a049;
            transform: scale(1.02);
            transition: all 0.2s;
        }

        .upload-form {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .qr-section {
            display: flex;
            align-items: flex-start;
            gap: 20px;
        }

        .text-input-area {
            flex-grow: 1;
        }

        .text-input {
            width: 100%;
            height: 100px;
            padding: 10px;
            font-size: 16px;
            border: 2px solid #ddd;
            border-radius: 6px;
            resize: vertical;
        }

        .save-text-btn {
            padding: 12px 24px;
            font-size: 16px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            margin-top: 10px;
        }

        .save-text-btn:hover {
            background-color: #45a049;
        }

        #textSaveResult {
            margin-top: 10px;
            color: green;
        }

        /* 修改文本区域样式 */
        .text-transfer-section {
            margin: 20px 0;
        }

        .text-area {
            width: 100%;
            height: 150px;
            padding: 10px;
            font-size: 16px;
            border: 2px solid #ddd;
            border-radius: 6px;
            resize: vertical;
            margin-bottom: 10px;
        }

        .button-group {
            display: flex;
            gap: 10px;
        }

        .refresh-btn,
        .submit-btn {
            padding: 12px 24px;
            font-size: 16px;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
        }

        .refresh-btn {
            background-color: #2196F3;
        }

        .refresh-btn:hover {
            background-color: #1976D2;
        }

        .submit-btn {
            background-color: #4CAF50;
        }

        .submit-btn:hover {
            background-color: #45a049;
        }

        /* 添加复制按钮样式 */
        .copy-btn {
            padding: 12px 24px;
            font-size: 16px;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            background-color: #9C27B0;
            /* 紫色，区分其他按钮 */
        }

        .copy-btn:hover {
            background-color: #7B1FA2;
        }

        /* 修改标题栏样式 */
        .section-header {
            display: flex;
            align-items: center;
            gap: 10px;
            /* 控制标题和按钮之间的间距 */
            margin-bottom: 10px;
        }

        .clear-btn {
            padding: 8px 16px;
            font-size: 14px;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            background-color: #FF5722;
        }

        .clear-btn:hover {
            background-color: #F4511E;
        }

        /* 添加URL二维码相关样式 */
        .url-qr-section {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
            align-items: flex-start;
        }

        .url-input {
            flex-grow: 1;
            padding: 10px;
            font-size: 16px;
            border: 2px solid #ddd;
            border-radius: 6px;
        }

        .qr-btn {
            padding: 12px 24px;
            font-size: 16px;
            background-color: #2196F3;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
        }

        .qr-btn:hover {
            background-color: #1976D2;
        }

        .clear-url-btn {
            padding: 12px 24px;
            font-size: 16px;
            background-color: #FF5722;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
        }

        .clear-url-btn:hover {
            background-color: #F4511E;
        }

        #qrResult {
            margin-top: 10px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        #qrResult img {
            max-width: 150px;
            height: auto;
            margin-top: 10px;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="section">
            <h2>扫描二维码访问</h2>
            <div class="qr-section">
                <img src="{{ url_for('generate_qr', data=access_url) }}" alt="QR Code" class="qr-code">
                <div class="text-input-area">
                    <textarea class="text-input" id="textInput" placeholder="在此输入文本内容..."></textarea>
                    <button onclick="saveText()" class="save-text-btn">保存为文本文件</button>
                    <div id="textSaveResult"></div>
                </div>
            </div>
        </div>

        <div class="section">
            <!-- 添加URL二维码生成功能 -->
            <div class="url-qr-section">
                <input type="text" id="urlInput" class="url-input" placeholder="输入URL生成二维码...">
                <button onclick="generateQR()" class="qr-btn">生成二维码</button>
                <button onclick="clearURL()" class="clear-url-btn">清空URL</button>
                <div id="qrResult"></div>
            </div>
            <div class="section-header">
                <h2>文本同步</h2>
                <button onclick="clearText()" class="clear-btn">清空并提交</button>
            </div>
            <div class="text-transfer-section">
                <textarea class="text-area" id="textArea" placeholder="在此输入文本..."></textarea>
                <div class="button-group">
                    <button onclick="refreshText()" class="refresh-btn">刷新</button>
                    <button onclick="submitText()" class="submit-btn">提交</button>
                    <button onclick="copyText()" class="copy-btn">复制</button>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>上传文件</h2>
            <form id="uploadForm" enctype="multipart/form-data" class="upload-form">
                <input type="file" name="files[]" multiple class="file-input" id="fileInput">
                <input type="button" value="上传" onclick="uploadFiles()" class="upload-btn" id="uploadBtn">
            </form>
            <div class="progress-container" id="progressContainer">
                <div class="progress-bar" id="progressBar">0%</div>
            </div>
            <div class="upload-status" id="uploadStatus"></div>
            <div id="uploadResult"></div>
        </div>

        <div class="section">
            <div style="display: flex; align-items: center; gap: 20px; margin-bottom: 10px;">
                <h2 style="margin: 0;">已上传文件列表</h2>
                <span style="font-size: 14px; color: #666;">PC本地目录：{{ upload_folder }}</span>
            </div>
            <div>
                {% for file in files %}
                <div>
                    <a href="{{ url_for('download_file', filename=file) }}">{{ file }}</a>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <script>
        function uploadFiles() {
            const fileInput = document.getElementById('fileInput');
            const uploadBtn = document.getElementById('uploadBtn');
            const progressContainer = document.getElementById('progressContainer');
            const progressBar = document.getElementById('progressBar');
            const uploadStatus = document.getElementById('uploadStatus');
            const uploadResult = document.getElementById('uploadResult');

            // 检查是否选择了文件
            if (!fileInput.files || fileInput.files.length === 0) {
                uploadResult.innerHTML = '请选择要上传的文件！';
                uploadResult.style.color = 'red';
                return;
            }

            // 重置状态
            uploadResult.innerHTML = '';
            uploadResult.style.color = 'green';

            // 显示进度条和状态
            progressContainer.style.display = 'block';
            progressBar.style.width = '0%';
            progressBar.textContent = '0%';
            uploadStatus.textContent = '准备上传...';

            // 禁用上传按钮
            uploadBtn.disabled = true;
            uploadBtn.textContent = '上传中...';
            uploadBtn.style.backgroundColor = '#ccc';

            const form = document.getElementById('uploadForm');
            const formData = new FormData(form);

            // 使用XMLHttpRequest来支持进度监控
            const xhr = new XMLHttpRequest();

            // 监听上传进度
            xhr.upload.addEventListener('progress', function (e) {
                if (e.lengthComputable) {
                    const percentComplete = Math.round((e.loaded / e.total) * 100);
                    progressBar.style.width = percentComplete + '%';
                    progressBar.textContent = percentComplete + '%';

                    if (percentComplete < 100) {
                        uploadStatus.textContent = `正在上传... ${percentComplete}%`;
                    } else {
                        uploadStatus.textContent = '上传完成，正在处理...';
                    }
                }
            });

            // 监听请求完成
            xhr.addEventListener('load', function () {
                if (xhr.status === 200) {
                    uploadResult.innerHTML = xhr.responseText;
                    uploadStatus.textContent = '上传成功！';
                    progressBar.style.backgroundColor = '#4CAF50';

                    // 清空文件选择
                    fileInput.value = '';

                    // 刷新页面以更新文件列表
                    setTimeout(() => location.reload(), 1500);
                } else {
                    uploadResult.innerHTML = '上传失败！';
                    uploadResult.style.color = 'red';
                    uploadStatus.textContent = '上传失败';
                    progressBar.style.backgroundColor = '#f44336';
                }

                // 恢复上传按钮
                setTimeout(() => {
                    uploadBtn.disabled = false;
                    uploadBtn.textContent = '上传';
                    uploadBtn.style.backgroundColor = '#4CAF50';
                    progressContainer.style.display = 'none';
                    uploadStatus.textContent = '';
                }, 2000);
            });

            // 监听请求错误
            xhr.addEventListener('error', function () {
                uploadResult.innerHTML = '上传失败！网络错误';
                uploadResult.style.color = 'red';
                uploadStatus.textContent = '网络错误';
                progressBar.style.backgroundColor = '#f44336';

                // 恢复上传按钮
                uploadBtn.disabled = false;
                uploadBtn.textContent = '上传';
                uploadBtn.style.backgroundColor = '#4CAF50';
                setTimeout(() => {
                    progressContainer.style.display = 'none';
                    uploadStatus.textContent = '';
                }, 2000);
            });

            // 发送请求
            xhr.open('POST', '/upload');
            xhr.send(formData);
        }

        function saveText() {
            var text = document.getElementById('textInput').value;
            if (!text) {
                document.getElementById('textSaveResult').innerHTML = '请输入文本内容';
                return;
            }

            var formData = new FormData();
            formData.append('text', text);

            fetch('/save-text', {
                method: 'POST',
                body: formData
            })
                .then(response => response.text())
                .then(result => {
                    document.getElementById('textSaveResult').innerHTML = result;
                    document.getElementById('textInput').value = '';
                    // 刷新页面以更新文件列表
                    setTimeout(() => location.reload(), 1000);
                })
                .catch(error => {
                    console.error('Error:', error);
                    document.getElementById('textSaveResult').innerHTML = '保存失败！';
                });
        }

        function submitText() {
            const text = document.getElementById('textArea').value;
            if (!text) {
                return;
            }

            var formData = new FormData();
            formData.append('text', text);

            fetch('/update-text', {
                method: 'POST',
                body: formData
            });
        }

        function refreshText() {
            fetch('/get-text')
                .then(response => response.text())
                .then(text => {
                    document.getElementById('textArea').value = text;
                });
        }

        function copyText() {
            const textArea = document.getElementById('textArea');
            textArea.select();
            document.execCommand('copy');
            // 取消选中
            window.getSelection().removeAllRanges();
        }

        function clearText() {
            document.getElementById('textArea').value = '';
            // 同时清空服务器存储的文本
            fetch('/update-text', {
                method: 'POST',
                body: new FormData()
            });
        }

        function generateQR() {
            const urlInput = document.getElementById('urlInput');
            const qrResult = document.getElementById('qrResult');
            const url = urlInput.value.trim();

            if (!url) {
                alert('请输入URL！');
                return;
            }

            // 使用本地API生成二维码
            const qrImage = document.createElement('img');
            qrImage.src = `/generate-qr/${encodeURIComponent(url)}`;
            qrImage.alt = 'QR Code';

            // 清除之前的二维码（如果有）
            qrResult.innerHTML = '';
            qrResult.appendChild(qrImage);
        }

        function clearURL() {
            document.getElementById('urlInput').value = '';
            document.getElementById('qrResult').innerHTML = '';
        }
    </script>
</body>

</html>