"""
<AUTHOR>
@date 2020/7/30 0030
"""
import asyncio
import json
import os
from typing import Dict, List

import time
from aiohttp import web, ClientSession

from domain import ExportCase
from http_request_test import run_cases


async def list_configs(req: web.Request):
    path = r'./resource/httpcase/'
    file_list = os.listdir(path)

    resp = {}
    for f in file_list:
        file_path = os.path.join(path, f)
        if os.path.splitext(file_path)[1] == '.json':
            with open(file_path, encoding='UTF-8') as file:
                resp[f] = json.loads(file.read())
    return web.json_response(resp)


async def list_swagger_apis(req: web.Request):
    url = req.query['url']
    async with ClientSession() as session:
        async with session.get(url) as resp:
            swagger_json = await resp.json()

    apis: Dict[str:List[Dict]] = {}
    paths = swagger_json['paths']
    for path in paths:
        for method in paths[path]:
            obj = paths[path][method]
            tag = obj['tags'][0]
            summary = obj['summary']
            params = []
            if 'parameters' in obj:
                params = [p['name'] for p in obj['parameters']]

            if tag not in apis:
                apis[tag] = []
            apis[tag].append({'path': path, 'method': method.upper(), 'name': summary, 'params': params})

    return web.json_response(apis)


async def save(req: web.Request):
    j = await req.json()

    config = j['config']

    case = ExportCase.read_from_dict(config)

    filepath = r'./resource/httpcase/tmp/%s.json' % case.name if 'execute_params' in j \
        else r'./resource/httpcase/%s.json' % case.name

    with open(filepath, 'w', encoding='utf8') as f:
        f.write(json.dumps(config, ensure_ascii=False, indent=2))

    report_filename = ''
    if 'execute_params' in j:
        report_filename = '%s_%s' % (case.name, time.strftime('%Y%m%d_%H%M%S'))
        params = j['execute_params']
        profile = params['profile']
        et = params['type']

        group = None
        uuid = None
        if et == 'group':
            group = params['group']
        elif et == 'single':
            uuid = params['uuid']
        elif et != 'all':
            raise AssertionError
        await asyncio.get_event_loop().run_in_executor(None,
                                                       lambda: run_cases(report_filename, filepath, profile, group,
                                                                         uuid))

    return web.json_response({'report_filename': report_filename})


if __name__ == '__main__':
    print('Open http://127.0.0.1:8080/page/test_case_editor.html')

    app = web.Application()
    app.add_routes(
        [web.get('/list_configs', list_configs), web.get('/list_swagger_apis', list_swagger_apis),
         web.post('/save', save), web.static('/page/report', r'./report'),
         web.static('/page', r'./resource/web/')])

    web.run_app(app)
