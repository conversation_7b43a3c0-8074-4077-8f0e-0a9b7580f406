import hmac, time, base64, urllib.parse
from hashlib import sha1

# appid:[clientkey,serverkey]
appkeys = {'yg28295931': ['sCGZSxhCes0EEvsnYnxkKNj6cXPENW3L', 'XoFbvglmyhMVoEMrLBce6Qk635AnvOr3'],
           'btest': ['123456', '123456'],
           'hunter': ['n6Xr0C7CftwovB4LglvYoxvaB5L4iLQ9', 'CaMWz3WKZ3E4QAG8A44V2zbQmB7AO4mn'],
           'prod055361': ['U7UJUoUVvJK6GGyXHktGDFSu2ozpxRAY', 'vfQWZlkHFNaFhyqSaIs6MAYCnLwwicbN'],
           'audit25427': ['IG2WDNCKZjJMikfJkbRTOlua7oL14PBB', 'l107LX1yvDQLadXcmMDYtDUkNoPKUqdP'],
           'prod320749': ['tE1eO6xaAtp9xICIUBfnQNgYmfRqCnaE', 'YwR81nSiIlYr1fRGKjgDUhHWeJLBkPoS'],
           'prod520438': ['G0CnqVizsobUdYloFSP9mW0EPotSWfUe', 'RJd6VrpygWIRZZKLNQs5TLNaEpRoT1Fu'],
           'yg65vir892638': ['xADni4Txo6rEPXqygKzcAK1npDfh22Rb', 'wdvoJCIMNVhTzBTK7395ZS9Zt9HW0EOZ'],
           'pyth': ['ijRCar7OLmaYYsCtwjV5eXhq5Qh26ymy', 'nw6KeiwQbZ3LeuVSke8j3VUrWpJkGe8i'],
           'audit97688': ['ijRCar7OLmaYYsCtwjV5eXhq5Qh26ymy', 'nw6KeiwQbZ3LeuVSke8j3VUrWpJkGe8i'],
           'yg55992558': ['1', '5q42OlRJN3fXeyQsuJTZT4WKJXFnY9Y2'],
           'prod856774':['7eRw1dTEj0OpYkn3nMJT7BPxicLltpaH','mFE4ev6sweo8KM4KxTMPloUNWcHxHOlQ']
           }
timestamp = str(int(time.time() * 1000))

# ct True：客户端请求 False：服务端请求
def getsign(ct=True, app='voyage', appid='yg28295931'):
    headers = {f'{app}-app-id': appid,
               # f'{app}-c-imei': '351564353542301',
               # f'{app}-c-token': '9ca16ae2e6eed7d661959aafb5c55c909e8ba6c55a939a9a83c560ad8e818cf252f7b0b6b4f72af0febec3b940f0feacc3b92aa884e1aad64b93b7e5a4cc70828f8aa6d85a93908bb8b74ba68683b1e84d92eaa5c300',
               f'{app}-client-ptype': '1',
               f'{app}-client-source': 'xx200',
               f'{app}-client-uuid': 'e9f9df2a0a1565a235da982df9033321',
               f'{app}-nonce': '1611754369',
               f'{app}-signature-method': 'HMAC-SHA1',
               f'{app}-timestamp': timestamp,
               f'{app}-version': '2.4.1.0',
               f'{app}-client-token':'3bdcf39f4e04b67deb990d5389156a5d'
               }
    if ct:
        strsign = '&'.join([f'{x}={headers[x]}' for x in sorted(headers.keys())])
        userkey = appkeys[appid][0]
    else:
        strsign = '&'.join(
            [f'{x}={headers[x]}' for x in ([key for key in sorted(headers.keys()) if 'client' not in key])])
        userkey = appkeys[appid][1]
    appsignature = hmac.new(userkey.encode(), strsign.encode(), sha1).digest()
    appsignature = base64.b64encode(appsignature)
    appsignature = urllib.parse.quote(appsignature, safe="")
    headers[f'{app}-signature'] = appsignature
    return headers
