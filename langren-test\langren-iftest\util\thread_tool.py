"""
<AUTHOR>
@date 2020/6/12 0012
"""
import threading
import time
from concurrent.futures import Future
from concurrent.futures.thread import ThreadPoolExecutor
from typing import TypeVar, Generic, Callable, List, Optional, Tuple

from util.log import get_logger

T = TypeVar('T')

logger = get_logger("ThreadTool")


class ThreadTool(object):

    def __init__(self, max_workers: int = 50):
        self.executor = ThreadPoolExecutor(max_workers=max_workers + 1)
        self.__scheduled_task_queue: List['Task'] = []
        self.__scheduled_terminated = False
        self.run_async(self.__trigger)

    def run_async(self, task: Callable[..., None], args: Tuple = ()) -> Future:

        def wrap_task():
            try:
                task(*args)
            except Exception as e:
                logger.error('error: [%s]' % e)
                raise e

        return self.executor.submit(wrap_task)

    def run_parallel(self, task: Callable[..., T], args_list: List[Tuple]) -> List[T]:
        results: List[T] = [None for i in range(0, len(args_list))]
        exceptions: List[Optional[Exception]] = [None for i in range(0, len(args_list))]
        latch = CountDownLatch(len(args_list))

        def wrap_task(args, i):
            try:
                res = task(*args)
                results[i] = res
            except Exception as e:
                exceptions[i] = e
                raise e
            finally:
                latch.count_down()

        index = 0
        for arg in args_list:
            self.run_async(wrap_task, (arg, index))
            index += 1
        latch.wait()

        for e in exceptions:
            if e is not None:
                raise e
        return results

    def run_race(self, task: Callable[..., T], args_list: List[Tuple]) -> T:
        lock = threading.Condition()
        t = [0, None]

        def wrap_task(args):
            res = None
            try:
                res = task(*args)
            except Exception:
                pass

            with lock:
                t[0] += 1
                if res is not None:
                    t[1] = res
                lock.notify()

        for arg in args_list:
            self.run_async(wrap_task, (arg,))

        while t[0] < len(args_list) and t[1] is None:
            with lock:
                lock.wait()

        return t[1]

    def run_scheduled(self, task: Callable[..., None], args: Tuple, timeout: float):
        self.__scheduled_task_queue.append(Task(time.time() + timeout, task, args))

    def __trigger(self):
        while not self.__scheduled_terminated:
            if len(self.__scheduled_task_queue) == 0:
                time.sleep(0.5)
                continue
            now = time.time()
            it = filter(lambda t: t.trigger_time <= now, self.__scheduled_task_queue.copy())

            end = object()
            while True:
                t = next(it, end)
                if t is end:
                    break
                self.__scheduled_task_queue.remove(t)
                self.run_async(t.task, t.args)
            time.sleep(0.5)

    def shutdown(self):
        self.__scheduled_terminated = True
        self.executor.shutdown()

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.shutdown()


class Signal(Generic[T]):

    def __init__(self):
        self.__event = threading.Event()
        self.__args: T = None

    def notify_all(self, args: T):
        self.__args = args
        self.__event.set()

    def wait(self, timeout: int = None) -> T:
        flag = self.__event.wait(timeout)

        if flag is True:
            return self.__args
        raise RuntimeError('timeout')


class CountDownLatch(object):

    def __init__(self, count: int):
        self.__count = count
        self.__con = threading.Condition()

    def wait(self):
        while self.__count > 0:
            with self.__con:
                self.__con.wait()

    def count_down(self):
        with self.__con:
            self.__count -= 1
            self.__con.notify_all()


class Task(object):
    def __init__(self, trigger_time: float, task: Callable[..., None], args: Tuple):
        self.trigger_time = trigger_time
        self.task = task
        self.args = args


class ScheduledExecutor(object):

    def __init__(self, thread_tool: ThreadTool):
        self.task_queue: List['Task'] = []
        self.terminated = False
        self.thread_tool = thread_tool
        self.thread_tool.run_async(self.trigger)

    def schedule(self, task: Callable[..., None], args: Tuple, timeout: float):
        self.task_queue.append(Task(time.time() + timeout, task, args))

    def trigger(self):
        while not self.terminated:
            if len(self.task_queue) == 0:
                time.sleep(0.5)
                continue
            now = time.time()
            it = filter(lambda t: t.trigger_time <= now, self.task_queue.copy())

            end = object()
            while True:
                t = next(it, end)
                if t is end:
                    break
                self.task_queue.remove(t)
                self.thread_tool.run_async(t.task, t.args)
            time.sleep(0.5)
