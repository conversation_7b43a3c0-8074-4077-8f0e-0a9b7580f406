import requests
import clientsign
import json, datetime, time

# baseurl = 'https://qa.boom.caniculab.com/boom/'
baseurl='https://apisdb.boom.caniculab.com/boom/'


class Boom():
    def __init__(self, appid='btest', ph='14725836915'):
        self.appid = appid
        self.headers = clientsign.getsign(appid)
        self.ph = ph

    # 获取隐私政策
    def gerplo(self):
        url = baseurl + 'open/privacyPolicy'
        query = {'appId': self.appid, 'phoneType': '2'}
        response = requests.get(url, params=query, headers=self.headers, verify=False)
        print("*********获取隐私政策***********")
        print(response.text)

    # 获取初始配置
    def getconfig(self):
        url = baseurl + 'client/init/config'
        response = requests.get(url, headers=self.headers, verify=False)
        print("*********获取初始配置***********")
        print(response.text)

    # 安卓验证
    def initandroid(self):
        url = baseurl + 'client/init/config'
        query = {'packageName': 'com.man4fun.test', 'signature': '4b35d673684f04423b04bb88cbddf9d5'}
        response = requests.get(url, headers=self.headers, verify=False)
        print("*********安卓验证***********")
        print(response.text)

    # ios验证
    def initios(self):
        url = baseurl + 'client/init/ios'
        query = {
            'bundleId': 'com.sparkinglab.tcamera',
            'idfa': '42CA2CDC-8D2D-4E18-9FC1-82EAB2D3A7AF',
            'uniqueId': '52493723CFA616FF0B3DAB41584C71AE',
            'uuid': '909f9f5346c3456e8b98ae4806cf87b3'
        }
        response = requests.get(url, params=query, headers=self.headers, verify=False)
        print("*********ios验证***********")
        print(response.text)

    # 问题反馈
    def feedback(self):
        url = baseurl + 'client/data/feedback'
        data = {
            "contact": f'{datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}++++++' + self.appid + '国内心',
            "issue": f'时间{datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}++++++' + self.appid + '国用来歌都会带有爱国主义色彩、呼唤起人们内心',
            "issueTime": time.time() * 1000,
            "issueType": 2,
            "language": "zh",
            # "logFile": "",
            "model": "unk",
            "netType": "wifi2",
            "resolution": "32*77",
            "screenshots": [
            ],
            "version": "6.3"
        }
        response = requests.post(url, json=data, headers=self.headers, verify=False)
        print('*********************问题反馈************')
        print(response.text)

    # 获取验证码
    def getcode(self, ph):
        url = baseurl + 'client/security/verificationCode'
        query = {"phoneNumber": ph,
                 'codeVerificationType': '0',
                 # 'bundleId': 'com.sparkinglab.tcamera',
                 # 'idfa': '42CA2CDC-8D2D-4E18-9FC1-82EAB2D3A7AF',
                 # 'uniqueId': '52493723CFA616FF0B3DAB41584C71AE',
                 # 'uuid': '909f9f5346c3456e8b98ae4806cf87b3'
                 }
        response = requests.get(url, params=query, headers=self.headers, verify=False)
        print("*********获取验证码***********")
        print(response.text)

    # 手机号登录
    def phonelogin(self, ph):
        url = baseurl + 'client/security/phone/login'
        query = {"phoneNumber": ph,
                 "verificationCode": "1234",
                 'cancelClosing': '0',
                 # 'bundleId': 'com.sparkinglab.tcamera',
                 # 'idfa': '42CA2CDC-8D2D-4E18-9FC1-82EAB2D3A7AF',
                 # 'uniqueId': '52493723CFA616FF0B3DAB41584C71AE',
                 # 'uuid': '909f9f5346c3456e8b98ae4806cf87b3',
                 'imeiMD5': '304f97e71862ebf51df4d81edaf97056',
                 # 'oaidMd5':'',
                 # 'openId': ''
                 }
        query = sorted(query.items())
        query = dict(query)
        response = requests.post(url, data=query, headers=self.headers, verify=False)
        print(response.text)
        boom_token = json.loads(response.text)["result"]['token']
        return boom_token

    # 注销提示框，非实际注销
    def logout(self):
        url = baseurl + 'client/security/logout'
        query = {'bundleId': 'com.sparkinglab.tcamera',
                 'idfa': '42CA2CDC-8D2D-4E18-9FC1-82EAB2D3A7AF',
                 'uniqueId': '52493723CFA616FF0B3DAB41584C71AE',
                 'uuid': '909f9f5346c3456e8b98ae4806cf87b3',
                 'openId': ''}
        response = requests.post(url, data=query, headers=self.headers, verify=False)
        print(response.text)

    # 检测unqiueid是否可用
    def checku(self):
        url = baseurl + 'client/security/guest/check'
        query = {'uniqueId': 'oklikoikl'
                 }
        response = requests.post(url, data=query, headers=self.headers, verify=False)
        print(response.text)

    # 游客登陆
    def guestlogin(self):
        url = baseurl + 'client/security/guest/login'
        query = {'uniqueId': 'oklikoikl'
                 }
        response = requests.post(url, data=query, headers=self.headers, verify=False)
        print(response.text)

    def activate(self):
        url = baseurl + 'client/security/activate'
        params = {
            # 'adData': 'adData - ios传 adData',
            # 'adToken': 'adToken - ios传 adToken',
            # 'channel': 'channel - 渠道',
            # 'idfa': 'idfa - ios传 idfa原值',
            # 'imeiMD5': 'bae979aa20599cb7147b8a7380bfcd84',
            # 'oaidMD5': 'oaidMD5 - 安卓传 oaidMD5'
        }
        response = requests.get(url, params=params, headers=self.headers, verify=False)
        print("*********设备激活***********")
        print(response.text)

    def start(self):
        self.gerplo()
        self.getconfig()
        self.initandroid()
        self.initios()
        # for i in range(69):
        #     self.feedback()
        # self.activate()
        self.getcode(self.ph)
        self.phonelogin(self.ph)  # 多线程待处理
        # self.guestlogin()



phs=[f'100596700{x}'for x in range(10,31)]
print(phs)
# phs=['10059670001','10059670002']
for a in phs:
    a = Boom('bestwish10', ph=a)
    a.start()
