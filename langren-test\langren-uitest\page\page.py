"""
<AUTHOR>
@date 2020/6/16 0016
"""
from abc import abstractmethod
from typing import Dict, List, Optional, Tuple

from airtest.core import api
from airtest.core.cv import Template
from poco import Poco
from poco.proxy import UIObjectProxy

from biz.gift.domain import Gift


class TemplateObjectProxy(object):

    def __init__(self, template: Template):
        self.__template = template

    def click(self):
        api.click(self.__template)


class HomeTabPage(object):

    def __init__(self, poco: Poco):
        self.poco = poco

    @property
    @abstractmethod
    def home_tab_btn(self) -> UIObjectProxy:
        pass

    @property
    @abstractmethod
    def nickname_label(self) -> UIObjectProxy:
        pass

    @property
    @abstractmethod
    def vip_duration_label(self) -> UIObjectProxy:
        pass

    @property
    @abstractmethod
    def user_info_back(self) -> UIObjectProxy:
        pass

    @property
    @abstractmethod
    def create_room_btn(self) -> UIObjectProxy:
        pass

    @property
    @abstractmethod
    def search_room_btn(self) -> UIObjectProxy:
        pass

    @property
    @abstractmethod
    def dialog_confirm_btn(self) -> UIObjectProxy:
        pass

    @property
    @abstractmethod
    def search_room_input(self) -> UIObjectProxy:
        pass

    @property
    @abstractmethod
    def follow_room_btn(self) -> UIObjectProxy:
        pass

    @property
    @abstractmethod
    def enter_follow_room_btn(self) -> UIObjectProxy:
        pass


class GameRoomPage(object):

    def __init__(self, poco: Poco):
        self.poco = poco

    @property
    @abstractmethod
    def room_num_label(self) -> UIObjectProxy:
        pass

    @property
    @abstractmethod
    def game_ready_btn(self) -> 'UIObjectProxy':
        pass

    @property
    @abstractmethod
    def start_game_btn(self) -> 'UIObjectProxy':
        pass

    @property
    @abstractmethod
    def game_role_identity_label(self) -> UIObjectProxy:
        pass

    @property
    @abstractmethod
    def game_role_prompt(self) -> UIObjectProxy:
        pass

    @abstractmethod
    def select_kill_man_btn(self, seat_num: int) -> UIObjectProxy:
        pass

    @property
    @abstractmethod
    def seat_num(self) -> Dict[str, int]:
        pass

    @abstractmethod
    def seat_member_img(self, seat_num: int) -> UIObjectProxy:
        pass

    @property
    @abstractmethod
    def send_gift_btn(self) -> UIObjectProxy:
        pass

    @property
    @abstractmethod
    def latest_gift(self) -> Optional[Tuple[bool, int, int, Gift]]:
        pass

    @property
    @abstractmethod
    def unread_msg_label(self) -> UIObjectProxy:
        pass

    @abstractmethod
    def swipe_down(self):
        pass

    @abstractmethod
    def select_poison_man_btn(self, seat_num: int) -> UIObjectProxy:
        pass

    @property
    @abstractmethod
    def witch_rescue_confirm_btn(self) -> UIObjectProxy:
        pass

    @property
    @abstractmethod
    def witch_rescue_cancel_btn(self) -> UIObjectProxy:
        pass

    @property
    @abstractmethod
    def witch_poison_confirm_btn(self) -> UIObjectProxy:
        pass

    @property
    @abstractmethod
    def witch_poison_cancel_btn(self) -> UIObjectProxy:
        pass

    @property
    @abstractmethod
    def speak_end_btn(self) -> UIObjectProxy:
        pass

    @abstractmethod
    def select_vote_man_btn(self, seat_num: int) -> UIObjectProxy:
        pass

    @property
    @abstractmethod
    def vote_confirm_btn(self) -> UIObjectProxy:
        pass

    @property
    @abstractmethod
    def vote_cancel_btn(self) -> UIObjectProxy:
        pass

    @property
    @abstractmethod
    def last_words_end_btn(self) -> UIObjectProxy:
        pass

    @property
    @abstractmethod
    def game_progress_label(self) -> UIObjectProxy:
        pass

    @property
    @abstractmethod
    def system_msg_labels(self) -> List[UIObjectProxy]:
        pass

    @property
    @abstractmethod
    def latest_system_msg_label(self) -> UIObjectProxy:
        pass

    @property
    @abstractmethod
    def vote_result_label(self) -> UIObjectProxy:
        pass

    @property
    @abstractmethod
    def first_win_seat_num_label(self) -> UIObjectProxy:
        pass

    @property
    @abstractmethod
    def game_result_win_dialog(self) -> UIObjectProxy:
        pass

    @property
    @abstractmethod
    def kill_dialog_voice_btn(self) -> UIObjectProxy:
        pass


class MessageTabPage(object):

    def __init__(self, poco: Poco):
        self.poco = poco

    @property
    @abstractmethod
    def msg_tab_btn(self) -> UIObjectProxy:
        pass

    @property
    @abstractmethod
    def add_friend_btn(self) -> UIObjectProxy:
        pass

    @property
    @abstractmethod
    def add_friend_id_input(self) -> UIObjectProxy:
        pass

    @property
    @abstractmethod
    def add_friend_id_confirm_btn(self) -> UIObjectProxy:
        pass


class MyTabPage(object):

    def __init__(self, poco: Poco):
        self.poco = poco

    @property
    @abstractmethod
    def my_tab_btn(self) -> UIObjectProxy:
        pass

    @property
    @abstractmethod
    def personal_card_btn(self) -> UIObjectProxy:
        pass


class UserHomePage(object):

    def __init__(self, poco: Poco):
        self.poco = poco

    @property
    @abstractmethod
    def private_msg_btn(self) -> UIObjectProxy:
        pass

    @property
    @abstractmethod
    def edit_profile_btn(self) -> UIObjectProxy:
        pass

    @property
    @abstractmethod
    def nickname_label(self) -> UIObjectProxy:
        pass

    @property
    @abstractmethod
    def signature_label(self) -> UIObjectProxy:
        pass


class UserProfileEditPage(object):

    def __init__(self, poco: Poco):
        self.poco = poco

    @property
    def head_img(self) -> UIObjectProxy:
        return self.poco(text="头像")

    @property
    def nickname(self) -> UIObjectProxy:
        return self.poco(text="昵称")

    @property
    def gender(self) -> UIObjectProxy:
        return self.poco(text="性别")

    @property
    def signature(self) -> UIObjectProxy:
        return self.poco(text="签名")

    @property
    def background_img(self) -> UIObjectProxy:
        return self.poco(text="背景图")

    @property
    @abstractmethod
    def buy_vip_dialog(self) -> UIObjectProxy:
        pass

    @property
    def cancel_buy_vip_btn(self) -> UIObjectProxy:
        return self.poco(text="取消")

    @property
    @abstractmethod
    def nickname_input(self) -> UIObjectProxy:
        pass

    @property
    @abstractmethod
    def signature_input(self) -> UIObjectProxy:
        pass

    @property
    def select_photo_btn(self) -> UIObjectProxy:
        return self.poco(textMatches="^选择相册.*$")

    def choose_photo(self, name: str) -> UIObjectProxy:
        return self.poco(text=name)

    @property
    def camera_btn(self) -> UIObjectProxy:
        return self.poco(textMatches="^拍照.*$")

    @property
    @abstractmethod
    def confirm_crop_btn(self) -> UIObjectProxy:
        pass

    @property
    def confirm_btn(self) -> UIObjectProxy:
        return self.poco(text="确认")

    @property
    def cancel_btn(self) -> UIObjectProxy:
        return self.poco(text="取消")


class UserGenderEditPage(object):

    def __init__(self, poco: Poco):
        self.poco = poco

    @property
    @abstractmethod
    def selected_gender(self) -> int:
        # 0->female 1->male
        pass

    @property
    def male_btn(self) -> UIObjectProxy:
        return self.poco(text="男")

    @property
    def female_btn(self) -> UIObjectProxy:
        return self.poco(text="女")

    @property
    @abstractmethod
    def gender_card_num(self) -> int:
        pass

    @property
    def buy_gender_card_btn(self) -> UIObjectProxy:
        return self.poco(text="快速购买")

    @property
    def confirm_buy_btn(self) -> UIObjectProxy:
        return self.poco(text="购买")

    @property
    def buy_accept_btn(self) -> UIObjectProxy:
        return self.poco(text="确定")

    @property
    @abstractmethod
    def edit_btn(self) -> UIObjectProxy:
        pass

    @property
    def confirm_edit_btn(self) -> UIObjectProxy:
        return self.poco(text="确认修改")


class PrivateMessagePage(object):

    def __init__(self, poco: Poco):
        self.poco = poco

    @property
    @abstractmethod
    def send_gift_btn(self) -> UIObjectProxy:
        pass

    @property
    @abstractmethod
    def latest_gift(self) -> Optional[Tuple[bool, Gift]]:
        pass

    @property
    @abstractmethod
    def unread_msg_label(self) -> UIObjectProxy:
        pass

    @abstractmethod
    def swipe_down(self):
        pass


class GiftPanel(object):

    def __init__(self, poco: Poco):
        self.poco = poco

    @property
    @abstractmethod
    def open_select_num_btn(self) -> UIObjectProxy:
        pass

    @abstractmethod
    def gift_num_btn(self, num: str) -> UIObjectProxy:
        pass

    @property
    @abstractmethod
    def gift_num_input(self) -> UIObjectProxy:
        pass

    @property
    @abstractmethod
    def gift_num_input_confirm_btn(self) -> UIObjectProxy:
        pass

    @abstractmethod
    def swipe_prev(self):
        pass

    @abstractmethod
    def swipe_next(self):
        pass

    @abstractmethod
    def gift_names(self) -> List[Tuple[bool, UIObjectProxy]]:
        pass

    @property
    @abstractmethod
    def confirm_send_btn(self) -> UIObjectProxy:
        pass


class PageRoot(object):

    def dispose(self):
        pass

    @property
    @abstractmethod
    def poco(self) -> Poco:
        pass

    @property
    @abstractmethod
    def back_btn(self) -> UIObjectProxy:
        pass

    @property
    @abstractmethod
    def home_tab_page(self) -> HomeTabPage:
        pass

    @property
    @abstractmethod
    def game_room_page(self) -> GameRoomPage:
        pass

    @property
    @abstractmethod
    def message_tab_page(self) -> MessageTabPage:
        pass

    @property
    @abstractmethod
    def my_tab_page(self) -> MyTabPage:
        pass

    @property
    @abstractmethod
    def user_home_page(self) -> UserHomePage:
        pass

    @property
    @abstractmethod
    def user_profile_edit_page(self) -> UserProfileEditPage:
        pass

    @property
    @abstractmethod
    def user_gender_edit_page(self) -> UserGenderEditPage:
        pass

    @property
    @abstractmethod
    def private_message_page(self) -> PrivateMessagePage:
        pass

    @property
    @abstractmethod
    def gift_panel(self) -> GiftPanel:
        pass
