import requests, multiprocessing
from BOOM.boom import serversign
import json, os
import urllib3

# baseurl = 'https://qa.boom.caniculab.com/boom/'
baseurl = 'https://apisdb.boom.caniculab.com/boom/'
headers = serversign.getsign('bestwish10')


# headers = serversign.getsign('hunter')


def placeorder():
    headers['Content-Type'] = 'application/json'
    url = baseurl + 'sdk/recharge/order/place'
    data = {
        "amount": 1,
        "body": "我要买买买",
        "cpOrderNo": "orderNo_1713349932292.93",
        "openId": "bestwish10-sdb-17299",
        "productCount": 1,
        # "productId": "eggkn",
        "subject": "充值0.01元元元元元元元元"
    }
    data1 = {
        "amount": 1,
        "body": "冲冲冲",
        "cpOrderNo": "orderNo_1713350516377.724",
        "openId": "bestwish10-sdb-17299",
        "productCount": 1,
        "productId": "eggkn",
        "subject": "蛋刀"
    }
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
    response = requests.post(url, headers=headers, data=json.dumps(data1), verify=False)
    # response = requests.post(url, headers=headers, data=json.dumps(data))
    print(response.text)


# placeorder()
# 查询订单
def query(cpOrderNo):
    data = {'cpOrderNo': cpOrderNo}
    url = 'https://apisdb.boom.caniculab.com/boom/sdk/recharge/order/query'
    response = requests.get(url, params=data, headers=headers)
    print(response.url)
    print(response.text)


# query('*********************')


# 查ip
def ipquery(ip):
    url = baseurl + 'sdk/ip/query'
    data = {'ip': ip}
    response = requests.get(url, params=data, headers=headers)
    print(response.text)
    return response.text


# ipquery('*******')
# ipquery('2408:840c:de81:52d:cc8b:522f:6069:d177')


def checkimg(img):
    headers['Content-Type'] = 'application/json'
    body = {
        "imageType": 1,  # 1普通图片 2用户资料图片
        "account": "123",
        "imageUrl": img,
        "ip": "2408:840c:de81:52d:cc8b:522f:6069:d177"
    }
    url = baseurl + 'sdk/check/image'
    response = requests.post(url, data=json.dumps(body), headers=headers)
    # print(response.request.headers)
    print(response.text)


# checkimg('https://pic.netbian.com/uploads/allimg/240410/223535-17127597352a25.jpg')
def checktext(text):
    headers['Content-Type'] = 'application/json'
    body = {
        "textType": 1,  # 类型 1普通文本, 2用户资料文本
        "account": "123",
        "content": text,
        "ip": "2408:840c:de81:52d:cc8b:522f:6069:d177"
    }
    url = baseurl + 'sdk/check/text'
    response = requests.post(url, data=json.dumps(body), headers=headers)
    print(response.text)

#
# checktext('fu#69###')
# if __name__ == '__main__':
#     checklist=['333','rrrrr','ffdsfs','fdsfsdf']
#     for i in checklist:
#         p1 = multiprocessing.Process(target=checktext, args=(i,))
#         print(i)
#         p1.start()
#         p1.join()
