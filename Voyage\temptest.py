import asyncio
import aiohttp
import uuid
import time
from Appsign import getsign

baseurl = {
    'voyage': 'http://k8s-voyageqa-ingressv-0e43378f0b-1537062284.ap-southeast-1.elb.amazonaws.com/voyage/',
    'boom': 'https://qa.boom.caniculab.com/boom/'
}


class App():
    def __init__(self, appname='boom', appid='btest', ph='14955336964', max_retries=3):
        self.appid = appid
        self.headers = getsign(True, appname, appid)
        self.ph = ph
        self.baseurl = baseurl[appname]
        self.init_android_count = 0
        self.checku_count = 0
        self.guestlogin_count = 0
        self.start_time = time.time()  # 记录测试开始时间
        self.max_retries = max_retries

    async def fetch(self, session, url, method='GET', params=None):
        for attempt in range(self.max_retries):
            try:
                if method == 'GET':
                    async with session.get(url, headers=self.headers, params=params) as response:
                        return await self.handle_response(response)
                elif method == 'POST':
                    async with session.post(url, headers=self.headers, data=params) as response:
                        return await self.handle_response(response)
            except asyncio.TimeoutError:
                print(f"Request to {url} timed out. Attempt {attempt + 1}/{self.max_retries}")
                if attempt == self.max_retries - 1:  # 如果是最后一次重试
                    return None

    async def handle_response(self, response):
        if response.status == 200:
            body = await response.text()
            return body
        else:
            print(f"Request failed with status: {response.status}")
            return None

    async def initandroid(self):
        url = self.baseurl + 'client/init/config'
        response = await self.fetch(self.session, url)
        if response is not None:
            self.init_android_count += 1  # 更新请求计数

    async def checku(self, unique_id):
        url = self.baseurl + 'client/security/guest/check'
        query = {'uniqueId': unique_id}
        response = await self.fetch(self.session, url, method='POST', params=query)
        if response is not None:
            self.checku_count += 1  # 更新请求计数

    async def guestlogin(self, unique_id):
        url = self.baseurl + 'client/security/guest/login'
        query = {'uniqueId': unique_id}
        response = await self.fetch(self.session, url, method='POST', params=query)
        if response is not None:
            self.guestlogin_count += 1  # 更新请求计数

    async def run_tasks(self):
        tasks = []
        target_concurrent_requests = 5  # 增加目标并发请求数量

        for _ in range(target_concurrent_requests):
            unique_id = str(uuid.uuid4()).replace('-', '')  # 生成一个唯一的 uniqueId
            tasks.append(asyncio.create_task(self.initandroid()))  # 并发调用 initandroid 请求
            tasks.append(asyncio.create_task(self.checku(unique_id)))  # 并发调用 checku 请求
            tasks.append(asyncio.create_task(self.guestlogin(unique_id)))  # 并发调用 guestlogin 请求

        # 等待所有任务完成
        await asyncio.gather(*tasks)

        end_time = time.time()
        total_time_seconds = end_time - self.start_time  # 计算总耗时（秒）

        # 打印总时间和平均请求数
        print(f"Total time taken: {total_time_seconds:.2f} seconds")

        if total_time_seconds > 0:
            avg_init_android = self.init_android_count / total_time_seconds
            avg_checku = self.checku_count / total_time_seconds
            avg_guestlogin = self.guestlogin_count / total_time_seconds

            print(f"Total requests for initandroid: {self.init_android_count}")
            print(f"Total requests for checku: {self.checku_count}")
            print(f"Total requests for guestlogin: {self.guestlogin_count}")

            print(f"Average requests per second for initandroid: {avg_init_android:.2f}")
            print(f"Average requests per second for checku: {avg_checku:.2f}")
            print(f"Average requests per second for guestlogin: {avg_guestlogin:.2f}")
        else:
            print("Total time is too short to calculate average.")

    async def start(self):
        timeout = aiohttp.ClientTimeout(total=30)  # 减少总请求超时时间
        connector = aiohttp.TCPConnector(limit_per_host=2000)  # 增加每个主机的最大连接数
        async with aiohttp.ClientSession(connector=connector, timeout=timeout) as self.session:
            await self.run_tasks()


if __name__ == "__main__":
    app_instance = App('voyage', 'prod856774')
    asyncio.run(app_instance.start())