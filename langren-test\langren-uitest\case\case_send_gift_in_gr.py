"""
<AUTHOR>
@date 2020/7/1 0001
"""
import random
import time
import unittest
import warnings
from typing import TypeV<PERSON>, List, Dict, Tuple

from biz.game.domain import SeatProfile
from biz.gift.domain import Gift
from role.role import <PERSON><PERSON><PERSON><PERSON><PERSON>, Role<PERSON>anager
from role.room_role import <PERSON><PERSON><PERSON><PERSON>, Owner, <PERSON><PERSON><PERSON>
from role.user_role import User
from util.device import start_devices, DeviceContext
from util.log import get_logger
from util.method_tool import retry
from util.rpc import RpcClient
from util.thread_tool import ThreadTool

T = TypeVar('T')


def random_pick(l: List[T]) -> T:
    return l[random.randrange(0, len(l))]


logger = get_logger('SendGiftTestCase')

defined_gift_counts = [11, 66, 99, -1]
defined_gift_names = ['幸运盒', '大白', '泰迪熊']


def random_gift_count() -> int:
    pick = random_pick(defined_gift_counts)
    if pick != -1:
        return pick
    return random.randrange(1, 100)


def random_gift_name() -> str:
    return random_pick(defined_gift_names)


devices = ['W4XUT19B01001844', '127.0.0.1:62026']

send_gift_pair = [(1, 2), (2, 1)]

send_gift_config = [(*send_gift_pair[i % 2], random_gift_name(), random_gift_count()) for i in
                    range(0, 20)]


class SendGiftInGrTestCase(unittest.TestCase):
    thread_tool: ThreadTool = None
    device_ctx: DeviceContext = None
    rpc_clients: Dict[str, RpcClient] = None

    @classmethod
    def setUpClass(cls) -> None:
        warnings.simplefilter("ignore", ResourceWarning)
        cls.thread_tool = ThreadTool()
        cls.device_ctx = start_devices(
            devices)
        cls.rpc_clients = cls.device_ctx.rpc_clients

    def setUp(self) -> None:
        self.poll = True
        self.gifts: Dict[int, List[Tuple[bool, int, int, Gift]]] = {}
        self.seat_profiles: Dict[int, SeatProfile] = {}

    def test_send_gift_in_gr(self):
        """私信送礼逻辑"""
        print('send_gift_config: %s' % send_gift_config)
        logger.info('send_gift_config: %s' % send_gift_config)
        # 重启app
        rpc_clients = self.rpc_clients
        self.thread_tool.run_parallel(lambda dn: rpc_clients[dn].get_proxy(DeviceManager).restart_langren_app(),
                                      [(dn,) for dn in devices])

        # 获取昵称
        nicknames = self.thread_tool.run_parallel(lambda dn: rpc_clients[dn].get_proxy(User).infer_user_nickname(),
                                                  [(dn,) for dn in devices])

        # 第一台设备是房主
        own_dn = devices[1]
        # 分配角色
        self.thread_tool.run_parallel(lambda dn: rpc_clients[dn].get_proxy(RoleManager).assign_role(
            Owner if dn == own_dn else SeatUser), [(dn,) for dn in devices])

        # 创房
        owner = self.rpc_clients[own_dn].get_proxy(Owner)
        room_num = owner.create_room()
        # 进房
        self.rpc_clients[devices[0]].get_proxy(SeatUser).join_room(room_num)

        # 获取座位号
        time.sleep(1)
        seat_dict = rpc_clients[own_dn].get_proxy(RoomRole).infer_seat_num()
        if len(seat_dict) != len(rpc_clients):
            time.sleep(4)
            seat_dict = rpc_clients[own_dn].get_proxy(RoomRole).infer_seat_num()

        idx = 0
        for dn in rpc_clients:
            nn = nicknames[idx]
            sn = seat_dict[nn]
            self.seat_profiles[sn] = SeatProfile(sn, rpc_clients[dn], nn, '')
            idx += 1
        self.start_poll_gift()
        # 送礼
        for fsn, tsn, gn, ct in send_gift_config:
            self.seat_profiles[fsn].rpc_client.get_proxy(RoomRole).send_gift_in_gr(tsn, gn, ct)

            @retry(5)
            def try_assert():
                time.sleep(2)
                # 校验送礼方
                assert fsn in self.gifts
                f_is_send, f_fsn, f_tsn, f_gift = self.gifts[fsn][-1]
                assert f_is_send is True
                assert fsn == f_fsn
                assert tsn == f_tsn
                assert f_gift.name == gn
                assert f_gift.count == ct
                # 校验收礼方
                assert tsn in self.gifts
                t_is_send, t_fsn, t_tsn, t_gift = self.gifts[tsn][-1]
                assert t_is_send is False
                assert fsn == t_fsn
                assert tsn == t_tsn
                assert t_gift.name == gn
                assert t_gift.count == ct
                # 校验人气，特效
                assert t_gift.popularity == f_gift.popularity

                def get_effect_detail(effect: str, kw: str) -> List[str]:
                    if effect.strip() == '':
                        return []
                    return [e[e.index(kw) + len(kw):] for e in effect.split('\n')]

                assert get_effect_detail(t_gift.effect, '获得') == get_effect_detail(f_gift.effect, '获得了')

            try_assert()

    def start_poll_gift(self):

        def print_gift(sn: int, g: Tuple[bool, int, int, Gift]):
            txt = '%s [%s] %s->%s %s' % (sn, 'SEND' if g[0] else 'RECEIVE', g[1], g[2], g[3])
            print(txt)
            logger.info(txt)

        def poll_gift(sn: int):
            while self.poll:
                time.sleep(0.5)
                try:
                    lg = self.seat_profiles[sn].rpc_client.get_proxy(RoomRole).get_latest_gift_in_room()
                    if lg is None:
                        continue
                    if sn not in self.gifts:
                        self.gifts[sn] = [lg]
                        print_gift(sn, lg)
                    else:
                        if self.gifts[sn][-1] != lg:
                            self.gifts[sn].append(lg)
                            print_gift(sn, lg)

                except Exception as e:
                    print(e)
                    continue

        for sn in {p[0] for p in send_gift_pair}.union({p[1] for p in send_gift_pair}):
            self.thread_tool.run_async(poll_gift, (sn,))

    def tearDown(self) -> None:
        self.poll = False

    @classmethod
    def tearDownClass(cls) -> None:
        cls.thread_tool.shutdown()
        cls.device_ctx.dispose()


if __name__ == '__main__':
    unittest.main()
