<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TestCase Editor</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap-vue/2.15.0/bootstrap-vue.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/4.5.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap-vue/2.15.0/bootstrap-vue-icons.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/codemirror/5.54.0/codemirror.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/codemirror/5.54.0/theme/material-darker.css" rel="stylesheet">
    <script src="https://cdn.bootcdn.net/ajax/libs/vue/2.6.11/vue.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/Sortable/1.10.2/Sortable.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/bootstrap-vue/2.15.0/bootstrap-vue.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/bootstrap-vue/2.15.0/bootstrap-vue-icons.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/Vue.Draggable/15.0.0/vuedraggable.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/codemirror/5.54.0/codemirror.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/codemirror/5.54.0/mode/python/python.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/codemirror/5.54.0/mode/javascript/javascript.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/axios/0.20.0-0/axios.min.js"></script>
    <style>
        .group-item {
            margin-top: 5px;
        }

        .row {
            margin: 5px;
        }

        .nav-button {
            margin-right: 5px;
        }

        .hide {
            display: none;
        }

        [v-cloak] {
            display: none;
        }

        .center-screen {
            position: fixed;
            left: 50%;
            top: 50%;
        }

        .cover {
            background-color: rgba(177, 177, 177, 0.3);
            position: fixed;
            top: 0;
            left: 0;
            height: 100%;
            width: 100%;
            z-index: 999999;
        }

        .text-ellipsis {
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
        }

        .row > div {
            text-align: center;
        }
    </style>
</head>
<body>

<div v-cloak id="app">
    <div class="cover" v-if="loading">
        <b-spinner class="center-screen" variant="primary" label="Loading..."></b-spinner>
    </div>
    <b-navbar variant="faded" type="light">
        <b-navbar-brand tag="h1" class="mb-0">TestCase Editor</b-navbar-brand>
        <b-navbar-brand tag="h1" class="mb-0">{{name}}</b-navbar-brand>
        <b-navbar-nav class="ml-auto">
            <b-nav-form>
                <b-form-input size="sm" class="mr-sm-2" placeholder="Search" v-model="search"></b-form-input>
                <b-button class="nav-button" size="sm" variant="outline-primary" v-b-modal="'settings-modal'">Settings
                </b-button>
                <b-button class="nav-button" size="sm" variant="outline-primary" v-b-modal="'config-modal'">View
                </b-button>
                <b-button class="nav-button" size="sm" variant="outline-primary"
                          @click="click('select_json_file')">Import
                </b-button>
                <input type="file" class="hide" id="select_json_file" accept=".json" @change="importFile"/>
                <b-button class="nav-button" size="sm" variant="outline-primary" @click="saveFile">Export</b-button>
            </b-nav-form>
            <b-nav-form>
                <b-button class="nav-button" size="sm" variant="outline-primary" v-b-modal="'select-config-modal'">
                    Select
                </b-button>
                <b-button class="nav-button" size="sm" variant="outline-primary" @click="saveAndExecute(null)">
                    Save
                </b-button>
                <b-form-select class="nav-button" v-model="activeProfile" :options="profileNames"></b-form-select>
                <b-button class="nav-button" variant="outline-success" size="sm" @click="executeAll">Execute
                </b-button>
            </b-nav-form>
        </b-navbar-nav>
    </b-navbar>
    <b-container fluid>
        <b-tabs lazy card v-model="activeGroup" @activate-tab="changeGroup">
            <b-tab v-for="(group,group_index) in groups" :key="group.name" :title="group.name">
                <b-row>
                    <b-col cols="4" style="height: 800px; overflow-y: scroll;">
                        <b-row>
                            <b-col>
                                <b-button pill variant="outline-secondary" size="sm"
                                          v-if="deletedRequests[group.name] && deletedRequests[group.name].length > 0"
                                          @click="restoreRequest(group)">
                                    <b-icon icon="arrow-counterclockwise" aria-hidden="true"></b-icon>
                                    Undo
                                </b-button>
                                <b-button pill variant="outline-dark" size="sm" @click="addRequest">
                                    <b-icon icon="plus" aria-hidden="true"></b-icon>
                                    New Request
                                </b-button>
                                <b-button pill variant="outline-dark" size="sm" v-if="swaggerUrl != null"
                                          v-b-modal="'select-request-modal'">
                                    <b-icon icon="box-arrow-in-down" aria-hidden="true"></b-icon>
                                    Swagger
                                </b-button>
                                <b-button pill variant="outline-success" size="sm" v-if="group.requests.length > 0"
                                          @click="executeGroup(group)">
                                    <b-icon icon="play" aria-hidden="true"></b-icon>
                                    Execute
                                </b-button>
                            </b-col>
                        </b-row>
                        <draggable v-model="group.requests" :group="'group'+group_index">
                            <b-list-group v-for="(request, index) in filter_requests" :key="request">
                                <b-list-group-item class="group-item" v-b-toggle="request.uuid"
                                                   v-on:click="activeRequest=request">
                                    <b-row>
                                        <b-col cols="1"><h6>{{index+1}}</h6></b-col>
                                        <b-col><h6 class="text-ellipsis">{{request.name}}</h6></b-col>
                                        <b-col cols="1">
                                            <b-form-checkbox v-model="request.enabled" name="check-button" switch>
                                            </b-form-checkbox>
                                        </b-col>
                                        <b-col cols="1">
                                            <b-button class="ml-auto" pill variant="outline-success" size="sm"
                                                      @click.stop="executeRequest(request)">
                                                <b-icon icon="play" aria-hidden="true"></b-icon>
                                            </b-button>
                                        </b-col>
                                        <b-col cols="1">
                                            <b-button class="ml-auto" pill variant="outline-info" size="sm"
                                                      @click.stop="duplicateRequest(request)">
                                                <b-icon icon="files" aria-hidden="true"></b-icon>
                                            </b-button>
                                        </b-col>
                                        <b-col cols="1">
                                            <b-button class="ml-auto" pill variant="outline-danger" size="sm"
                                                      @click.stop="delRequest(group,request)">
                                                <b-icon icon="trash" aria-hidden="true"></b-icon>
                                            </b-button>
                                        </b-col>
                                    </b-row>
                                </b-list-group-item>
                                <b-collapse :id="request.uuid" class="mt-2">
                                </b-collapse>
                            </b-list-group>
                        </draggable>
                    </b-col>
                    <b-col cols="8">
                        <b-card v-if="activeRequest !== null">
                            <b-row class="row">
                                <b-col>
                                    <b-form-input v-model="activeRequest.name"></b-form-input>
                                </b-col>
                                <b-col cols="2">
                                    <b-button pill variant="outline-info" size="sm"
                                              @click="openScriptEditModal(activeRequest)">
                                        <b-icon icon="pencil-square" aria-hidden="true"></b-icon>
                                        Edit Script
                                    </b-button>
                                </b-col>
                            </b-row>
                            <b-row class="row">
                                <b-col cols="2">
                                    <b-form-select v-model="activeRequest.method"
                                                   :options="methods"></b-form-select>
                                </b-col>
                                <b-col>
                                    <b-form-input v-model="activeRequest.url"
                                                  placeholder="Enter URL"></b-form-input>
                                </b-col>
                            </b-row>
                            <b-row class="row">
                                <b-col>
                                    <b-tabs content-class="mt-3">
                                        <b-tab title="Params" active>
                                            <b-row class="row" v-for="param in activeRequest.params">
                                                <b-col>
                                                    <b-form-input v-model="param.key"
                                                                  placeholder="Enter Key"></b-form-input>
                                                </b-col>
                                                <b-col>
                                                    <b-form-input v-model="param.value"
                                                                  placeholder="Enter Value"></b-form-input>
                                                </b-col>
                                                <b-col cols="1">
                                                    <b-button variant="outline-danger" class="mb-2"
                                                              @click.stop="activeRequest.params.splice(activeRequest.params.indexOf(param),1)">
                                                        <b-icon icon="trash" aria-hidden="true"></b-icon>
                                                    </b-button>
                                                </b-col>
                                            </b-row>
                                            <b-row>
                                                <b-col>
                                                    <b-button pill variant="outline-dark" size="sm"
                                                              @click="activeRequest.params.push({key: '', value: ''})">
                                                        <b-icon icon="plus" aria-hidden="true"></b-icon>
                                                        New Param
                                                    </b-button>
                                                </b-col>
                                            </b-row>
                                        </b-tab>
                                        <b-tab title="Headers">
                                            <b-row class="row" v-for="header in activeRequest.headers">
                                                <b-col>
                                                    <b-form-input v-model="header.key"
                                                                  placeholder="Enter Key"></b-form-input>
                                                </b-col>
                                                <b-col>
                                                    <b-form-input v-model="header.value"
                                                                  placeholder="Enter Value"></b-form-input>
                                                </b-col>
                                                <b-col cols="1">
                                                    <b-button variant="outline-danger" class="mb-2"
                                                              @click.stop="activeRequest.headers.splice(activeRequest.headers.indexOf(header),1)">
                                                        <b-icon icon="trash" aria-hidden="true"></b-icon>
                                                    </b-button>
                                                </b-col>
                                            </b-row>
                                            <b-row>
                                                <b-col>
                                                    <b-button pill variant="outline-dark" size="sm"
                                                              @click="activeRequest.headers.push({key: '', value: ''})">
                                                        <b-icon icon="plus" aria-hidden="true"></b-icon>
                                                        New Header
                                                    </b-button>
                                                </b-col>
                                            </b-row>
                                        </b-tab>
                                        <b-tab title="Body">
                                            <b-row>
                                                <b-col>
                                                    <b-form-radio v-model="activeRequest.body.type"
                                                                  :name="'body-type-group'"
                                                                  value="none">none
                                                    </b-form-radio>
                                                </b-col>
                                                <b-col>
                                                    <b-form-radio v-model="activeRequest.body.type"
                                                                  :name="'body-type-group'"
                                                                  value="formdata">form-data
                                                    </b-form-radio>
                                                </b-col>
                                                <b-col>
                                                    <b-form-radio v-model="activeRequest.body.type"
                                                                  :name="'body-type-group'"
                                                                  value="raw">raw
                                                    </b-form-radio>
                                                </b-col>
                                                <b-col>
                                                    <b-form-radio v-model="activeRequest.body.type"
                                                                  :name="'body-type-group'"
                                                                  value="binary">binary
                                                    </b-form-radio>
                                                </b-col>
                                            </b-row>
                                            <b-row>
                                                <b-col v-if="activeRequest.body.type=='none'"></b-col>
                                                <b-col v-if="activeRequest.body.type=='formdata'">
                                                    <b-row class="row"
                                                           v-for="(fd,fd_index) in activeRequest.body.formdata">
                                                        <b-col>
                                                            <b-form-input v-model="fd.key"
                                                                          placeholder="Enter Key"></b-form-input>
                                                        </b-col>
                                                        <b-col v-if="fd.type=='text'">
                                                            <b-form-input v-model="fd.value"
                                                                          placeholder="Enter Value"></b-form-input>
                                                        </b-col>
                                                        <b-col v-if="fd.type=='file'">
                                                            {{fd.value.filename || 'no file selected.'}}
                                                        </b-col>
                                                        <b-col v-if="fd.type=='file'">
                                                            <b-button variant="outline-info" class="mb-2"
                                                                      @click.stop="click('select_form_file_'+'_'+fd_index)">
                                                                <b-icon icon="file-plus"
                                                                        aria-hidden="true"></b-icon>
                                                                Select File
                                                            </b-button>
                                                            <input type="file" class="hide"
                                                                   :id="'select_form_file_'+'_'+fd_index"
                                                                   @change="importFromFile(fd,$event)"/>
                                                        </b-col>
                                                        <b-col cols="1">
                                                            <b-button variant="outline-danger" class="mb-2"
                                                                      @click.stop="activeRequest.body.formdata.splice(activeRequest.body.formdata.indexOf(fd),1)">
                                                                <b-icon icon="trash"
                                                                        aria-hidden="true"></b-icon>
                                                            </b-button>
                                                        </b-col>
                                                    </b-row>
                                                    <b-row>
                                                        <b-col>
                                                            <b-button pill variant="outline-dark" size="sm"
                                                                      @click="activeRequest.body.formdata.push({key: '', value: '',type:'text'})">
                                                                <b-icon icon="plus" aria-hidden="true"></b-icon>
                                                                New Text
                                                            </b-button>
                                                            <b-button pill variant="outline-dark" size="sm"
                                                                      @click="activeRequest.body.formdata.push({key: '', value: '',type:'file'})">
                                                                <b-icon icon="plus" aria-hidden="true"></b-icon>
                                                                New File
                                                            </b-button>
                                                        </b-col>
                                                    </b-row>
                                                </b-col>
                                                <b-col v-if="activeRequest.body.type=='raw'">
                                                    <b-form-textarea
                                                            id="textarea"
                                                            v-model="activeRequest.body.raw"
                                                            placeholder="Enter something..."
                                                            rows="3"
                                                            max-rows="6"
                                                    ></b-form-textarea>
                                                </b-col>
                                                <b-col v-if="activeRequest.body.type=='binary'"><p>Not available
                                                    yet.</p>
                                                </b-col>
                                            </b-row>
                                        </b-tab>
                                    </b-tabs>
                                </b-col>
                            </b-row>
                        </b-card>
                    </b-col>
                </b-row>
            </b-tab>
        </b-tabs>
    </b-container>
    <b-modal id="settings-modal" static="true" centered title="Settings" hide-footer no-close-on-backdrop
             no-close-on-esc
             size="lg">
        <b-tabs content-class="mt-3">
            <b-tab title="Project Name">
                <b-row class="row">
                    <b-col col="4">
                        <b-form-input v-model="name"
                                      placeholder="Enter Name"></b-form-input>
                    </b-col>
                </b-row>
            </b-tab>
            <b-tab title="Groups">
                <draggable v-model="groups" group="groups">
                    <b-row class="row" v-for="group in groups" size="lg">
                        <b-col>
                            <b-form-input v-model="group.name"
                                          placeholder="Enter Name"></b-form-input>
                        </b-col>
                        <b-col cols="1">
                            <b-button variant="outline-danger" class="mb-2"
                                      @click.stop="delGroup(group)">
                                <b-icon icon="trash" aria-hidden="true"></b-icon>
                            </b-button>
                        </b-col>
                    </b-row>
                </draggable>
                <b-row>
                    <b-col>
                        <b-button pill variant="outline-dark" size="sm"
                                  @click="groups.push({name: 'unnamed', requests: []})">
                            <b-icon icon="plus" aria-hidden="true"></b-icon>
                            New Group
                        </b-button>
                    </b-col>
                </b-row>
            </b-tab>
            <b-tab title="Profiles">
                <b-row class="row" v-for="p in profiles">
                    <b-col col="8">
                        <b-form-input v-model="p.name"
                                      placeholder="Enter Profile Name"></b-form-input>
                    </b-col>
                    <b-col cols="3">
                        <b-button pill variant="outline-info" size="sm"
                                  @click="openScriptEditModal(p)">
                            <b-icon icon="pencil-square" aria-hidden="true"></b-icon>
                            Edit Script
                        </b-button>
                    </b-col>
                    <b-col cols="1">
                        <b-button variant="outline-danger" class="mb-2"
                                  @click.stop="profiles.splice(profiles.indexOf(p),1)">
                            <b-icon icon="trash" aria-hidden="true"></b-icon>
                        </b-button>
                    </b-col>
                </b-row>
                <b-row>
                    <b-col>
                        <b-button pill variant="outline-dark" size="sm" @click="addProfile">
                            <b-icon icon="plus" aria-hidden="true"></b-icon>
                            New Profile
                        </b-button>
                    </b-col>
                </b-row>
            </b-tab>
            <b-tab title="Swagger URL">
                <b-row class="row">
                    <b-col col="4">
                        <b-form-input v-model="swaggerUrl"
                                      placeholder="Enter URL"></b-form-input>
                    </b-col>
                </b-row>
            </b-tab>
        </b-tabs>
    </b-modal>
    <b-modal id="script-modal" ref="script-modal" static="true" centered title="Script Editor" no-close-on-backdrop
             no-close-on-esc
             ok-title="Save" size="lg">
        <textarea name="script_code" id="script_code"></textarea>
    </b-modal>
    <b-modal id="config-modal" static="true" centered title="View Config" hide-footer size="lg">
        <textarea name="config_code" id="config_code"></textarea>
    </b-modal>

    <b-modal id="select-config-modal" ref="select-config-modal" static="true" centered title="Select Config" size="lg">
        <b-form-select v-model="selectedConfigName" :options="allConfigNames"></b-form-select>
    </b-modal>

    <b-modal id="select-request-modal" ref="select-request-modal" static="true" centered title="Select Request"
             size="lg">
        <b-form-select v-model="selectedRequest" :options="allSwaggerRequests"></b-form-select>
        <p v-if="selectedRequest != null">{{selectedRequest.method + " " + selectedRequest.path}}</p>
    </b-modal>
    <b-modal id="duplicate-request-modal" ref="duplicate-request-modal" static="true" centered title="Select Group"
             size="lg">
        <b-form-select v-model="duplicateTargetGroupName" :options="groupNames"></b-form-select>
    </b-modal>
</div>
<a id="save_json_file" class="hide"></a>
</body>
<script type="text/javascript">
    window.onbeforeunload = function (e) {
        e.returnValue = "Confirm leave?";
    };

    function saveFile(name, txt) {
        const blob = new Blob([txt]);
        const link = document.getElementById('save_json_file');
        link.download = name + new Date().getTime() + ".json";
        link.href = URL.createObjectURL(blob);
        link.click()
    }

    function guid2() {

        function s4() {
            return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
        }

        return (s4() + s4() + "-" + s4() + "-" + s4() + "-" + s4() + "-" + s4() + s4() + s4());
    }


    function arrayBufferToBase64(buffer) {
        var binary = '';
        var bytes = new Uint8Array(buffer);
        for (var len = bytes.byteLength, i = 0; i < len; i++) {
            binary += String.fromCharCode(bytes[i]);
        }
        return window.btoa(binary);
    }
</script>
<script type="text/javascript">
    var PROFILE_SCRIPT = 'def init(ctx):\n  pass\n\ndef before(ctx, test, req):\n  pass\n\ndef after(ctx, test, req, resp):\n  pass';
    var REQUEST_SCRIPT = 'def init(ctx, test):\n  pass\n\ndef before(ctx, test, req):\n  pass\n\ndef after(ctx, test, req, resp):\n  pass';


    var app = new Vue({
        el: '#app',
        data: {
            loading: false,
            name: 'unnamed',
            version: 1,
            profiles: [],
            methods: ['GET', 'POST', 'PUT', 'DELETE'],
            requests: [],
            swaggerUrl: null,
            search: '',
            activeGroup: 0,
            activeProfile: null,
            activeRequest: null,
            groups: [{name: 'unnamed', requests: []}],
            selectedConfigName: null,
            allConfigs: {},
            deletedRequests: {},
            allSwaggerRequests: [],
            selectedRequest: null,
            duplicateTargetGroupName: null
        },
        computed: {
            filter_requests: function () {
                let requests = this.groups[this.activeGroup].requests;
                if (this.search === '') {
                    return requests;
                }

                return requests.filter(v => v.name.indexOf(this.search) !== -1);
            },
            config: function () {
                return JSON.stringify({
                    name: this.name,
                    version: this.version,
                    swaggerUrl: this.swaggerUrl,
                    profiles: this.profiles,
                    groups: this.groups
                }, null, 2);
            },
            profileNames: function () {
                return this.profiles.map(function (p) {
                    return p.name
                })
            },
            allConfigNames: function () {
                return Object.keys(this.allConfigs)
            },
            groupNames: function () {
                return this.groups.map(function (p) {
                    return p.name
                })
            },
        },
        methods: {
            click: function (id) {
                document.getElementById(id).click()
            },
            toast: function (msg) {
                this.$bvToast.toast(msg, {
                    title: 'Tips',
                    autoHideDelay: 1000,
                    appendToast: true,
                    toaster: 'b-toaster-top-center'
                })
            },
            changeGroup: function (n, o, event) {
                this.activeRequest = null;
            },
            openScriptEditModal: function (item) {
                let that = this;
                this.$refs['script-modal'].$off('ok');
                this.$refs['script-modal'].$on('ok', function () {
                    item.script = that.editor.getValue();
                    that.editor.setValue('');
                });
                this.editor.setValue(item.script);
                this.editor.refresh();
                this.$refs['script-modal'].show();
            },
            addProfile: function () {
                this.profiles.push({
                    name: 'unnamed',
                    script: PROFILE_SCRIPT
                })
            },
            addRequest: function () {
                this.groups[this.activeGroup].requests.push({
                    uuid: guid2(),
                    enabled: true,
                    name: 'unnamed',
                    method: 'GET',
                    url: '',
                    params: [],
                    headers: [],
                    body: {
                        type: 'none',
                        'formdata': []
                    },
                    script: REQUEST_SCRIPT
                });
            },
            duplicateRequest: function (request) {
                let that = this;
                this.$refs['duplicate-request-modal'].$off('ok');
                this.$refs['duplicate-request-modal'].$on('ok', function () {
                    if (!that.duplicateTargetGroupName) {
                        return
                    }
                    let nr = JSON.parse(JSON.stringify(request));
                    nr.uuid = guid2();

                    that.groups.forEach(function (g, i) {
                        if (g.name === that.duplicateTargetGroupName) {
                            g.requests.push(nr);
                            that.activeGroup = i;
                            return false;
                        }
                        return true;
                    });
                });
                this.$refs['duplicate-request-modal'].show();
            }, delRequest: function (group, request) {
                let index = group.requests.indexOf(request);
                group.requests.splice(index, 1);
                if (this.activeRequest === request) {
                    this.activeRequest = null
                }

                if (!this.deletedRequests[group.name]) {
                    this.deletedRequests[group.name] = []
                }

                this.deletedRequests[group.name].push({index: index, request: request})
            }, delGroup: function (group) {
                let groups = this.groups;
                let groupIndex = groups.indexOf(group);
                if (this.activeGroup === groupIndex) {
                    this.activeGroup = 0;
                }
                groups.splice(groupIndex, 1);
            }, restoreRequest: function (group) {
                let request = this.deletedRequests[group.name].pop();
                group.requests.splice(request.index, 0, request.request)
            }, resetData: function (data) {
                for (let k in data) {
                    if (!data.hasOwnProperty(k)) {
                        return
                    }
                    let v = data[k];
                    if (v instanceof Array) {
                        this[k].splice(0);
                        this[k].push.apply(this[k], v);
                    } else {
                        this[k] = v;
                    }
                }
                this.activeGroup = 0;
                this.activeRequest = null;
                this.activeProfile = null;
                this.toast(`Load Success`)
            }, importFile: function (event) {
                let file = event.target.files[0];
                let fileReader = new FileReader();
                fileReader.readAsText(file);
                let that = this;
                fileReader.onload = function (e) {
                    let txt = e.target.result;
                    let data = JSON.parse(txt);
                    that.resetData(data);
                }
            }, saveFile() {
                saveFile(this.name, this.config);
                this.toast(`Save Success`)
            },
            importFromFile: function (fd, event) {
                let file = event.target.files[0];
                let fileReader = new FileReader();
                fileReader.onload = function (e) {
                    let buf = e.target.result;
                    let str = arrayBufferToBase64(buf);
                    fd.value = {
                        filename: file.name,
                        bin: str
                    }
                };
                fileReader.readAsArrayBuffer(file);
            },
            saveAndExecute: function (ep) {
                this.loading = true;
                let that = this;
                let body = {
                    config: {
                        name: this.name,
                        version: this.version,
                        swaggerUrl: this.swaggerUrl,
                        profiles: this.profiles,
                        groups: this.groups
                    }
                };

                if (ep) {
                    body['execute_params'] = ep
                }
                axios({
                    url: '/save',
                    method: 'post',
                    data: JSON.stringify(body, null, 2)
                }).then(function (response) {
                    let reportFilename = response.data.report_filename;
                    if (reportFilename) {
                        window.open('/page/report/' + reportFilename + '.html')
                    }

                    that.loading = false;
                    that.toast(`OK`)
                }).catch(function (error) {
                    that.loading = false;
                    that.toast(`ERROR`)
                })
            },
            checkActiveProfile: function () {
                if (!this.activeProfile) {
                    this.toast(`select a profile first`);
                    return false
                }

                return true
            },
            executeRequest: function (request) {
                if (!this.checkActiveProfile()) {
                    return
                }
                this.saveAndExecute({
                    type: 'single',
                    profile: this.activeProfile,
                    uuid: request.uuid
                })
            },
            executeGroup: function (group) {
                if (!this.checkActiveProfile()) {
                    return
                }
                this.saveAndExecute({
                    type: 'group',
                    profile: this.activeProfile,
                    group: group.name
                })
            },
            executeAll: function () {
                if (!this.checkActiveProfile()) {
                    return
                }
                this.saveAndExecute({
                    type: 'all',
                    profile: this.activeProfile,
                })
            }
        },
        mounted: function () {
            let that = this;

            axios.interceptors.response.use(function (response) {
                return response;
            }, function (error) {
                that.toast('http error occurred');
                console.log(error);
                return Promise.reject(error);
            });

            this.editor = CodeMirror.fromTextArea(document.getElementById('script_code'), {
                value: "",
                mode: "python",
                // theme: 'material-darker',
                lineNumbers: true,
            });

            this.config_editor = CodeMirror.fromTextArea(document.getElementById('config_code'), {
                value: "",
                mode: "javascript",
                // theme: 'material-darker',
                lineNumbers: true,
                readOnly: true,
            });

            this.$root.$on('bv::modal::shown', function (event, modelId) {
                if (modelId === 'script-modal') {
                    that.editor.refresh();
                } else if (modelId === 'config-modal') {
                    that.config_editor.setValue(that.config);
                    that.config_editor.refresh();
                } else if (modelId === 'select-config-modal') {
                    axios.get('/list_configs').then(function (response) {
                        that.allConfigs = response.data
                    })
                } else if (modelId === 'select-request-modal') {
                    axios.get('/list_swagger_apis', {
                        params: {url: this.swaggerUrl}
                    }).then(function (response) {
                        let data = response.data;

                        let groups = [];
                        for (let group in data) {
                            if (!data.hasOwnProperty(group)) {
                                continue
                            }
                            let reqs = data[group];

                            let options = [];
                            reqs.forEach(function (req) {
                                options.push({text: req.name, value: req})
                            });

                            groups.push({label: group, options: options})
                        }
                        that.allSwaggerRequests = groups
                    });
                }
            });

            this.$refs['select-config-modal'].$on('ok', function () {
                if (!that.selectedConfigName) {
                    return
                }
                that.resetData(that.allConfigs[that.selectedConfigName])
            });

            this.$refs['select-request-modal'].$on('ok', function () {
                let req = that.selectedRequest;
                if (!req) {
                    return
                }

                let params = [];
                req.params.forEach(function (param) {
                    params.push({
                        key: param,
                        value: ''
                    })
                });

                that.groups[that.activeGroup].requests.push({
                    uuid: guid2(),
                    enabled: true,
                    name: req.name,
                    method: req.method,
                    url: '${host}' + req.path,
                    params: params,
                    headers: [],
                    body: {
                        type: 'none',
                        'formdata': []
                    },
                    script: REQUEST_SCRIPT
                });
            });
        }
    })
</script>
</html>