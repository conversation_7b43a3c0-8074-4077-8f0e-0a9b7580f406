"""
<AUTHOR>
@date 2020/7/17 0017
"""
from typing import List, Dict, Tuple

import time
import xlwt
from xlwt import Workbook, Worksheet

import im_client as im
from case_tool import do_prepare_db, do_prepare_apollo
from case_tool import read_gift_cases
from domain import UserConfig, GiftConfig
from langren_http import Http<PERSON>all
from langren_im import ImCall
from util.thread_tool import ThreadTool

host = 'lrqa.langren001.com'
case_xlsx_path = r'./resource/send_gift_effect_hg.xlsx'


class ExecuteMode(object):
    # 并行
    PARALLEL = 1
    # 串行
    SEQUENCE = 2


# 用例执行模式
MODE = ExecuteMode.PARALLEL
# 最大并行线程数
MAX_THREAD_NUM = 100


def do_collect(user_cfg: Dict[int, UserConfig], cases: List[Tuple[int, int, int, int]]) -> List[Dict[str, int]]:
    http_calls: Dict[int, HttpCall] = {}
    im_calls: Dict[int, ImCall] = {}

    gift_cfg: Dict[int, GiftConfig] = {}

    for from_user_id, to_user_id, gift_id, count in cases:
        from_user = user_cfg[from_user_id]
        if from_user_id not in http_calls:
            from_http_call = HttpCall(host)
            _, im_token = from_http_call.login(from_user.user_identity,
                                               from_user.access_token,
                                               from_user.device_id)
            from_im_call = ImCall(host)
            from_im_call.login(from_user.user_id, im_token, from_user.device_id)

            http_calls[from_user_id] = from_http_call
            im_calls[from_user_id] = from_im_call

        if gift_id not in gift_cfg:
            gift_config = http_calls[from_user_id].get_gift_config(gift_id)
            gift_cfg[gift_id] = gift_config

    if MODE == ExecuteMode.PARALLEL:
        with ThreadTool(min(MAX_THREAD_NUM, len(cases))) as tool:
            all_effect = tool.run_parallel(
                lambda f, t, g, c: im_calls[f].send_gift(f, t, gift_cfg[g], c), cases)
    elif MODE == ExecuteMode.SEQUENCE:
        all_effect = list(
            map(lambda case: im_calls[case[0]].send_gift(case[0], case[1], gift_cfg[case[2]], case[3]), cases))
    else:
        raise AssertionError

    for ic in im_calls.values():
        ic.dispose()

    for popularity, effects in all_effect:
        effects['人气'] = popularity
    return list(map(lambda x: x[1], all_effect))


def write_effects(data: Dict[str, Tuple[List[Tuple[int, int, int, int]], List[Dict[str, int]]]]):
    workbook: Workbook = Workbook(encoding='utf8')

    for case_name in data:
        cases, all_effect = data[case_name]
        st: Worksheet = workbook.add_sheet(f'CASE_{case_name}')
        st.set_panes_frozen(True)
        st.set_horz_split_pos(1)
        st.set_vert_split_pos(4)

        case_headers = ['送礼方ID', '收礼方ID', '礼物ID', '礼物数量']
        for i in range(0, len(case_headers)):
            st.write(0, i, case_headers[i])

        case_num = len(cases)
        for i in range(0, case_num):
            from_user_id, to_user_id, gift_id, count = cases[i]
            r = i + 1
            st.write(r, 0, from_user_id)
            st.write(r, 1, to_user_id)
            st.write(r, 2, gift_id)
            st.write(r, 3, count)

        effect_names_set = set()
        for d in all_effect:
            effect_names_set = effect_names_set.union(d.keys())
        effect_names = sorted(list(effect_names_set))

        exclude_gift_names = ['VIP', '金币', '人气']
        for egn in exclude_gift_names:
            if egn in effect_names:
                effect_names.remove(egn)
                effect_names.insert(0, egn)

        for i in range(0, len(effect_names)):
            c = i + len(case_headers)
            st.write(0, c, effect_names[i])

        for i in range(0, len(all_effect)):
            r = i + 1
            effects = all_effect[i]
            for effect_name in effects:
                c = effect_names.index(effect_name) + len(case_headers)
                v = effects[effect_name]
                if effect_name in exclude_gift_names:
                    st.write(r, c, v)
                else:
                    __write_cell(st, r, c, v)

        st.write_merge(1 + case_num, 1 + case_num, 0, 2, '合计')
        total_gift_num = sum(map(lambda x: x[3], cases))
        st.write(1 + case_num, 3, total_gift_num)

        total_effect_summaries: Dict[str, Tuple[int, int, int, int]] = {}
        for i in range(0, len(effect_names)):
            en = effect_names[i]
            total_effect_num = 0
            total_appearance_num = 0
            if en in exclude_gift_names:
                min_num = None
                max_num = None
                for effects in all_effect:
                    if en in effects:
                        total_appearance_num += 1
                        v = effects[en]
                        total_effect_num += v
                        if min_num is None or min_num > v:
                            min_num = v
                        if max_num is None or max_num < v:
                            max_num = v
            else:
                min_num = -1
                max_num = 0
                for effects in all_effect:
                    if en in effects:
                        total_appearance_num += 1
                        v = effects[en]

                        if v != -1:
                            total_effect_num += v

                            if min_num > v or min_num == -1:
                                min_num = v
                            if max_num < v and max_num != -1:
                                max_num = v
                        else:
                            max_num = v

            st.write(1 + case_num, i + len(case_headers), total_effect_num)
            total_effect_summaries[en] = (total_effect_num, total_appearance_num, min_num, max_num)

        st = workbook.add_sheet(f'SUMMARY_{case_name}')
        st.set_panes_frozen(True)
        st.set_horz_split_pos(1)
        st.set_vert_split_pos(1)

        summary_headers = ['礼物名称', '总数量', '单次最大数量', '单次最小数量', '出现次数', '总数量/总送礼数量', '总数量/出现次数', '出现次数/总送礼次数']
        for i in range(0, len(summary_headers)):
            st.write(0, i, summary_headers[i])

        for i in range(0, len(effect_names)):
            r = i + 1
            en = effect_names[i]
            st.write(r, 0, en)

            total_effect_num, total_appearance_num, min_num, max_num = total_effect_summaries[en]
            st.write(r, 1, total_effect_num)
            if en in exclude_gift_names:
                st.write(r, 2, max_num)
                st.write(r, 3, min_num)
            else:
                __write_cell(st, r, 2, max_num)
                __write_cell(st, r, 3, min_num)
            st.write(r, 4, total_appearance_num)
            style_decimal = xlwt.easyxf(num_format_str='0.0000')
            st.write(r, 5, total_effect_num / total_gift_num, style_decimal)
            st.write(r, 6, total_effect_num / total_appearance_num, style_decimal)
            style_percent = xlwt.easyxf(num_format_str='0.00%')
            st.write(r, 7, total_appearance_num / case_num, style_percent)

    workbook.save(r'./report/send_gift_effect_%s.xlsx' % time.strftime('%Y%m%d_%H%M%S'))


def __write_cell(st: Worksheet, r: int, c: int, v: int):
    if v != -1:
        st.write(r, c, v)
    else:
        forever_style = xlwt.easyxf('font: color-index white; pattern: pattern solid, fore_colour green;')
        st.write(r, c, '永久', forever_style)


def main():
    start_time = time.time()

    user_cfg, prepare_database, prepare_apollo, all_case = read_gift_cases(case_xlsx_path)

    do_prepare_db(prepare_database)
    do_prepare_apollo(prepare_apollo)

    im.start_reactor()
    try:
        all_effects: Dict[str, Tuple[List[Tuple[int, int, int, int]], List[Dict[str, int]]]] = {}
        for case_name in all_case:
            print(f'start to run case {case_name}')
            cases = all_case[case_name]
            all_effect = do_collect(user_cfg, cases)
            all_effects[case_name] = (cases, all_effect)

        print('start to write_effects...')
        write_effects(all_effects)
    finally:
        im.stop_reactor()
    end_time = time.time()
    print('costs: %ss' % (end_time - start_time))


if __name__ == '__main__':
    main()
