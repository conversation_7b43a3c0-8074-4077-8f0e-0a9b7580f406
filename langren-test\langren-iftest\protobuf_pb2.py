# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: protobuf.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
from google.protobuf import descriptor_pb2
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='protobuf.proto',
  package='',
  serialized_pb=_b('\n\x0eprotobuf.proto\"\x83\x02\n\rRequestHeader\x12\x0b\n\x03sid\x18\x01 \x02(\x05\x12\x0b\n\x03\x63id\x18\x02 \x02(\x05\x12\x0e\n\x06\x63seqNo\x18\x03 \x02(\x03\x12\x0e\n\x06sseqNo\x18\x04 \x01(\x03\x12\x0f\n\x07reqTime\x18\x05 \x02(\x03\x12\x12\n\noffMsgType\x18\x06 \x01(\t\x12\x0f\n\x07signkey\x18\x08 \x01(\t\x12\x13\n\x0bsignVersion\x18\t \x01(\t\x12\x12\n\ndeviceType\x18\n \x01(\t\x12\r\n\x05nonce\x18\x0b \x01(\x05\x12\x19\n\x07\x63\x61ptcha\x18\x0c \x01(\x0b\x32\x08.Captcha\x12\x0b\n\x03sms\x18\r \x01(\t\x12\r\n\x05pcode\x18\x0e \x01(\t\x12\x13\n\x0b\x66orwardSign\x18\x0f \x01(\t\"K\n\x07\x43\x61ptcha\x12\x11\n\tcaptchaId\x18\x01 \x02(\t\x12\x10\n\x08validate\x18\x02 \x02(\t\x12\x1b\n\x13\x65xtVerificationType\x18\x03 \x01(\t\"7\n\x07Request\x12\x1e\n\x06header\x18\x01 \x02(\x0b\x32\x0e.RequestHeader\x12\x0c\n\x04\x62ody\x18\x02 \x02(\x0c\"~\n\x0eResponseHeader\x12\x0b\n\x03sid\x18\x01 \x02(\x05\x12\x0b\n\x03\x63id\x18\x02 \x02(\x05\x12\x0e\n\x06\x63seqNo\x18\x03 \x02(\x03\x12\x0e\n\x06sseqNo\x18\x04 \x01(\x03\x12\x10\n\x08respTime\x18\x05 \x02(\x03\x12\x0c\n\x04\x63ode\x18\x06 \x02(\x05\x12\x12\n\noffMsgType\x18\x07 \x01(\t\"9\n\x08Response\x12\x1f\n\x06header\x18\x01 \x02(\x0b\x32\x0f.ResponseHeader\x12\x0c\n\x04\x62ody\x18\x02 \x02(\x0c\"\x81\x01\n\tErrorResp\x12\x11\n\terrorCode\x18\x01 \x02(\x05\x12\x10\n\x08\x65rrorMsg\x18\x02 \x02(\t\x12\x0e\n\x06\x64\x65tail\x18\x03 \x01(\x0c\x12 \n\x0b\x65rrorParams\x18\x04 \x03(\x0b\x32\x0b.ErrorParam\x12\x1d\n\x15\x65rrorNotificationType\x18\x05 \x01(\x05\"(\n\nErrorParam\x12\x0b\n\x03key\x18\x01 \x02(\t\x12\r\n\x05value\x18\x02 \x02(\t\"\x1c\n\nSeqSyncReq\x12\x0e\n\x06\x64\x65vice\x18\x01 \x02(\t\"?\n\x08LoginReq\x12\x13\n\x0b\x61\x63\x63\x65ssToken\x18\x01 \x02(\t\x12\x0e\n\x06userId\x18\x02 \x02(\x03\x12\x0e\n\x06\x64\x65vice\x18\x03 \x02(\t\"\xcb\x02\n\x16GameRoomUserChangeResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12\x0e\n\x06userId\x18\x02 \x02(\x03\x12\x10\n\x08nickName\x18\x03 \x01(\t\x12\x13\n\x0bheaderThumb\x18\x04 \x01(\t\x12\r\n\x05level\x18\x05 \x01(\x05\x12\x0f\n\x07seatNum\x18\x06 \x01(\x05\x12\x12\n\nchangeType\x18\x07 \x02(\x05\x12\x0f\n\x07\x63ontent\x18\x08 \x01(\t\x12\x10\n\x08password\x18\t \x01(\t\x12\x17\n\x0fpopularityLevel\x18\n \x01(\x05\x12\x11\n\troomLevel\x18\x0b \x01(\x05\x12\x0f\n\x07vipType\x18\x0c \x01(\x05\x12\x14\n\x0cmicrophoneId\x18\r \x01(\x05\x12\x13\n\x0bheadFrameId\x18\x0e \x01(\x05\x12\x0e\n\x06gender\x18\x0f \x01(\x05\x12\x1a\n\x12idleSeatChangeType\x18\x10 \x01(\x05\"Y\n\x11GameRoomReadyResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12\x0e\n\x06userId\x18\x02 \x02(\x03\x12\x12\n\nuserStatus\x18\x03 \x02(\x05\x12\x0f\n\x07readied\x18\x04 \x02(\x05\"N\n\x16GameRoomSeatSwitchResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12\x0f\n\x07seatNum\x18\x02 \x02(\x05\x12\x12\n\nseatStatus\x18\x03 \x02(\x05\"\xc0\x03\n\x0fGameRoomMessage\x12\x0c\n\x04\x66rom\x18\x01 \x02(\t\x12\x10\n\x08\x66romType\x18\x02 \x02(\x05\x12\x0f\n\x07roomKey\x18\x03 \x02(\t\x12\x0f\n\x07msgType\x18\x04 \x01(\x05\x12\x12\n\nmsgSubType\x18\x05 \x01(\x05\x12\x0f\n\x07\x63ontent\x18\x06 \x02(\t\x12\x11\n\tmessageOn\x18\x07 \x02(\x03\x12\x13\n\x0bunsupported\x18\x08 \x01(\t\x12\x11\n\tmessageId\x18\t \x02(\t\x12\x1e\n\x07present\x18\n \x01(\x0b\x32\r.PresentModel\x12\x14\n\x0c\x63hatBubbleId\x18\x0b \x01(\x03\x12\x0f\n\x07vipType\x18\x0c \x01(\x05\x12\x1b\n\x08voteInfo\x18\r \x01(\x0b\x32\t.VoteInfo\x12\"\n\tfireworks\x18\x0e \x01(\x0b\x32\x0f.FireworksModel\x12\x12\n\nfromNickId\x18\x0f \x01(\x03\x12\x14\n\x0cnickIdPretty\x18\x10 \x01(\x08\x12\x0f\n\x07seatNum\x18\x11 \x01(\x05\x12$\n\x0c\x66romUserInfo\x18\x12 \x01(\x0b\x32\x0e.UserInfoModel\x12\"\n\ntoUserInfo\x18\x13 \x01(\x0b\x32\x0e.UserInfoModel\"2\n\x0bLevelUpResp\x12\r\n\x05level\x18\x01 \x02(\x05\x12\x14\n\x0cpopupMessage\x18\x02 \x02(\t\"\x8c\x01\n\x13WerewolfPlayerModel\x12\x0e\n\x06userId\x18\x01 \x02(\x03\x12\x13\n\x0bheaderThumb\x18\x02 \x02(\t\x12\x0f\n\x07seatNum\x18\x03 \x02(\x05\x12\x16\n\x0esurvivalStatus\x18\x04 \x02(\x05\x12\x14\n\x0cidentityType\x18\x05 \x01(\x05\x12\x11\n\tloverFlag\x18\x06 \x01(\x08\"a\n\x11WerewolfKillModel\x12$\n\x06player\x18\x01 \x02(\x0b\x32\x14.WerewolfPlayerModel\x12\x10\n\x08killedBy\x18\x02 \x03(\x05\x12\x14\n\x0cidentityType\x18\x03 \x02(\x05\"d\n\x13WerewolfResultModel\x12$\n\x06player\x18\x01 \x02(\x0b\x32\x14.WerewolfPlayerModel\x12\x14\n\x0cidentityType\x18\x02 \x02(\x05\x12\x11\n\tdeathType\x18\x03 \x01(\x05\"_\n\x11\x41llotIdentityResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12\x14\n\x0cidentityType\x18\x02 \x02(\x05\x12\x11\n\tcountdown\x18\x03 \x02(\x05\x12\x10\n\x08\x63\x61mpType\x18\x04 \x01(\x05\"y\n\x18WerewolfNotificationResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12\x0f\n\x07userIds\x18\x02 \x03(\x03\x12\x1a\n\x12werewolfKingUserId\x18\x03 \x01(\x03\x12\x1f\n\x17\x62\x65\x61utifulWerewolfUserId\x18\x04 \x01(\x03\"[\n\x10WerewolfKillResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12#\n\x07players\x18\x02 \x03(\x0b\x32\x12.WerewolfKillModel\x12\x11\n\tcountdown\x18\x03 \x01(\x05\"d\n\x17ProphetNotificationResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12%\n\x07players\x18\x02 \x03(\x0b\x32\x14.WerewolfPlayerModel\x12\x11\n\tcountdown\x18\x03 \x02(\x05\"\x8c\x01\n\x11ProphetResultResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12$\n\x06player\x18\x02 \x02(\x0b\x32\x14.WerewolfPlayerModel\x12\x12\n\nisWerewolf\x18\x03 \x02(\x08\x12\x11\n\tcountdown\x18\x04 \x02(\x05\x12\x19\n\x11prophetResultType\x18\x05 \x01(\x05\"}\n\x1bWitchRescueNotificationResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12$\n\x06player\x18\x02 \x02(\x0b\x32\x14.WerewolfPlayerModel\x12\x11\n\tcountdown\x18\x03 \x02(\x05\x12\x14\n\x0cunableRescue\x18\x04 \x01(\x08\"h\n\x1bWitchPoisonNotificationResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12%\n\x07players\x18\x02 \x03(\x0b\x32\x14.WerewolfPlayerModel\x12\x11\n\tcountdown\x18\x03 \x02(\x05\"g\n\x1aPlayerDiesNotificationResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12\x0f\n\x07userIds\x18\x02 \x03(\x03\x12\x14\n\x0chasLastWords\x18\x03 \x01(\x08\x12\x11\n\tdeathType\x18\x04 \x01(\x05\"[\n\x0eHunterKillResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12%\n\x07players\x18\x02 \x03(\x0b\x32\x14.WerewolfPlayerModel\x12\x11\n\tcountdown\x18\x03 \x02(\x05\"a\n\x14VoteNotificationResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12%\n\x07players\x18\x02 \x03(\x0b\x32\x14.WerewolfPlayerModel\x12\x11\n\tcountdown\x18\x03 \x02(\x05\"\x89\x01\n\x11PlayerWaitingResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12\r\n\x05title\x18\x02 \x02(\t\x12\x0f\n\x07\x63ontent\x18\x03 \x02(\t\x12\x11\n\tcountdown\x18\x04 \x02(\x05\x12\x0c\n\x04type\x18\x05 \x01(\x05\x12\"\n\troundInfo\x18\x06 \x03(\x0b\x32\x0f.RoundInfoModel\"X\n\x0e\x44\x61yOrNightResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12\x13\n\x0b\x63urrentType\x18\x02 \x02(\x05\x12\r\n\x05turns\x18\x03 \x02(\x05\x12\x11\n\tbgmNumber\x18\x04 \x01(\x05\"\x8e\x03\n\x0eGameResultResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12(\n\nwerewolves\x18\x02 \x03(\x0b\x32\x14.WerewolfResultModel\x12$\n\x06person\x18\x03 \x03(\x0b\x32\x14.WerewolfResultModel\x12\x12\n\nexperience\x18\x04 \x02(\x01\x12\x0f\n\x07victory\x18\x05 \x02(\x08\x12\x11\n\tcountdown\x18\x06 \x02(\x05\x12\x14\n\x0cidentityType\x18\x07 \x01(\x05\x12$\n\x06lovers\x18\x08 \x03(\x0b\x32\x14.WerewolfResultModel\x12\x12\n\nwinnerType\x18\t \x01(\x05\x12%\n\x07hunters\x18\n \x03(\x0b\x32\x14.WerewolfResultModel\x12\"\n\x04nian\x18\x0b \x03(\x0b\x32\x14.WerewolfResultModel\x12#\n\x05loyal\x18\x0c \x03(\x0b\x32\x14.WerewolfResultModel\x12#\n\x05rebel\x18\r \x03(\x0b\x32\x14.WerewolfResultModel\"^\n\x15SpeakNotificationResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12\x0e\n\x06userId\x18\x02 \x02(\x03\x12\x11\n\tcountdown\x18\x03 \x02(\x05\x12\x11\n\tshowDelay\x18\x04 \x01(\x08\"\x1b\n\tLoginResp\x12\x0e\n\x06userId\x18\x01 \x02(\x03\"4\n\x10OffSiteLoginResp\x12\x0e\n\x06userId\x18\x01 \x02(\x03\x12\x10\n\x08\x64\x65viceId\x18\x02 \x02(\t\"\\\n\x1d\x41goraVoiceConditionChangeResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12\x13\n\x0bvoiceSwitch\x18\x02 \x02(\x08\x12\x15\n\rspeakingUsers\x18\x03 \x03(\x03\"$\n\x11\x41goraBanVoiceResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\"W\n\x12\x41goraFreeSpeakResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12\x17\n\x0f\x66reeSpeakUserId\x18\x02 \x02(\x03\x12\x17\n\x0f\x66reeSpeakStatus\x18\x03 \x02(\x05\"+\n\x18WerewolfKillCompleteResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\"O\n\x18\x41\x62normalNotificationResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12\x0f\n\x07\x63ontent\x18\x02 \x02(\t\x12\x11\n\tcountdown\x18\x03 \x02(\x05\"A\n\x14\x45xperienceChangeResp\x12\x12\n\nexperience\x18\x01 \x02(\x01\x12\x15\n\rexperienceBar\x18\x02 \x01(\x01\"\xcf\x03\n\x0cPresentModel\x12\x17\n\x0fpresentConfigId\x18\x01 \x02(\x03\x12\x12\n\npresentKey\x18\x02 \x02(\t\x12\x13\n\x0bpresentName\x18\x03 \x02(\t\x12\x0e\n\x06\x65\x66\x66\x65\x63t\x18\x04 \x01(\t\x12\x14\n\x0cpresentThumb\x18\x05 \x02(\t\x12\x0e\n\x06\x61mount\x18\x06 \x02(\x01\x12\x12\n\nfromUserId\x18\x07 \x02(\x03\x12\x10\n\x08toUserId\x18\x08 \x02(\x03\x12\r\n\x05\x63ount\x18\t \x02(\x05\x12\x12\n\npopularity\x18\n \x02(\x01\x12\x13\n\x0bprivateGive\x18\x0b \x02(\x08\x12\x1c\n\x14presentDynamicEffect\x18\x0c \x01(\x05\x12\x12\n\npresentGif\x18\r \x01(\t\x12\x0f\n\x07seatNum\x18\x0e \x01(\x05\x12\r\n\x05\x63ombo\x18\x0f \x01(\x08\x12\x10\n\x08\x63omboNum\x18\x10 \x01(\x05\x12\x13\n\x0b\x66romSeatNum\x18\x11 \x01(\x05\x12\x14\n\x0c\x66romNickName\x18\x12 \x01(\t\x12\x12\n\ntoNickName\x18\x13 \x01(\t\x12#\n\nawardModel\x18\x14 \x03(\x0b\x32\x0f.AwardInfoModel\x12!\n\x19presentSceneDynamicEffect\x18\x15 \x01(\x05\"\x84\x01\n\rUserInfoModel\x12\x0e\n\x06nickId\x18\x01 \x01(\x03\x12\x10\n\x08nickName\x18\x02 \x01(\t\x12\x11\n\theadThumb\x18\x03 \x01(\t\x12\x0f\n\x07vipType\x18\x04 \x01(\x05\x12\x14\n\x0cnickIdPretty\x18\x05 \x01(\x08\x12\x17\n\x0fpopularityLevel\x18\x06 \x01(\x05\"J\n\x0e\x41wardInfoModel\x12\x11\n\tawardName\x18\x01 \x02(\t\x12\x12\n\nawardCount\x18\x02 \x02(\x05\x12\x11\n\tawardUnit\x18\x03 \x01(\t\"\xd6\x03\n\x0ePrivateMessage\x12\x0c\n\x04\x66rom\x18\x01 \x02(\t\x12\n\n\x02to\x18\x02 \x02(\t\x12\x11\n\tgroupType\x18\x03 \x02(\x05\x12\x11\n\tmessageId\x18\x04 \x02(\t\x12\x11\n\tmessageOn\x18\x05 \x02(\x03\x12\x0f\n\x07msgType\x18\x06 \x01(\x05\x12\x12\n\nmsgSubType\x18\x07 \x01(\x05\x12\x0e\n\x06length\x18\x08 \x01(\x05\x12\x0b\n\x03url\x18\t \x01(\t\x12\x0f\n\x07\x63ontent\x18\n \x02(\t\x12\x0e\n\x06status\x18\x0b \x01(\x05\x12\x13\n\x0bunsupported\x18\x0c \x01(\t\x12\r\n\x05\x65xtra\x18\r \x01(\x0c\x12\x1e\n\x07present\x18\x0e \x01(\x0b\x32\r.PresentModel\x12,\n\x0egameRoomInvite\x18\x0f \x01(\x0b\x32\x14.GameRoomInviteModel\x12\x14\n\x0c\x63hatBubbleId\x18\x10 \x01(\x03\x12\x0f\n\x07vipType\x18\x11 \x01(\x05\x12\x13\n\x0bheadFrameId\x18\x12 \x01(\x05\x12\x16\n\x0e\x63ontentUserIds\x18\x13 \x03(\x03\x12$\n\x0c\x66romUserInfo\x18\x14 \x01(\x0b\x32\x0e.UserInfoModel\x12\"\n\ntoUserInfo\x18\x15 \x01(\x0b\x32\x0e.UserInfoModel\"1\n\x1cPrivateMessageReadConfirmReq\x12\x11\n\tmessageId\x18\x01 \x02(\t\")\n\x14GetPrivateMessageReq\x12\x11\n\tmessageId\x18\x01 \x02(\t\"@\n\x15GetPrivateMessageResp\x12\'\n\x0eprivateMessage\x18\x01 \x02(\x0b\x32\x0f.PrivateMessage\"\xc5\x01\n\x13GameRoomInviteModel\x12\x12\n\nfromUserId\x18\x01 \x02(\x03\x12\x10\n\x08toUserId\x18\x02 \x02(\x03\x12\x10\n\x08roomName\x18\x03 \x02(\t\x12\x0f\n\x07roomKey\x18\x04 \x02(\t\x12\x13\n\x0bsendContent\x18\x05 \x02(\t\x12\x16\n\x0ereceiveContent\x18\x06 \x02(\t\x12\x0f\n\x07roomNum\x18\x07 \x01(\x05\x12\x14\n\x0cgameRoomType\x18\x08 \x01(\x05\x12\x11\n\troomTheme\x18\t \x01(\x05\"H\n\x12UserFriendsAddResp\x12!\n\tuserBasic\x18\x01 \x02(\x0b\x32\x0e.UserBasicResp\x12\x0f\n\x07version\x18\x02 \x02(\x03\"5\n\x12UserFriendsDelResp\x12\x0e\n\x06userId\x18\x01 \x02(\x03\x12\x0f\n\x07version\x18\x02 \x02(\x03\"H\n\x12UserInfoChangeResp\x12!\n\tuserBasic\x18\x01 \x02(\x0b\x32\x0e.UserBasicResp\x12\x0f\n\x07version\x18\x02 \x02(\x03\"\xab\x01\n\rUserBasicResp\x12\x0e\n\x06userId\x18\x01 \x02(\x03\x12\x0e\n\x06nickId\x18\x02 \x02(\x03\x12\x10\n\x08nickName\x18\x03 \x02(\t\x12\x0e\n\x06gender\x18\x04 \x02(\x05\x12\x13\n\x0bheaderThmnb\x18\x05 \x02(\t\x12\x0b\n\x03vip\x18\x06 \x02(\x08\x12\x11\n\tsignature\x18\x07 \x02(\t\x12\r\n\x05level\x18\x08 \x01(\x05\x12\x14\n\x0coriginalName\x18\t \x01(\t\"*\n\x11\x46riendRequestResp\x12\x15\n\rrequestedFrom\x18\x01 \x02(\x03\"m\n\rSystemMessage\x12\x0f\n\x07msgType\x18\x01 \x02(\x05\x12\x12\n\nmsgSubType\x18\x02 \x01(\x05\x12\x0f\n\x07\x63ontent\x18\x03 \x02(\t\x12\x11\n\tmessageOn\x18\x04 \x02(\x03\x12\x13\n\x0bunsupported\x18\x05 \x01(\t\"$\n\x11\x42\x61lanceChangeResp\x12\x0f\n\x07\x62\x61lance\x18\x01 \x02(\x01\"c\n\x12VyingIdentityModel\x12\x14\n\x0cidentityType\x18\x01 \x02(\x05\x12\x12\n\ntotalCount\x18\x02 \x02(\x05\x12\x14\n\x0c\x63urrentCount\x18\x03 \x02(\x05\x12\r\n\x05price\x18\x04 \x02(\x01\"`\n\x11VyingIdentityResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12\'\n\nidentities\x18\x02 \x03(\x0b\x32\x13.VyingIdentityModel\x12\x11\n\tcountdown\x18\x03 \x02(\x05\",\n\x19GameStartNotificationResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\"B\n\x1aGameRoomPasswordChangeResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12\x13\n\x0bsetPassword\x18\x02 \x02(\x08\"\\\n\x0f\x43upidChooseResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12%\n\x07players\x18\x02 \x03(\x0b\x32\x14.WerewolfPlayerModel\x12\x11\n\tcountdown\x18\x03 \x02(\x05\"\\\n\x0f\x43upidResultResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12%\n\x07players\x18\x02 \x03(\x0b\x32\x14.WerewolfPlayerModel\x12\x11\n\tcountdown\x18\x03 \x02(\x05\">\n\x18SergeantNotificationResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12\x11\n\tcountdown\x18\x02 \x02(\x05\"]\n\x10VoteSergeantResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12%\n\x07players\x18\x02 \x03(\x0b\x32\x14.WerewolfPlayerModel\x12\x11\n\tcountdown\x18\x03 \x02(\x05\"e\n\x17PromotionToSergeantResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12&\n\x08sergeant\x18\x02 \x02(\x0b\x32\x14.WerewolfPlayerModel\x12\x11\n\tcountdown\x18\x03 \x02(\x05\"_\n\x12SergeantChangeResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12%\n\x07players\x18\x02 \x03(\x0b\x32\x14.WerewolfPlayerModel\x12\x11\n\tcountdown\x18\x03 \x02(\x05\"\x91\x01\n\x18SergeantChangeResultResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12)\n\x0boldSergeant\x18\x02 \x02(\x0b\x32\x14.WerewolfPlayerModel\x12&\n\x08sergeant\x18\x03 \x02(\x0b\x32\x14.WerewolfPlayerModel\x12\x11\n\tcountdown\x18\x04 \x02(\x05\"A\n\x1dSergeantElectNotificationResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12\x0f\n\x07userIds\x18\x02 \x03(\x03\"Q\n\x19SergeantRoundCompleteResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12\x13\n\x0bhasSergeant\x18\x02 \x02(\x08\x12\x0e\n\x06hasBGM\x18\x03 \x01(\x08\"(\n\x15SergeantVoteRoundResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\"#\n\x10VoteCompleteResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\"!\n\x0e\x43upidRoundResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\"F\n\x0eGuildApplyResp\x12\x0e\n\x06userId\x18\x01 \x02(\x03\x12\x13\n\x0bheaderThumb\x18\x02 \x02(\t\x12\x0f\n\x07\x63ontent\x18\x03 \x02(\t\"R\n\x15GuildMemberChangeResp\x12\x0e\n\x06userId\x18\x01 \x02(\x03\x12\x0f\n\x07guildId\x18\x02 \x02(\x03\x12\x18\n\x10memberChangeType\x18\x03 \x02(\x05\"$\n\x11GuildDissolveResp\x12\x0f\n\x07guildId\x18\x01 \x02(\x03\"e\n\x1aGuildMemberTitleChangeResp\x12\x0f\n\x07guildId\x18\x01 \x02(\x03\x12\x13\n\x0btitleNumber\x18\x02 \x02(\x03\x12\x11\n\ttitleName\x18\x03 \x02(\t\x12\x0e\n\x06userId\x18\x04 \x02(\x03\"f\n\x12GetGuildMessageReq\x12\x0e\n\x06userId\x18\x01 \x02(\x03\x12\x0f\n\x07guildId\x18\x02 \x02(\x03\x12\x0e\n\x06offset\x18\x03 \x02(\x03\x12\x10\n\x08loadMore\x18\x04 \x02(\x08\x12\r\n\x05\x63ount\x18\x05 \x02(\x05\"@\n\x13GetGuildMessageResp\x12)\n\rguildMessages\x18\x01 \x03(\x0b\x32\x12.GuildMessageModel\"L\n\x11GuildMessageModel\x12\x0e\n\x06offset\x18\x01 \x02(\x03\x12\'\n\x0eprivateMessage\x18\x02 \x02(\x0b\x32\x0f.PrivateMessage\"k\n\x17GuildOfflineMessageResp\x12)\n\rguildMessages\x18\x01 \x03(\x0b\x32\x12.GuildMessageModel\x12\x13\n\x0bunReadCount\x18\x02 \x02(\x05\x12\x10\n\x08redPoint\x18\x03 \x01(\x08\"e\n\x15GuildRecruitCheckResp\x12\x0e\n\x06status\x18\x01 \x02(\x05\x12\x14\n\x0crecruitSeqId\x18\x02 \x02(\x03\x12\x15\n\rpublishUserId\x18\x03 \x02(\x03\x12\x0f\n\x07guildId\x18\x04 \x02(\x03\"R\n\x16LogoutNotificationResp\x12\x12\n\nlogoutType\x18\x01 \x02(\x05\x12\x0e\n\x06reason\x18\x02 \x01(\t\x12\x14\n\x0c\x63sPermission\x18\x03 \x01(\x08\"T\n\x19GameRoomSettingChangeResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12\x13\n\x0bsetPassword\x18\x02 \x02(\x08\x12\x11\n\troomLevel\x18\x03 \x02(\x05\"&\n\x14GetOfflineMessageReq\x12\x0e\n\x06sseqNo\x18\x01 \x01(\x03\"\'\n\x15GetOfflineMessageResp\x12\x0e\n\x06sseqNo\x18\x01 \x01(\x03\"h\n\x1bSelfDestructionNotification\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12%\n\x07players\x18\x02 \x03(\x0b\x32\x14.WerewolfPlayerModel\x12\x11\n\tcountdown\x18\x03 \x02(\x05\"b\n\x15GuardNotificationResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12%\n\x07players\x18\x02 \x03(\x0b\x32\x14.WerewolfPlayerModel\x12\x11\n\tcountdown\x18\x03 \x02(\x05\"\x1f\n\x0cSpeakEndResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\"D\n\x18SelfDestructionStartResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12\x17\n\x0f\x64\x65structionType\x18\x02 \x01(\x05\")\n\x16SelfDestructionEndResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\"c\n\x18HunterKillInTheNightResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12#\n\x07players\x18\x02 \x03(\x0b\x32\x12.WerewolfKillModel\x12\x11\n\tcountdown\x18\x03 \x01(\x05\":\n\x16HunterNotificationResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12\x0f\n\x07userIds\x18\x02 \x03(\x03\"H\n\x11VipInfoChangeResp\x12\x0e\n\x06userId\x18\x01 \x02(\x03\x12\x0f\n\x07vipType\x18\x02 \x02(\x05\x12\x12\n\nexpireDate\x18\x03 \x02(\x03\"4\n\x13UserLevelChangeResp\x12\x0e\n\x06userId\x18\x01 \x02(\x03\x12\r\n\x05level\x18\x02 \x02(\x05\"-\n\x08VoteInfo\x12!\n\nvoteModels\x18\x01 \x03(\x0b\x32\r.UserVoteInfo\";\n\x0cUserVoteInfo\x12\x14\n\x0cuserSeatNums\x18\x01 \x03(\x05\x12\x15\n\rvoteToSeatNum\x18\x02 \x01(\x05\"\xb0\x01\n\x1fRecreationRoomSettingChangeResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12\x10\n\x08roomName\x18\x02 \x01(\t\x12\x13\n\x0bsetPassword\x18\x03 \x01(\x08\x12\r\n\x05tagId\x18\x04 \x01(\x05\x12\x15\n\rbackgroundUrl\x18\x05 \x01(\t\x12\x16\n\x0e\x62\x61\x63kgroundType\x18\x06 \x01(\x05\x12\x17\n\x0f\x62\x61\x63kgroundUrlV2\x18\x07 \x01(\t\"A\n\x1eRecreationRoomOwnerChangeModel\x12\x0e\n\x06userId\x18\x01 \x02(\x03\x12\x0f\n\x07seatNum\x18\x02 \x02(\x05\"\xad\x01\n\x1dRecreationRoomOwnerChangeResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12\x10\n\x08password\x18\x02 \x01(\t\x12\x32\n\tlastOwner\x18\x03 \x02(\x0b\x32\x1f.RecreationRoomOwnerChangeModel\x12\x35\n\x0c\x63urrentOwner\x18\x04 \x02(\x0b\x32\x1f.RecreationRoomOwnerChangeModel\"&\n\x13RecreationRoomHeart\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\"?\n\x1bHunterRoundNotificationResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12\x0f\n\x07isStart\x18\x02 \x02(\x08\"A\n\x0fTruthOrDareResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12\x0c\n\x04type\x18\x02 \x02(\x05\x12\x0f\n\x07\x63ontent\x18\x03 \x02(\t\"/\n\x0eRoomSoundsResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12\x0c\n\x04type\x18\x02 \x02(\x05\"J\n\x12PenguinFrozenModel\x12$\n\x06player\x18\x01 \x02(\x0b\x32\x14.WerewolfPlayerModel\x12\x0e\n\x06\x66rozen\x18\x02 \x02(\x08\"]\n\x11PenguinFrozenResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12$\n\x07players\x18\x02 \x03(\x0b\x32\x13.PenguinFrozenModel\x12\x11\n\tcountdown\x18\x03 \x02(\x05\"o\n\x12WerewolfFrozenResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12\x0f\n\x07\x63ontent\x18\x02 \x02(\t\x12\x0f\n\x07seatNum\x18\x03 \x02(\x05\x12\x13\n\x0bheaderThumb\x18\x04 \x02(\t\x12\x11\n\tcountdown\x18\x05 \x02(\x05\"X\n\x0c\x43ockDiesResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12\x13\n\x0bheaderThumb\x18\x02 \x02(\t\x12\x0f\n\x07seatNum\x18\x03 \x02(\x05\x12\x11\n\tcountdown\x18\x04 \x02(\x05\"@\n\x0c\x42\x65\x61rRoarResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12\x0c\n\x04roar\x18\x02 \x02(\x08\x12\x11\n\tcountdown\x18\x03 \x02(\x05\"0\n\x1dPenguinFrozenNotificationResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\"E\n\x0fTreasureBoxResp\x12\x0e\n\x06\x62oxKey\x18\x01 \x02(\t\x12\x0f\n\x07roomKey\x18\x02 \x02(\t\x12\x11\n\tcountdown\x18\x03 \x02(\x05\"b\n\x0cRoomInfoResp\x12\x0e\n\x06userId\x18\x01 \x02(\x03\x12\x0c\n\x04type\x18\x02 \x02(\x05\x12\x0f\n\x07roomKey\x18\x03 \x01(\t\x12\x10\n\x08roomType\x18\x04 \x01(\x05\x12\x11\n\troomTheme\x18\x05 \x01(\x05\"I\n\x11MatchingUserModel\x12\x0e\n\x06userId\x18\x01 \x02(\x03\x12\x13\n\x0bheaderThumb\x18\x02 \x02(\t\x12\x0f\n\x07isReady\x18\x03 \x02(\x08\"\x89\x01\n\x10MatchingInfoResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12\x12\n\nreadyCount\x18\x02 \x02(\x05\x12\x12\n\ntotalCount\x18\x03 \x02(\x05\x12\x11\n\tcountdown\x18\x04 \x02(\x05\x12)\n\rmatchingUsers\x18\x05 \x03(\x0b\x32\x12.MatchingUserModel\"G\n\x13QualifyingReadyResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12\x0e\n\x06userId\x18\x02 \x02(\x03\x12\x0f\n\x07readied\x18\x03 \x02(\x05\"/\n\x17RematchNotificationResp\x12\x14\n\x0cgameRoomType\x18\x01 \x02(\x05\"\xbd\x01\n\x0fRoomMemberModel\x12\x0e\n\x06userId\x18\x01 \x02(\x03\x12\x10\n\x08nickName\x18\x02 \x02(\t\x12\x13\n\x0bheaderThumb\x18\x03 \x02(\t\x12\r\n\x05level\x18\x04 \x02(\x05\x12\x0f\n\x07seatNum\x18\x05 \x02(\x05\x12\x17\n\x0fpopularityLevel\x18\x06 \x01(\x05\x12\x0f\n\x07vipType\x18\x07 \x01(\x05\x12\x14\n\x0cmicrophoneId\x18\x08 \x01(\x05\x12\x13\n\x0bheadFrameId\x18\t \x01(\x05\"\x89\x01\n\x16QualifyingRoomInfoResp\x12\x0f\n\x07roomNum\x18\x01 \x02(\x03\x12\x10\n\x08roomName\x18\x02 \x02(\t\x12\x0f\n\x07roomKey\x18\x03 \x02(\t\x12\x14\n\x0cgameRoomType\x18\x04 \x02(\x05\x12%\n\x0broomMembers\x18\x05 \x03(\x0b\x32\x10.RoomMemberModel\"f\n\x12TinyTierChangeResp\x12\x12\n\ntinyTierId\x18\x01 \x02(\x05\x12\x12\n\ntinyTierUp\x18\x02 \x02(\x08\x12\x14\n\x0ctinyTierFrom\x18\x03 \x02(\t\x12\x12\n\ntinyTierTo\x18\x04 \x02(\t\"<\n\x14UserCreditChangeResp\x12\x0e\n\x06userId\x18\x01 \x02(\x03\x12\x14\n\x0c\x63reditPoints\x18\x02 \x02(\x05\"\"\n\x0fSystemToastResp\x12\x0f\n\x07\x63ontent\x18\x01 \x02(\t\"Y\n\rBigEmojiModel\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12\x13\n\x0b\x66romSeatNum\x18\x02 \x02(\x05\x12\x11\n\ttoSeatNum\x18\x03 \x02(\x05\x12\x0f\n\x07\x65mojiId\x18\x04 \x02(\x05\"/\n\x0b\x42igEmojiReq\x12 \n\x08\x62igEmoji\x18\x01 \x02(\x0b\x32\x0e.BigEmojiModel\"0\n\x0c\x42igEmojiResp\x12 \n\x08\x62igEmoji\x18\x01 \x02(\x0b\x32\x0e.BigEmojiModel\"\x90\x01\n\tSongModel\x12\x0e\n\x06songId\x18\x01 \x02(\x05\x12\x0c\n\x04name\x18\x02 \x02(\t\x12\x13\n\x0b\x61rtistNames\x18\x03 \x03(\t\x12\x0e\n\x06imgUrl\x18\x04 \x02(\t\x12\x0f\n\x07\x66ileUrl\x18\x05 \x02(\t\x12\x0b\n\x03md5\x18\x06 \x02(\t\x12\x10\n\x08\x64uration\x18\x07 \x02(\x05\x12\x10\n\x08lyricUrl\x18\x08 \x01(\t\"a\n\rSongInfoModel\x12\x12\n\nuniquedKey\x18\x01 \x02(\t\x12\x18\n\x04song\x18\x02 \x02(\x0b\x32\n.SongModel\x12\x0e\n\x06userId\x18\x03 \x02(\x03\x12\x12\n\nplayStatus\x18\x04 \x01(\x05\"\x7f\n\x12SongListChangeResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12\x13\n\x0buniquedKeys\x18\x02 \x03(\t\x12\x0e\n\x06option\x18\x03 \x02(\x05\x12 \n\x08songInfo\x18\x04 \x02(\x0b\x32\x0e.SongInfoModel\x12\x11\n\ttimeStamp\x18\x05 \x02(\x03\"7\n\x0eSwitchSongResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12\x14\n\x0cswitchStatus\x18\x02 \x02(\x05\"C\n\x0eSongStatusResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12 \n\x08songInfo\x18\x02 \x02(\x0b\x32\x0e.SongInfoModel\"U\n\x0eSingNotifyResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12\x10\n\x08waitTime\x18\x02 \x02(\x05\x12 \n\x08songInfo\x18\x03 \x02(\x0b\x32\x0e.SongInfoModel\"=\n\x17RichManNotificationResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12\x11\n\tcountdown\x18\x02 \x02(\x05\"W\n\x0cNianKillResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12#\n\x07players\x18\x02 \x03(\x0b\x32\x12.WerewolfKillModel\x12\x11\n\tcountdown\x18\x03 \x01(\x05\"8\n\x14NianNotificationResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12\x0f\n\x07userIds\x18\x02 \x03(\x03\"`\n\x14NianKillCompleteResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12$\n\x06killed\x18\x02 \x02(\x0b\x32\x14.WerewolfPlayerModel\x12\x11\n\tcountdown\x18\x03 \x02(\x05\"I\n\x14PetExploreStatusResp\x12\x0e\n\x06userId\x18\x01 \x02(\x03\x12\x0e\n\x06status\x18\x02 \x02(\x05\x12\x11\n\ttimeStamp\x18\x03 \x02(\x03\"@\n\x0ePropUpdateResp\x12\x0e\n\x06userId\x18\x01 \x02(\x03\x12\x0f\n\x07propNum\x18\x02 \x02(\x05\x12\r\n\x05\x63ount\x18\x03 \x02(\x05\"H\n\x0fKillConfirmResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12$\n\x06killed\x18\x02 \x02(\x0b\x32\x14.WerewolfPlayerModel\"N\n\x15HunterKillConfirmResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12$\n\x06killed\x18\x02 \x02(\x0b\x32\x14.WerewolfPlayerModel\"w\n\x13\x46ireworksAwardModel\x12\x0e\n\x06userId\x18\x01 \x02(\x03\x12\x0f\n\x07seatNum\x18\x02 \x02(\x05\x12\x1b\n\x13\x61wardAttributeValue\x18\x03 \x02(\x05\x12\r\n\x05props\x18\x04 \x02(\t\x12\x13\n\x0bheaderThumb\x18\x05 \x02(\t\"\x84\x01\n\x0e\x46ireworksModel\x12\x14\n\x0c\x66ireworksNum\x18\x01 \x02(\x05\x12$\n\x06\x61wards\x18\x02 \x03(\x0b\x32\x14.FireworksAwardModel\x12\x1a\n\x12\x61wardAttributeName\x18\x03 \x01(\t\x12\x1a\n\x12\x61wardAttributeIcon\x18\x04 \x01(\t\"D\n\x11GuildTransferResp\x12\x0f\n\x07guildId\x18\x01 \x02(\x03\x12\x10\n\x08recordId\x18\x02 \x02(\x03\x12\x0c\n\x04type\x18\x03 \x02(\x05\"n\n!BeautifulWerewolfNotificationResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12%\n\x07players\x18\x02 \x03(\x0b\x32\x14.WerewolfPlayerModel\x12\x11\n\tcountdown\x18\x03 \x02(\x05\"_\n%RecreationGameRoomBoardTextChangeResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12\x12\n\nchangeType\x18\x02 \x02(\x05\x12\x11\n\tboardText\x18\x03 \x01(\t\"l\n\x1cRecreationRoomSeatChangeResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12\x0e\n\x06userId\x18\x02 \x01(\x03\x12\x13\n\x0blastSeatNum\x18\x03 \x02(\x05\x12\x16\n\x0e\x63urrentSeatNum\x18\x04 \x02(\x05\"H\n\x10NickIdChangeResp\x12\x0e\n\x06userId\x18\x01 \x02(\x03\x12\x0e\n\x06nickId\x18\x02 \x02(\x03\x12\x14\n\x0cnickIdPretty\x18\x03 \x01(\x08\"Z\n\x1cRecreationRoomSeatInviteResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12\x15\n\rinvitedUserId\x18\x02 \x02(\x03\x12\x12\n\nexpireTime\x18\x03 \x02(\x03\"u\n\x1aRecreationRoomUserMuteResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12\x13\n\x0bmutedUserId\x18\x02 \x02(\x03\x12\x15\n\rmuteCountdown\x18\x03 \x02(\x03\x12\x1a\n\x12\x62\x65\x66oreMuteUserType\x18\x04 \x01(\x05\"T\n\x17\x41udienceCountChangeResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12\x15\n\raudienceCount\x18\x02 \x02(\x05\x12\x11\n\ttimestamp\x18\x03 \x02(\x03\"U\n\x11\x41udienceApplyResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12\x0e\n\x06userId\x18\x02 \x02(\x03\x12\x0c\n\x04type\x18\x03 \x02(\x05\x12\x11\n\ttimestamp\x18\x04 \x02(\x03\"U\n\x1bWerewolfPlayerIdentityModel\x12\x0e\n\x06userId\x18\x01 \x02(\x03\x12\x14\n\x0cidentityType\x18\x02 \x01(\x05\x12\x10\n\x08\x63\x61mpType\x18\x03 \x01(\x05\"Z\n\x18IdentityNotificationResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12-\n\x07players\x18\x02 \x03(\x0b\x32\x1c.WerewolfPlayerIdentityModel\"o\n\x12KillInTheNightResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12\x10\n\x08killType\x18\x02 \x02(\x05\x12#\n\x07players\x18\x03 \x03(\x0b\x32\x12.WerewolfKillModel\x12\x11\n\tcountdown\x18\x04 \x01(\x05\"n\n!ImperialConcubineNotificationResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12%\n\x07players\x18\x02 \x03(\x0b\x32\x14.WerewolfPlayerModel\x12\x11\n\tcountdown\x18\x03 \x02(\x05\"w\n\x18MinisterNotificationResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12%\n\x07players\x18\x02 \x03(\x0b\x32\x14.WerewolfPlayerModel\x12\x10\n\x08sameCamp\x18\x03 \x02(\x08\x12\x11\n\tcountdown\x18\x04 \x02(\x05\"`\n\x13SpyNotificationResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12%\n\x07players\x18\x02 \x03(\x0b\x32\x14.WerewolfSelectModel\x12\x11\n\tcountdown\x18\x03 \x01(\x05\"`\n\x13WerewolfSelectModel\x12$\n\x06player\x18\x01 \x02(\x0b\x32\x14.WerewolfPlayerModel\x12\x12\n\nselectedBy\x18\x02 \x03(\x05\x12\x0f\n\x07\x64isable\x18\x03 \x01(\x08\"k\n\rSpyResultResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12$\n\x06player\x18\x02 \x02(\x0b\x32\x14.WerewolfPlayerModel\x12\x11\n\tcountdown\x18\x03 \x02(\x05\x12\x10\n\x08\x63\x61mpType\x18\x04 \x02(\x05\"\x95\x01\n\x17\x45mperorNotificationResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12,\n\x0epreviousPlayer\x18\x02 \x02(\x0b\x32\x14.WerewolfPlayerModel\x12(\n\nnextPlayer\x18\x03 \x02(\x0b\x32\x14.WerewolfPlayerModel\x12\x11\n\tcountdown\x18\x04 \x02(\x05\".\n\x0eRoundInfoModel\x12\x0c\n\x04type\x18\x01 \x02(\x05\x12\x0e\n\x06status\x18\x02 \x02(\x05\"0\n\x1d\x45mperorChooseNotificationResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\"3\n\x12SelectCompleteResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12\x0c\n\x04type\x18\x02 \x02(\x05\"L\n\x15RoundNotificationResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12\x11\n\troundType\x18\x02 \x02(\x05\x12\x0f\n\x07isStart\x18\x03 \x02(\x08\"P\n\x16RoomSeatApplyCountResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12\x12\n\napplyCount\x18\x02 \x02(\x05\x12\x11\n\ttimestamp\x18\x03 \x02(\x03\"P\n\x17\x41udiencePresentHeadResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12\x11\n\theadThumb\x18\x02 \x03(\t\x12\x11\n\ttimestamp\x18\x03 \x02(\x03\"a\n\x17\x41nchorManagerChangeResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12\x0e\n\x06userId\x18\x02 \x02(\x03\x12\x12\n\nchangeType\x18\x03 \x02(\x05\x12\x11\n\ttimestamp\x18\x04 \x02(\x03\"A\n\x14RoomBulletScreenResp\x12\x10\n\x08nickName\x18\x01 \x02(\t\x12\x17\n\x0fpopularityLevel\x18\x02 \x02(\x05\"O\n\x1bGoldenMouseNotificationResp\x12\x0f\n\x07roomKey\x18\x01 \x02(\t\x12\x0c\n\x04gold\x18\x02 \x02(\x05\x12\x11\n\tcountdown\x18\x03 \x02(\x05\x42\x1c\n\x1a\x63om.c2vl.kgamebox.protobuf')
)
_sym_db.RegisterFileDescriptor(DESCRIPTOR)




_REQUESTHEADER = _descriptor.Descriptor(
  name='RequestHeader',
  full_name='RequestHeader',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='sid', full_name='RequestHeader.sid', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='cid', full_name='RequestHeader.cid', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='cseqNo', full_name='RequestHeader.cseqNo', index=2,
      number=3, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='sseqNo', full_name='RequestHeader.sseqNo', index=3,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='reqTime', full_name='RequestHeader.reqTime', index=4,
      number=5, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='offMsgType', full_name='RequestHeader.offMsgType', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='signkey', full_name='RequestHeader.signkey', index=6,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='signVersion', full_name='RequestHeader.signVersion', index=7,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='deviceType', full_name='RequestHeader.deviceType', index=8,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='nonce', full_name='RequestHeader.nonce', index=9,
      number=11, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='captcha', full_name='RequestHeader.captcha', index=10,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='sms', full_name='RequestHeader.sms', index=11,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='pcode', full_name='RequestHeader.pcode', index=12,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='forwardSign', full_name='RequestHeader.forwardSign', index=13,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=19,
  serialized_end=278,
)


_CAPTCHA = _descriptor.Descriptor(
  name='Captcha',
  full_name='Captcha',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='captchaId', full_name='Captcha.captchaId', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='validate', full_name='Captcha.validate', index=1,
      number=2, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='extVerificationType', full_name='Captcha.extVerificationType', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=280,
  serialized_end=355,
)


_REQUEST = _descriptor.Descriptor(
  name='Request',
  full_name='Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='Request.header', index=0,
      number=1, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='body', full_name='Request.body', index=1,
      number=2, type=12, cpp_type=9, label=2,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=357,
  serialized_end=412,
)


_RESPONSEHEADER = _descriptor.Descriptor(
  name='ResponseHeader',
  full_name='ResponseHeader',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='sid', full_name='ResponseHeader.sid', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='cid', full_name='ResponseHeader.cid', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='cseqNo', full_name='ResponseHeader.cseqNo', index=2,
      number=3, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='sseqNo', full_name='ResponseHeader.sseqNo', index=3,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='respTime', full_name='ResponseHeader.respTime', index=4,
      number=5, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='code', full_name='ResponseHeader.code', index=5,
      number=6, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='offMsgType', full_name='ResponseHeader.offMsgType', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=414,
  serialized_end=540,
)


_RESPONSE = _descriptor.Descriptor(
  name='Response',
  full_name='Response',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='Response.header', index=0,
      number=1, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='body', full_name='Response.body', index=1,
      number=2, type=12, cpp_type=9, label=2,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=542,
  serialized_end=599,
)


_ERRORRESP = _descriptor.Descriptor(
  name='ErrorResp',
  full_name='ErrorResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='errorCode', full_name='ErrorResp.errorCode', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='errorMsg', full_name='ErrorResp.errorMsg', index=1,
      number=2, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='detail', full_name='ErrorResp.detail', index=2,
      number=3, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='errorParams', full_name='ErrorResp.errorParams', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='errorNotificationType', full_name='ErrorResp.errorNotificationType', index=4,
      number=5, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=602,
  serialized_end=731,
)


_ERRORPARAM = _descriptor.Descriptor(
  name='ErrorParam',
  full_name='ErrorParam',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='ErrorParam.key', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='value', full_name='ErrorParam.value', index=1,
      number=2, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=733,
  serialized_end=773,
)


_SEQSYNCREQ = _descriptor.Descriptor(
  name='SeqSyncReq',
  full_name='SeqSyncReq',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='device', full_name='SeqSyncReq.device', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=775,
  serialized_end=803,
)


_LOGINREQ = _descriptor.Descriptor(
  name='LoginReq',
  full_name='LoginReq',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='accessToken', full_name='LoginReq.accessToken', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='userId', full_name='LoginReq.userId', index=1,
      number=2, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='device', full_name='LoginReq.device', index=2,
      number=3, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=805,
  serialized_end=868,
)


_GAMEROOMUSERCHANGERESP = _descriptor.Descriptor(
  name='GameRoomUserChangeResp',
  full_name='GameRoomUserChangeResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='GameRoomUserChangeResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='userId', full_name='GameRoomUserChangeResp.userId', index=1,
      number=2, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='nickName', full_name='GameRoomUserChangeResp.nickName', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='headerThumb', full_name='GameRoomUserChangeResp.headerThumb', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='level', full_name='GameRoomUserChangeResp.level', index=4,
      number=5, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='seatNum', full_name='GameRoomUserChangeResp.seatNum', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='changeType', full_name='GameRoomUserChangeResp.changeType', index=6,
      number=7, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='content', full_name='GameRoomUserChangeResp.content', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='password', full_name='GameRoomUserChangeResp.password', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='popularityLevel', full_name='GameRoomUserChangeResp.popularityLevel', index=9,
      number=10, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='roomLevel', full_name='GameRoomUserChangeResp.roomLevel', index=10,
      number=11, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='vipType', full_name='GameRoomUserChangeResp.vipType', index=11,
      number=12, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='microphoneId', full_name='GameRoomUserChangeResp.microphoneId', index=12,
      number=13, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='headFrameId', full_name='GameRoomUserChangeResp.headFrameId', index=13,
      number=14, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='gender', full_name='GameRoomUserChangeResp.gender', index=14,
      number=15, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='idleSeatChangeType', full_name='GameRoomUserChangeResp.idleSeatChangeType', index=15,
      number=16, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=871,
  serialized_end=1202,
)


_GAMEROOMREADYRESP = _descriptor.Descriptor(
  name='GameRoomReadyResp',
  full_name='GameRoomReadyResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='GameRoomReadyResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='userId', full_name='GameRoomReadyResp.userId', index=1,
      number=2, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='userStatus', full_name='GameRoomReadyResp.userStatus', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='readied', full_name='GameRoomReadyResp.readied', index=3,
      number=4, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1204,
  serialized_end=1293,
)


_GAMEROOMSEATSWITCHRESP = _descriptor.Descriptor(
  name='GameRoomSeatSwitchResp',
  full_name='GameRoomSeatSwitchResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='GameRoomSeatSwitchResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='seatNum', full_name='GameRoomSeatSwitchResp.seatNum', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='seatStatus', full_name='GameRoomSeatSwitchResp.seatStatus', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1295,
  serialized_end=1373,
)


_GAMEROOMMESSAGE = _descriptor.Descriptor(
  name='GameRoomMessage',
  full_name='GameRoomMessage',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='from', full_name='GameRoomMessage.from', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='fromType', full_name='GameRoomMessage.fromType', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='GameRoomMessage.roomKey', index=2,
      number=3, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='msgType', full_name='GameRoomMessage.msgType', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='msgSubType', full_name='GameRoomMessage.msgSubType', index=4,
      number=5, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='content', full_name='GameRoomMessage.content', index=5,
      number=6, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='messageOn', full_name='GameRoomMessage.messageOn', index=6,
      number=7, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='unsupported', full_name='GameRoomMessage.unsupported', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='messageId', full_name='GameRoomMessage.messageId', index=8,
      number=9, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='present', full_name='GameRoomMessage.present', index=9,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='chatBubbleId', full_name='GameRoomMessage.chatBubbleId', index=10,
      number=11, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='vipType', full_name='GameRoomMessage.vipType', index=11,
      number=12, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='voteInfo', full_name='GameRoomMessage.voteInfo', index=12,
      number=13, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='fireworks', full_name='GameRoomMessage.fireworks', index=13,
      number=14, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='fromNickId', full_name='GameRoomMessage.fromNickId', index=14,
      number=15, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='nickIdPretty', full_name='GameRoomMessage.nickIdPretty', index=15,
      number=16, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='seatNum', full_name='GameRoomMessage.seatNum', index=16,
      number=17, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='fromUserInfo', full_name='GameRoomMessage.fromUserInfo', index=17,
      number=18, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='toUserInfo', full_name='GameRoomMessage.toUserInfo', index=18,
      number=19, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1376,
  serialized_end=1824,
)


_LEVELUPRESP = _descriptor.Descriptor(
  name='LevelUpResp',
  full_name='LevelUpResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='level', full_name='LevelUpResp.level', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='popupMessage', full_name='LevelUpResp.popupMessage', index=1,
      number=2, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1826,
  serialized_end=1876,
)


_WEREWOLFPLAYERMODEL = _descriptor.Descriptor(
  name='WerewolfPlayerModel',
  full_name='WerewolfPlayerModel',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='userId', full_name='WerewolfPlayerModel.userId', index=0,
      number=1, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='headerThumb', full_name='WerewolfPlayerModel.headerThumb', index=1,
      number=2, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='seatNum', full_name='WerewolfPlayerModel.seatNum', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='survivalStatus', full_name='WerewolfPlayerModel.survivalStatus', index=3,
      number=4, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='identityType', full_name='WerewolfPlayerModel.identityType', index=4,
      number=5, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='loverFlag', full_name='WerewolfPlayerModel.loverFlag', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1879,
  serialized_end=2019,
)


_WEREWOLFKILLMODEL = _descriptor.Descriptor(
  name='WerewolfKillModel',
  full_name='WerewolfKillModel',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='player', full_name='WerewolfKillModel.player', index=0,
      number=1, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='killedBy', full_name='WerewolfKillModel.killedBy', index=1,
      number=2, type=5, cpp_type=1, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='identityType', full_name='WerewolfKillModel.identityType', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2021,
  serialized_end=2118,
)


_WEREWOLFRESULTMODEL = _descriptor.Descriptor(
  name='WerewolfResultModel',
  full_name='WerewolfResultModel',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='player', full_name='WerewolfResultModel.player', index=0,
      number=1, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='identityType', full_name='WerewolfResultModel.identityType', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='deathType', full_name='WerewolfResultModel.deathType', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2120,
  serialized_end=2220,
)


_ALLOTIDENTITYRESP = _descriptor.Descriptor(
  name='AllotIdentityResp',
  full_name='AllotIdentityResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='AllotIdentityResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='identityType', full_name='AllotIdentityResp.identityType', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='countdown', full_name='AllotIdentityResp.countdown', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='campType', full_name='AllotIdentityResp.campType', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2222,
  serialized_end=2317,
)


_WEREWOLFNOTIFICATIONRESP = _descriptor.Descriptor(
  name='WerewolfNotificationResp',
  full_name='WerewolfNotificationResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='WerewolfNotificationResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='userIds', full_name='WerewolfNotificationResp.userIds', index=1,
      number=2, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='werewolfKingUserId', full_name='WerewolfNotificationResp.werewolfKingUserId', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='beautifulWerewolfUserId', full_name='WerewolfNotificationResp.beautifulWerewolfUserId', index=3,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2319,
  serialized_end=2440,
)


_WEREWOLFKILLRESP = _descriptor.Descriptor(
  name='WerewolfKillResp',
  full_name='WerewolfKillResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='WerewolfKillResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='players', full_name='WerewolfKillResp.players', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='countdown', full_name='WerewolfKillResp.countdown', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2442,
  serialized_end=2533,
)


_PROPHETNOTIFICATIONRESP = _descriptor.Descriptor(
  name='ProphetNotificationResp',
  full_name='ProphetNotificationResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='ProphetNotificationResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='players', full_name='ProphetNotificationResp.players', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='countdown', full_name='ProphetNotificationResp.countdown', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2535,
  serialized_end=2635,
)


_PROPHETRESULTRESP = _descriptor.Descriptor(
  name='ProphetResultResp',
  full_name='ProphetResultResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='ProphetResultResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='player', full_name='ProphetResultResp.player', index=1,
      number=2, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='isWerewolf', full_name='ProphetResultResp.isWerewolf', index=2,
      number=3, type=8, cpp_type=7, label=2,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='countdown', full_name='ProphetResultResp.countdown', index=3,
      number=4, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='prophetResultType', full_name='ProphetResultResp.prophetResultType', index=4,
      number=5, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2638,
  serialized_end=2778,
)


_WITCHRESCUENOTIFICATIONRESP = _descriptor.Descriptor(
  name='WitchRescueNotificationResp',
  full_name='WitchRescueNotificationResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='WitchRescueNotificationResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='player', full_name='WitchRescueNotificationResp.player', index=1,
      number=2, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='countdown', full_name='WitchRescueNotificationResp.countdown', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='unableRescue', full_name='WitchRescueNotificationResp.unableRescue', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2780,
  serialized_end=2905,
)


_WITCHPOISONNOTIFICATIONRESP = _descriptor.Descriptor(
  name='WitchPoisonNotificationResp',
  full_name='WitchPoisonNotificationResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='WitchPoisonNotificationResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='players', full_name='WitchPoisonNotificationResp.players', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='countdown', full_name='WitchPoisonNotificationResp.countdown', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2907,
  serialized_end=3011,
)


_PLAYERDIESNOTIFICATIONRESP = _descriptor.Descriptor(
  name='PlayerDiesNotificationResp',
  full_name='PlayerDiesNotificationResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='PlayerDiesNotificationResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='userIds', full_name='PlayerDiesNotificationResp.userIds', index=1,
      number=2, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='hasLastWords', full_name='PlayerDiesNotificationResp.hasLastWords', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='deathType', full_name='PlayerDiesNotificationResp.deathType', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3013,
  serialized_end=3116,
)


_HUNTERKILLRESP = _descriptor.Descriptor(
  name='HunterKillResp',
  full_name='HunterKillResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='HunterKillResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='players', full_name='HunterKillResp.players', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='countdown', full_name='HunterKillResp.countdown', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3118,
  serialized_end=3209,
)


_VOTENOTIFICATIONRESP = _descriptor.Descriptor(
  name='VoteNotificationResp',
  full_name='VoteNotificationResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='VoteNotificationResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='players', full_name='VoteNotificationResp.players', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='countdown', full_name='VoteNotificationResp.countdown', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3211,
  serialized_end=3308,
)


_PLAYERWAITINGRESP = _descriptor.Descriptor(
  name='PlayerWaitingResp',
  full_name='PlayerWaitingResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='PlayerWaitingResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='title', full_name='PlayerWaitingResp.title', index=1,
      number=2, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='content', full_name='PlayerWaitingResp.content', index=2,
      number=3, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='countdown', full_name='PlayerWaitingResp.countdown', index=3,
      number=4, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='type', full_name='PlayerWaitingResp.type', index=4,
      number=5, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='roundInfo', full_name='PlayerWaitingResp.roundInfo', index=5,
      number=6, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3311,
  serialized_end=3448,
)


_DAYORNIGHTRESP = _descriptor.Descriptor(
  name='DayOrNightResp',
  full_name='DayOrNightResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='DayOrNightResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='currentType', full_name='DayOrNightResp.currentType', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='turns', full_name='DayOrNightResp.turns', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='bgmNumber', full_name='DayOrNightResp.bgmNumber', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3450,
  serialized_end=3538,
)


_GAMERESULTRESP = _descriptor.Descriptor(
  name='GameResultResp',
  full_name='GameResultResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='GameResultResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='werewolves', full_name='GameResultResp.werewolves', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='person', full_name='GameResultResp.person', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='experience', full_name='GameResultResp.experience', index=3,
      number=4, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='victory', full_name='GameResultResp.victory', index=4,
      number=5, type=8, cpp_type=7, label=2,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='countdown', full_name='GameResultResp.countdown', index=5,
      number=6, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='identityType', full_name='GameResultResp.identityType', index=6,
      number=7, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='lovers', full_name='GameResultResp.lovers', index=7,
      number=8, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='winnerType', full_name='GameResultResp.winnerType', index=8,
      number=9, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='hunters', full_name='GameResultResp.hunters', index=9,
      number=10, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='nian', full_name='GameResultResp.nian', index=10,
      number=11, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='loyal', full_name='GameResultResp.loyal', index=11,
      number=12, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='rebel', full_name='GameResultResp.rebel', index=12,
      number=13, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3541,
  serialized_end=3939,
)


_SPEAKNOTIFICATIONRESP = _descriptor.Descriptor(
  name='SpeakNotificationResp',
  full_name='SpeakNotificationResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='SpeakNotificationResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='userId', full_name='SpeakNotificationResp.userId', index=1,
      number=2, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='countdown', full_name='SpeakNotificationResp.countdown', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='showDelay', full_name='SpeakNotificationResp.showDelay', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3941,
  serialized_end=4035,
)


_LOGINRESP = _descriptor.Descriptor(
  name='LoginResp',
  full_name='LoginResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='userId', full_name='LoginResp.userId', index=0,
      number=1, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4037,
  serialized_end=4064,
)


_OFFSITELOGINRESP = _descriptor.Descriptor(
  name='OffSiteLoginResp',
  full_name='OffSiteLoginResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='userId', full_name='OffSiteLoginResp.userId', index=0,
      number=1, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='deviceId', full_name='OffSiteLoginResp.deviceId', index=1,
      number=2, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4066,
  serialized_end=4118,
)


_AGORAVOICECONDITIONCHANGERESP = _descriptor.Descriptor(
  name='AgoraVoiceConditionChangeResp',
  full_name='AgoraVoiceConditionChangeResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='AgoraVoiceConditionChangeResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='voiceSwitch', full_name='AgoraVoiceConditionChangeResp.voiceSwitch', index=1,
      number=2, type=8, cpp_type=7, label=2,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='speakingUsers', full_name='AgoraVoiceConditionChangeResp.speakingUsers', index=2,
      number=3, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4120,
  serialized_end=4212,
)


_AGORABANVOICERESP = _descriptor.Descriptor(
  name='AgoraBanVoiceResp',
  full_name='AgoraBanVoiceResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='AgoraBanVoiceResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4214,
  serialized_end=4250,
)


_AGORAFREESPEAKRESP = _descriptor.Descriptor(
  name='AgoraFreeSpeakResp',
  full_name='AgoraFreeSpeakResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='AgoraFreeSpeakResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='freeSpeakUserId', full_name='AgoraFreeSpeakResp.freeSpeakUserId', index=1,
      number=2, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='freeSpeakStatus', full_name='AgoraFreeSpeakResp.freeSpeakStatus', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4252,
  serialized_end=4339,
)


_WEREWOLFKILLCOMPLETERESP = _descriptor.Descriptor(
  name='WerewolfKillCompleteResp',
  full_name='WerewolfKillCompleteResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='WerewolfKillCompleteResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4341,
  serialized_end=4384,
)


_ABNORMALNOTIFICATIONRESP = _descriptor.Descriptor(
  name='AbnormalNotificationResp',
  full_name='AbnormalNotificationResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='AbnormalNotificationResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='content', full_name='AbnormalNotificationResp.content', index=1,
      number=2, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='countdown', full_name='AbnormalNotificationResp.countdown', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4386,
  serialized_end=4465,
)


_EXPERIENCECHANGERESP = _descriptor.Descriptor(
  name='ExperienceChangeResp',
  full_name='ExperienceChangeResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='experience', full_name='ExperienceChangeResp.experience', index=0,
      number=1, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='experienceBar', full_name='ExperienceChangeResp.experienceBar', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4467,
  serialized_end=4532,
)


_PRESENTMODEL = _descriptor.Descriptor(
  name='PresentModel',
  full_name='PresentModel',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='presentConfigId', full_name='PresentModel.presentConfigId', index=0,
      number=1, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='presentKey', full_name='PresentModel.presentKey', index=1,
      number=2, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='presentName', full_name='PresentModel.presentName', index=2,
      number=3, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='effect', full_name='PresentModel.effect', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='presentThumb', full_name='PresentModel.presentThumb', index=4,
      number=5, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='amount', full_name='PresentModel.amount', index=5,
      number=6, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='fromUserId', full_name='PresentModel.fromUserId', index=6,
      number=7, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='toUserId', full_name='PresentModel.toUserId', index=7,
      number=8, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='count', full_name='PresentModel.count', index=8,
      number=9, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='popularity', full_name='PresentModel.popularity', index=9,
      number=10, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='privateGive', full_name='PresentModel.privateGive', index=10,
      number=11, type=8, cpp_type=7, label=2,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='presentDynamicEffect', full_name='PresentModel.presentDynamicEffect', index=11,
      number=12, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='presentGif', full_name='PresentModel.presentGif', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='seatNum', full_name='PresentModel.seatNum', index=13,
      number=14, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='combo', full_name='PresentModel.combo', index=14,
      number=15, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='comboNum', full_name='PresentModel.comboNum', index=15,
      number=16, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='fromSeatNum', full_name='PresentModel.fromSeatNum', index=16,
      number=17, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='fromNickName', full_name='PresentModel.fromNickName', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='toNickName', full_name='PresentModel.toNickName', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='awardModel', full_name='PresentModel.awardModel', index=19,
      number=20, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='presentSceneDynamicEffect', full_name='PresentModel.presentSceneDynamicEffect', index=20,
      number=21, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4535,
  serialized_end=4998,
)


_USERINFOMODEL = _descriptor.Descriptor(
  name='UserInfoModel',
  full_name='UserInfoModel',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='nickId', full_name='UserInfoModel.nickId', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='nickName', full_name='UserInfoModel.nickName', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='headThumb', full_name='UserInfoModel.headThumb', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='vipType', full_name='UserInfoModel.vipType', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='nickIdPretty', full_name='UserInfoModel.nickIdPretty', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='popularityLevel', full_name='UserInfoModel.popularityLevel', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5001,
  serialized_end=5133,
)


_AWARDINFOMODEL = _descriptor.Descriptor(
  name='AwardInfoModel',
  full_name='AwardInfoModel',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='awardName', full_name='AwardInfoModel.awardName', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='awardCount', full_name='AwardInfoModel.awardCount', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='awardUnit', full_name='AwardInfoModel.awardUnit', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5135,
  serialized_end=5209,
)


_PRIVATEMESSAGE = _descriptor.Descriptor(
  name='PrivateMessage',
  full_name='PrivateMessage',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='from', full_name='PrivateMessage.from', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='to', full_name='PrivateMessage.to', index=1,
      number=2, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='groupType', full_name='PrivateMessage.groupType', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='messageId', full_name='PrivateMessage.messageId', index=3,
      number=4, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='messageOn', full_name='PrivateMessage.messageOn', index=4,
      number=5, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='msgType', full_name='PrivateMessage.msgType', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='msgSubType', full_name='PrivateMessage.msgSubType', index=6,
      number=7, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='length', full_name='PrivateMessage.length', index=7,
      number=8, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='url', full_name='PrivateMessage.url', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='content', full_name='PrivateMessage.content', index=9,
      number=10, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='status', full_name='PrivateMessage.status', index=10,
      number=11, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='unsupported', full_name='PrivateMessage.unsupported', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='extra', full_name='PrivateMessage.extra', index=12,
      number=13, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='present', full_name='PrivateMessage.present', index=13,
      number=14, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='gameRoomInvite', full_name='PrivateMessage.gameRoomInvite', index=14,
      number=15, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='chatBubbleId', full_name='PrivateMessage.chatBubbleId', index=15,
      number=16, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='vipType', full_name='PrivateMessage.vipType', index=16,
      number=17, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='headFrameId', full_name='PrivateMessage.headFrameId', index=17,
      number=18, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='contentUserIds', full_name='PrivateMessage.contentUserIds', index=18,
      number=19, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='fromUserInfo', full_name='PrivateMessage.fromUserInfo', index=19,
      number=20, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='toUserInfo', full_name='PrivateMessage.toUserInfo', index=20,
      number=21, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5212,
  serialized_end=5682,
)


_PRIVATEMESSAGEREADCONFIRMREQ = _descriptor.Descriptor(
  name='PrivateMessageReadConfirmReq',
  full_name='PrivateMessageReadConfirmReq',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='messageId', full_name='PrivateMessageReadConfirmReq.messageId', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5684,
  serialized_end=5733,
)


_GETPRIVATEMESSAGEREQ = _descriptor.Descriptor(
  name='GetPrivateMessageReq',
  full_name='GetPrivateMessageReq',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='messageId', full_name='GetPrivateMessageReq.messageId', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5735,
  serialized_end=5776,
)


_GETPRIVATEMESSAGERESP = _descriptor.Descriptor(
  name='GetPrivateMessageResp',
  full_name='GetPrivateMessageResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='privateMessage', full_name='GetPrivateMessageResp.privateMessage', index=0,
      number=1, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5778,
  serialized_end=5842,
)


_GAMEROOMINVITEMODEL = _descriptor.Descriptor(
  name='GameRoomInviteModel',
  full_name='GameRoomInviteModel',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='fromUserId', full_name='GameRoomInviteModel.fromUserId', index=0,
      number=1, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='toUserId', full_name='GameRoomInviteModel.toUserId', index=1,
      number=2, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='roomName', full_name='GameRoomInviteModel.roomName', index=2,
      number=3, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='GameRoomInviteModel.roomKey', index=3,
      number=4, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='sendContent', full_name='GameRoomInviteModel.sendContent', index=4,
      number=5, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='receiveContent', full_name='GameRoomInviteModel.receiveContent', index=5,
      number=6, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='roomNum', full_name='GameRoomInviteModel.roomNum', index=6,
      number=7, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='gameRoomType', full_name='GameRoomInviteModel.gameRoomType', index=7,
      number=8, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='roomTheme', full_name='GameRoomInviteModel.roomTheme', index=8,
      number=9, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5845,
  serialized_end=6042,
)


_USERFRIENDSADDRESP = _descriptor.Descriptor(
  name='UserFriendsAddResp',
  full_name='UserFriendsAddResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='userBasic', full_name='UserFriendsAddResp.userBasic', index=0,
      number=1, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='version', full_name='UserFriendsAddResp.version', index=1,
      number=2, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6044,
  serialized_end=6116,
)


_USERFRIENDSDELRESP = _descriptor.Descriptor(
  name='UserFriendsDelResp',
  full_name='UserFriendsDelResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='userId', full_name='UserFriendsDelResp.userId', index=0,
      number=1, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='version', full_name='UserFriendsDelResp.version', index=1,
      number=2, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6118,
  serialized_end=6171,
)


_USERINFOCHANGERESP = _descriptor.Descriptor(
  name='UserInfoChangeResp',
  full_name='UserInfoChangeResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='userBasic', full_name='UserInfoChangeResp.userBasic', index=0,
      number=1, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='version', full_name='UserInfoChangeResp.version', index=1,
      number=2, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6173,
  serialized_end=6245,
)


_USERBASICRESP = _descriptor.Descriptor(
  name='UserBasicResp',
  full_name='UserBasicResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='userId', full_name='UserBasicResp.userId', index=0,
      number=1, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='nickId', full_name='UserBasicResp.nickId', index=1,
      number=2, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='nickName', full_name='UserBasicResp.nickName', index=2,
      number=3, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='gender', full_name='UserBasicResp.gender', index=3,
      number=4, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='headerThmnb', full_name='UserBasicResp.headerThmnb', index=4,
      number=5, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='vip', full_name='UserBasicResp.vip', index=5,
      number=6, type=8, cpp_type=7, label=2,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='signature', full_name='UserBasicResp.signature', index=6,
      number=7, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='level', full_name='UserBasicResp.level', index=7,
      number=8, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='originalName', full_name='UserBasicResp.originalName', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6248,
  serialized_end=6419,
)


_FRIENDREQUESTRESP = _descriptor.Descriptor(
  name='FriendRequestResp',
  full_name='FriendRequestResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='requestedFrom', full_name='FriendRequestResp.requestedFrom', index=0,
      number=1, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6421,
  serialized_end=6463,
)


_SYSTEMMESSAGE = _descriptor.Descriptor(
  name='SystemMessage',
  full_name='SystemMessage',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='msgType', full_name='SystemMessage.msgType', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='msgSubType', full_name='SystemMessage.msgSubType', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='content', full_name='SystemMessage.content', index=2,
      number=3, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='messageOn', full_name='SystemMessage.messageOn', index=3,
      number=4, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='unsupported', full_name='SystemMessage.unsupported', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6465,
  serialized_end=6574,
)


_BALANCECHANGERESP = _descriptor.Descriptor(
  name='BalanceChangeResp',
  full_name='BalanceChangeResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='balance', full_name='BalanceChangeResp.balance', index=0,
      number=1, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6576,
  serialized_end=6612,
)


_VYINGIDENTITYMODEL = _descriptor.Descriptor(
  name='VyingIdentityModel',
  full_name='VyingIdentityModel',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='identityType', full_name='VyingIdentityModel.identityType', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='totalCount', full_name='VyingIdentityModel.totalCount', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='currentCount', full_name='VyingIdentityModel.currentCount', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='price', full_name='VyingIdentityModel.price', index=3,
      number=4, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6614,
  serialized_end=6713,
)


_VYINGIDENTITYRESP = _descriptor.Descriptor(
  name='VyingIdentityResp',
  full_name='VyingIdentityResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='VyingIdentityResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='identities', full_name='VyingIdentityResp.identities', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='countdown', full_name='VyingIdentityResp.countdown', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6715,
  serialized_end=6811,
)


_GAMESTARTNOTIFICATIONRESP = _descriptor.Descriptor(
  name='GameStartNotificationResp',
  full_name='GameStartNotificationResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='GameStartNotificationResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6813,
  serialized_end=6857,
)


_GAMEROOMPASSWORDCHANGERESP = _descriptor.Descriptor(
  name='GameRoomPasswordChangeResp',
  full_name='GameRoomPasswordChangeResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='GameRoomPasswordChangeResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='setPassword', full_name='GameRoomPasswordChangeResp.setPassword', index=1,
      number=2, type=8, cpp_type=7, label=2,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6859,
  serialized_end=6925,
)


_CUPIDCHOOSERESP = _descriptor.Descriptor(
  name='CupidChooseResp',
  full_name='CupidChooseResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='CupidChooseResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='players', full_name='CupidChooseResp.players', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='countdown', full_name='CupidChooseResp.countdown', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6927,
  serialized_end=7019,
)


_CUPIDRESULTRESP = _descriptor.Descriptor(
  name='CupidResultResp',
  full_name='CupidResultResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='CupidResultResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='players', full_name='CupidResultResp.players', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='countdown', full_name='CupidResultResp.countdown', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7021,
  serialized_end=7113,
)


_SERGEANTNOTIFICATIONRESP = _descriptor.Descriptor(
  name='SergeantNotificationResp',
  full_name='SergeantNotificationResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='SergeantNotificationResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='countdown', full_name='SergeantNotificationResp.countdown', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7115,
  serialized_end=7177,
)


_VOTESERGEANTRESP = _descriptor.Descriptor(
  name='VoteSergeantResp',
  full_name='VoteSergeantResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='VoteSergeantResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='players', full_name='VoteSergeantResp.players', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='countdown', full_name='VoteSergeantResp.countdown', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7179,
  serialized_end=7272,
)


_PROMOTIONTOSERGEANTRESP = _descriptor.Descriptor(
  name='PromotionToSergeantResp',
  full_name='PromotionToSergeantResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='PromotionToSergeantResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='sergeant', full_name='PromotionToSergeantResp.sergeant', index=1,
      number=2, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='countdown', full_name='PromotionToSergeantResp.countdown', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7274,
  serialized_end=7375,
)


_SERGEANTCHANGERESP = _descriptor.Descriptor(
  name='SergeantChangeResp',
  full_name='SergeantChangeResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='SergeantChangeResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='players', full_name='SergeantChangeResp.players', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='countdown', full_name='SergeantChangeResp.countdown', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7377,
  serialized_end=7472,
)


_SERGEANTCHANGERESULTRESP = _descriptor.Descriptor(
  name='SergeantChangeResultResp',
  full_name='SergeantChangeResultResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='SergeantChangeResultResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='oldSergeant', full_name='SergeantChangeResultResp.oldSergeant', index=1,
      number=2, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='sergeant', full_name='SergeantChangeResultResp.sergeant', index=2,
      number=3, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='countdown', full_name='SergeantChangeResultResp.countdown', index=3,
      number=4, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7475,
  serialized_end=7620,
)


_SERGEANTELECTNOTIFICATIONRESP = _descriptor.Descriptor(
  name='SergeantElectNotificationResp',
  full_name='SergeantElectNotificationResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='SergeantElectNotificationResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='userIds', full_name='SergeantElectNotificationResp.userIds', index=1,
      number=2, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7622,
  serialized_end=7687,
)


_SERGEANTROUNDCOMPLETERESP = _descriptor.Descriptor(
  name='SergeantRoundCompleteResp',
  full_name='SergeantRoundCompleteResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='SergeantRoundCompleteResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='hasSergeant', full_name='SergeantRoundCompleteResp.hasSergeant', index=1,
      number=2, type=8, cpp_type=7, label=2,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='hasBGM', full_name='SergeantRoundCompleteResp.hasBGM', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7689,
  serialized_end=7770,
)


_SERGEANTVOTEROUNDRESP = _descriptor.Descriptor(
  name='SergeantVoteRoundResp',
  full_name='SergeantVoteRoundResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='SergeantVoteRoundResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7772,
  serialized_end=7812,
)


_VOTECOMPLETERESP = _descriptor.Descriptor(
  name='VoteCompleteResp',
  full_name='VoteCompleteResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='VoteCompleteResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7814,
  serialized_end=7849,
)


_CUPIDROUNDRESP = _descriptor.Descriptor(
  name='CupidRoundResp',
  full_name='CupidRoundResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='CupidRoundResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7851,
  serialized_end=7884,
)


_GUILDAPPLYRESP = _descriptor.Descriptor(
  name='GuildApplyResp',
  full_name='GuildApplyResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='userId', full_name='GuildApplyResp.userId', index=0,
      number=1, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='headerThumb', full_name='GuildApplyResp.headerThumb', index=1,
      number=2, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='content', full_name='GuildApplyResp.content', index=2,
      number=3, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7886,
  serialized_end=7956,
)


_GUILDMEMBERCHANGERESP = _descriptor.Descriptor(
  name='GuildMemberChangeResp',
  full_name='GuildMemberChangeResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='userId', full_name='GuildMemberChangeResp.userId', index=0,
      number=1, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='guildId', full_name='GuildMemberChangeResp.guildId', index=1,
      number=2, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='memberChangeType', full_name='GuildMemberChangeResp.memberChangeType', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7958,
  serialized_end=8040,
)


_GUILDDISSOLVERESP = _descriptor.Descriptor(
  name='GuildDissolveResp',
  full_name='GuildDissolveResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='guildId', full_name='GuildDissolveResp.guildId', index=0,
      number=1, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8042,
  serialized_end=8078,
)


_GUILDMEMBERTITLECHANGERESP = _descriptor.Descriptor(
  name='GuildMemberTitleChangeResp',
  full_name='GuildMemberTitleChangeResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='guildId', full_name='GuildMemberTitleChangeResp.guildId', index=0,
      number=1, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='titleNumber', full_name='GuildMemberTitleChangeResp.titleNumber', index=1,
      number=2, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='titleName', full_name='GuildMemberTitleChangeResp.titleName', index=2,
      number=3, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='userId', full_name='GuildMemberTitleChangeResp.userId', index=3,
      number=4, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8080,
  serialized_end=8181,
)


_GETGUILDMESSAGEREQ = _descriptor.Descriptor(
  name='GetGuildMessageReq',
  full_name='GetGuildMessageReq',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='userId', full_name='GetGuildMessageReq.userId', index=0,
      number=1, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='guildId', full_name='GetGuildMessageReq.guildId', index=1,
      number=2, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='offset', full_name='GetGuildMessageReq.offset', index=2,
      number=3, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='loadMore', full_name='GetGuildMessageReq.loadMore', index=3,
      number=4, type=8, cpp_type=7, label=2,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='count', full_name='GetGuildMessageReq.count', index=4,
      number=5, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8183,
  serialized_end=8285,
)


_GETGUILDMESSAGERESP = _descriptor.Descriptor(
  name='GetGuildMessageResp',
  full_name='GetGuildMessageResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='guildMessages', full_name='GetGuildMessageResp.guildMessages', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8287,
  serialized_end=8351,
)


_GUILDMESSAGEMODEL = _descriptor.Descriptor(
  name='GuildMessageModel',
  full_name='GuildMessageModel',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='offset', full_name='GuildMessageModel.offset', index=0,
      number=1, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='privateMessage', full_name='GuildMessageModel.privateMessage', index=1,
      number=2, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8353,
  serialized_end=8429,
)


_GUILDOFFLINEMESSAGERESP = _descriptor.Descriptor(
  name='GuildOfflineMessageResp',
  full_name='GuildOfflineMessageResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='guildMessages', full_name='GuildOfflineMessageResp.guildMessages', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='unReadCount', full_name='GuildOfflineMessageResp.unReadCount', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='redPoint', full_name='GuildOfflineMessageResp.redPoint', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8431,
  serialized_end=8538,
)


_GUILDRECRUITCHECKRESP = _descriptor.Descriptor(
  name='GuildRecruitCheckResp',
  full_name='GuildRecruitCheckResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='status', full_name='GuildRecruitCheckResp.status', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='recruitSeqId', full_name='GuildRecruitCheckResp.recruitSeqId', index=1,
      number=2, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='publishUserId', full_name='GuildRecruitCheckResp.publishUserId', index=2,
      number=3, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='guildId', full_name='GuildRecruitCheckResp.guildId', index=3,
      number=4, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8540,
  serialized_end=8641,
)


_LOGOUTNOTIFICATIONRESP = _descriptor.Descriptor(
  name='LogoutNotificationResp',
  full_name='LogoutNotificationResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='logoutType', full_name='LogoutNotificationResp.logoutType', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='reason', full_name='LogoutNotificationResp.reason', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='csPermission', full_name='LogoutNotificationResp.csPermission', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8643,
  serialized_end=8725,
)


_GAMEROOMSETTINGCHANGERESP = _descriptor.Descriptor(
  name='GameRoomSettingChangeResp',
  full_name='GameRoomSettingChangeResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='GameRoomSettingChangeResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='setPassword', full_name='GameRoomSettingChangeResp.setPassword', index=1,
      number=2, type=8, cpp_type=7, label=2,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='roomLevel', full_name='GameRoomSettingChangeResp.roomLevel', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8727,
  serialized_end=8811,
)


_GETOFFLINEMESSAGEREQ = _descriptor.Descriptor(
  name='GetOfflineMessageReq',
  full_name='GetOfflineMessageReq',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='sseqNo', full_name='GetOfflineMessageReq.sseqNo', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8813,
  serialized_end=8851,
)


_GETOFFLINEMESSAGERESP = _descriptor.Descriptor(
  name='GetOfflineMessageResp',
  full_name='GetOfflineMessageResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='sseqNo', full_name='GetOfflineMessageResp.sseqNo', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8853,
  serialized_end=8892,
)


_SELFDESTRUCTIONNOTIFICATION = _descriptor.Descriptor(
  name='SelfDestructionNotification',
  full_name='SelfDestructionNotification',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='SelfDestructionNotification.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='players', full_name='SelfDestructionNotification.players', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='countdown', full_name='SelfDestructionNotification.countdown', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8894,
  serialized_end=8998,
)


_GUARDNOTIFICATIONRESP = _descriptor.Descriptor(
  name='GuardNotificationResp',
  full_name='GuardNotificationResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='GuardNotificationResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='players', full_name='GuardNotificationResp.players', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='countdown', full_name='GuardNotificationResp.countdown', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9000,
  serialized_end=9098,
)


_SPEAKENDRESP = _descriptor.Descriptor(
  name='SpeakEndResp',
  full_name='SpeakEndResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='SpeakEndResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9100,
  serialized_end=9131,
)


_SELFDESTRUCTIONSTARTRESP = _descriptor.Descriptor(
  name='SelfDestructionStartResp',
  full_name='SelfDestructionStartResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='SelfDestructionStartResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='destructionType', full_name='SelfDestructionStartResp.destructionType', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9133,
  serialized_end=9201,
)


_SELFDESTRUCTIONENDRESP = _descriptor.Descriptor(
  name='SelfDestructionEndResp',
  full_name='SelfDestructionEndResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='SelfDestructionEndResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9203,
  serialized_end=9244,
)


_HUNTERKILLINTHENIGHTRESP = _descriptor.Descriptor(
  name='HunterKillInTheNightResp',
  full_name='HunterKillInTheNightResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='HunterKillInTheNightResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='players', full_name='HunterKillInTheNightResp.players', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='countdown', full_name='HunterKillInTheNightResp.countdown', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9246,
  serialized_end=9345,
)


_HUNTERNOTIFICATIONRESP = _descriptor.Descriptor(
  name='HunterNotificationResp',
  full_name='HunterNotificationResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='HunterNotificationResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='userIds', full_name='HunterNotificationResp.userIds', index=1,
      number=2, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9347,
  serialized_end=9405,
)


_VIPINFOCHANGERESP = _descriptor.Descriptor(
  name='VipInfoChangeResp',
  full_name='VipInfoChangeResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='userId', full_name='VipInfoChangeResp.userId', index=0,
      number=1, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='vipType', full_name='VipInfoChangeResp.vipType', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='expireDate', full_name='VipInfoChangeResp.expireDate', index=2,
      number=3, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9407,
  serialized_end=9479,
)


_USERLEVELCHANGERESP = _descriptor.Descriptor(
  name='UserLevelChangeResp',
  full_name='UserLevelChangeResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='userId', full_name='UserLevelChangeResp.userId', index=0,
      number=1, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='level', full_name='UserLevelChangeResp.level', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9481,
  serialized_end=9533,
)


_VOTEINFO = _descriptor.Descriptor(
  name='VoteInfo',
  full_name='VoteInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='voteModels', full_name='VoteInfo.voteModels', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9535,
  serialized_end=9580,
)


_USERVOTEINFO = _descriptor.Descriptor(
  name='UserVoteInfo',
  full_name='UserVoteInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='userSeatNums', full_name='UserVoteInfo.userSeatNums', index=0,
      number=1, type=5, cpp_type=1, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='voteToSeatNum', full_name='UserVoteInfo.voteToSeatNum', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9582,
  serialized_end=9641,
)


_RECREATIONROOMSETTINGCHANGERESP = _descriptor.Descriptor(
  name='RecreationRoomSettingChangeResp',
  full_name='RecreationRoomSettingChangeResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='RecreationRoomSettingChangeResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='roomName', full_name='RecreationRoomSettingChangeResp.roomName', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='setPassword', full_name='RecreationRoomSettingChangeResp.setPassword', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='tagId', full_name='RecreationRoomSettingChangeResp.tagId', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='backgroundUrl', full_name='RecreationRoomSettingChangeResp.backgroundUrl', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='backgroundType', full_name='RecreationRoomSettingChangeResp.backgroundType', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='backgroundUrlV2', full_name='RecreationRoomSettingChangeResp.backgroundUrlV2', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9644,
  serialized_end=9820,
)


_RECREATIONROOMOWNERCHANGEMODEL = _descriptor.Descriptor(
  name='RecreationRoomOwnerChangeModel',
  full_name='RecreationRoomOwnerChangeModel',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='userId', full_name='RecreationRoomOwnerChangeModel.userId', index=0,
      number=1, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='seatNum', full_name='RecreationRoomOwnerChangeModel.seatNum', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9822,
  serialized_end=9887,
)


_RECREATIONROOMOWNERCHANGERESP = _descriptor.Descriptor(
  name='RecreationRoomOwnerChangeResp',
  full_name='RecreationRoomOwnerChangeResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='RecreationRoomOwnerChangeResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='password', full_name='RecreationRoomOwnerChangeResp.password', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='lastOwner', full_name='RecreationRoomOwnerChangeResp.lastOwner', index=2,
      number=3, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='currentOwner', full_name='RecreationRoomOwnerChangeResp.currentOwner', index=3,
      number=4, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9890,
  serialized_end=10063,
)


_RECREATIONROOMHEART = _descriptor.Descriptor(
  name='RecreationRoomHeart',
  full_name='RecreationRoomHeart',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='RecreationRoomHeart.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10065,
  serialized_end=10103,
)


_HUNTERROUNDNOTIFICATIONRESP = _descriptor.Descriptor(
  name='HunterRoundNotificationResp',
  full_name='HunterRoundNotificationResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='HunterRoundNotificationResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='isStart', full_name='HunterRoundNotificationResp.isStart', index=1,
      number=2, type=8, cpp_type=7, label=2,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10105,
  serialized_end=10168,
)


_TRUTHORDARERESP = _descriptor.Descriptor(
  name='TruthOrDareResp',
  full_name='TruthOrDareResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='TruthOrDareResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='type', full_name='TruthOrDareResp.type', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='content', full_name='TruthOrDareResp.content', index=2,
      number=3, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10170,
  serialized_end=10235,
)


_ROOMSOUNDSRESP = _descriptor.Descriptor(
  name='RoomSoundsResp',
  full_name='RoomSoundsResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='RoomSoundsResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='type', full_name='RoomSoundsResp.type', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10237,
  serialized_end=10284,
)


_PENGUINFROZENMODEL = _descriptor.Descriptor(
  name='PenguinFrozenModel',
  full_name='PenguinFrozenModel',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='player', full_name='PenguinFrozenModel.player', index=0,
      number=1, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='frozen', full_name='PenguinFrozenModel.frozen', index=1,
      number=2, type=8, cpp_type=7, label=2,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10286,
  serialized_end=10360,
)


_PENGUINFROZENRESP = _descriptor.Descriptor(
  name='PenguinFrozenResp',
  full_name='PenguinFrozenResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='PenguinFrozenResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='players', full_name='PenguinFrozenResp.players', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='countdown', full_name='PenguinFrozenResp.countdown', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10362,
  serialized_end=10455,
)


_WEREWOLFFROZENRESP = _descriptor.Descriptor(
  name='WerewolfFrozenResp',
  full_name='WerewolfFrozenResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='WerewolfFrozenResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='content', full_name='WerewolfFrozenResp.content', index=1,
      number=2, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='seatNum', full_name='WerewolfFrozenResp.seatNum', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='headerThumb', full_name='WerewolfFrozenResp.headerThumb', index=3,
      number=4, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='countdown', full_name='WerewolfFrozenResp.countdown', index=4,
      number=5, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10457,
  serialized_end=10568,
)


_COCKDIESRESP = _descriptor.Descriptor(
  name='CockDiesResp',
  full_name='CockDiesResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='CockDiesResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='headerThumb', full_name='CockDiesResp.headerThumb', index=1,
      number=2, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='seatNum', full_name='CockDiesResp.seatNum', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='countdown', full_name='CockDiesResp.countdown', index=3,
      number=4, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10570,
  serialized_end=10658,
)


_BEARROARRESP = _descriptor.Descriptor(
  name='BearRoarResp',
  full_name='BearRoarResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='BearRoarResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='roar', full_name='BearRoarResp.roar', index=1,
      number=2, type=8, cpp_type=7, label=2,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='countdown', full_name='BearRoarResp.countdown', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10660,
  serialized_end=10724,
)


_PENGUINFROZENNOTIFICATIONRESP = _descriptor.Descriptor(
  name='PenguinFrozenNotificationResp',
  full_name='PenguinFrozenNotificationResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='PenguinFrozenNotificationResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10726,
  serialized_end=10774,
)


_TREASUREBOXRESP = _descriptor.Descriptor(
  name='TreasureBoxResp',
  full_name='TreasureBoxResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='boxKey', full_name='TreasureBoxResp.boxKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='TreasureBoxResp.roomKey', index=1,
      number=2, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='countdown', full_name='TreasureBoxResp.countdown', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10776,
  serialized_end=10845,
)


_ROOMINFORESP = _descriptor.Descriptor(
  name='RoomInfoResp',
  full_name='RoomInfoResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='userId', full_name='RoomInfoResp.userId', index=0,
      number=1, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='type', full_name='RoomInfoResp.type', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='RoomInfoResp.roomKey', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='roomType', full_name='RoomInfoResp.roomType', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='roomTheme', full_name='RoomInfoResp.roomTheme', index=4,
      number=5, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10847,
  serialized_end=10945,
)


_MATCHINGUSERMODEL = _descriptor.Descriptor(
  name='MatchingUserModel',
  full_name='MatchingUserModel',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='userId', full_name='MatchingUserModel.userId', index=0,
      number=1, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='headerThumb', full_name='MatchingUserModel.headerThumb', index=1,
      number=2, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='isReady', full_name='MatchingUserModel.isReady', index=2,
      number=3, type=8, cpp_type=7, label=2,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10947,
  serialized_end=11020,
)


_MATCHINGINFORESP = _descriptor.Descriptor(
  name='MatchingInfoResp',
  full_name='MatchingInfoResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='MatchingInfoResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='readyCount', full_name='MatchingInfoResp.readyCount', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='totalCount', full_name='MatchingInfoResp.totalCount', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='countdown', full_name='MatchingInfoResp.countdown', index=3,
      number=4, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='matchingUsers', full_name='MatchingInfoResp.matchingUsers', index=4,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=11023,
  serialized_end=11160,
)


_QUALIFYINGREADYRESP = _descriptor.Descriptor(
  name='QualifyingReadyResp',
  full_name='QualifyingReadyResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='QualifyingReadyResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='userId', full_name='QualifyingReadyResp.userId', index=1,
      number=2, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='readied', full_name='QualifyingReadyResp.readied', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=11162,
  serialized_end=11233,
)


_REMATCHNOTIFICATIONRESP = _descriptor.Descriptor(
  name='RematchNotificationResp',
  full_name='RematchNotificationResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='gameRoomType', full_name='RematchNotificationResp.gameRoomType', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=11235,
  serialized_end=11282,
)


_ROOMMEMBERMODEL = _descriptor.Descriptor(
  name='RoomMemberModel',
  full_name='RoomMemberModel',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='userId', full_name='RoomMemberModel.userId', index=0,
      number=1, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='nickName', full_name='RoomMemberModel.nickName', index=1,
      number=2, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='headerThumb', full_name='RoomMemberModel.headerThumb', index=2,
      number=3, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='level', full_name='RoomMemberModel.level', index=3,
      number=4, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='seatNum', full_name='RoomMemberModel.seatNum', index=4,
      number=5, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='popularityLevel', full_name='RoomMemberModel.popularityLevel', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='vipType', full_name='RoomMemberModel.vipType', index=6,
      number=7, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='microphoneId', full_name='RoomMemberModel.microphoneId', index=7,
      number=8, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='headFrameId', full_name='RoomMemberModel.headFrameId', index=8,
      number=9, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=11285,
  serialized_end=11474,
)


_QUALIFYINGROOMINFORESP = _descriptor.Descriptor(
  name='QualifyingRoomInfoResp',
  full_name='QualifyingRoomInfoResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomNum', full_name='QualifyingRoomInfoResp.roomNum', index=0,
      number=1, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='roomName', full_name='QualifyingRoomInfoResp.roomName', index=1,
      number=2, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='QualifyingRoomInfoResp.roomKey', index=2,
      number=3, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='gameRoomType', full_name='QualifyingRoomInfoResp.gameRoomType', index=3,
      number=4, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='roomMembers', full_name='QualifyingRoomInfoResp.roomMembers', index=4,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=11477,
  serialized_end=11614,
)


_TINYTIERCHANGERESP = _descriptor.Descriptor(
  name='TinyTierChangeResp',
  full_name='TinyTierChangeResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='tinyTierId', full_name='TinyTierChangeResp.tinyTierId', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='tinyTierUp', full_name='TinyTierChangeResp.tinyTierUp', index=1,
      number=2, type=8, cpp_type=7, label=2,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='tinyTierFrom', full_name='TinyTierChangeResp.tinyTierFrom', index=2,
      number=3, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='tinyTierTo', full_name='TinyTierChangeResp.tinyTierTo', index=3,
      number=4, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=11616,
  serialized_end=11718,
)


_USERCREDITCHANGERESP = _descriptor.Descriptor(
  name='UserCreditChangeResp',
  full_name='UserCreditChangeResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='userId', full_name='UserCreditChangeResp.userId', index=0,
      number=1, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='creditPoints', full_name='UserCreditChangeResp.creditPoints', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=11720,
  serialized_end=11780,
)


_SYSTEMTOASTRESP = _descriptor.Descriptor(
  name='SystemToastResp',
  full_name='SystemToastResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='content', full_name='SystemToastResp.content', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=11782,
  serialized_end=11816,
)


_BIGEMOJIMODEL = _descriptor.Descriptor(
  name='BigEmojiModel',
  full_name='BigEmojiModel',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='BigEmojiModel.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='fromSeatNum', full_name='BigEmojiModel.fromSeatNum', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='toSeatNum', full_name='BigEmojiModel.toSeatNum', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='emojiId', full_name='BigEmojiModel.emojiId', index=3,
      number=4, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=11818,
  serialized_end=11907,
)


_BIGEMOJIREQ = _descriptor.Descriptor(
  name='BigEmojiReq',
  full_name='BigEmojiReq',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='bigEmoji', full_name='BigEmojiReq.bigEmoji', index=0,
      number=1, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=11909,
  serialized_end=11956,
)


_BIGEMOJIRESP = _descriptor.Descriptor(
  name='BigEmojiResp',
  full_name='BigEmojiResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='bigEmoji', full_name='BigEmojiResp.bigEmoji', index=0,
      number=1, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=11958,
  serialized_end=12006,
)


_SONGMODEL = _descriptor.Descriptor(
  name='SongModel',
  full_name='SongModel',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='songId', full_name='SongModel.songId', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='name', full_name='SongModel.name', index=1,
      number=2, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='artistNames', full_name='SongModel.artistNames', index=2,
      number=3, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='imgUrl', full_name='SongModel.imgUrl', index=3,
      number=4, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='fileUrl', full_name='SongModel.fileUrl', index=4,
      number=5, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='md5', full_name='SongModel.md5', index=5,
      number=6, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='duration', full_name='SongModel.duration', index=6,
      number=7, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='lyricUrl', full_name='SongModel.lyricUrl', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12009,
  serialized_end=12153,
)


_SONGINFOMODEL = _descriptor.Descriptor(
  name='SongInfoModel',
  full_name='SongInfoModel',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='uniquedKey', full_name='SongInfoModel.uniquedKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='song', full_name='SongInfoModel.song', index=1,
      number=2, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='userId', full_name='SongInfoModel.userId', index=2,
      number=3, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='playStatus', full_name='SongInfoModel.playStatus', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12155,
  serialized_end=12252,
)


_SONGLISTCHANGERESP = _descriptor.Descriptor(
  name='SongListChangeResp',
  full_name='SongListChangeResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='SongListChangeResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='uniquedKeys', full_name='SongListChangeResp.uniquedKeys', index=1,
      number=2, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='option', full_name='SongListChangeResp.option', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='songInfo', full_name='SongListChangeResp.songInfo', index=3,
      number=4, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='timeStamp', full_name='SongListChangeResp.timeStamp', index=4,
      number=5, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12254,
  serialized_end=12381,
)


_SWITCHSONGRESP = _descriptor.Descriptor(
  name='SwitchSongResp',
  full_name='SwitchSongResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='SwitchSongResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='switchStatus', full_name='SwitchSongResp.switchStatus', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12383,
  serialized_end=12438,
)


_SONGSTATUSRESP = _descriptor.Descriptor(
  name='SongStatusResp',
  full_name='SongStatusResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='SongStatusResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='songInfo', full_name='SongStatusResp.songInfo', index=1,
      number=2, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12440,
  serialized_end=12507,
)


_SINGNOTIFYRESP = _descriptor.Descriptor(
  name='SingNotifyResp',
  full_name='SingNotifyResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='SingNotifyResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='waitTime', full_name='SingNotifyResp.waitTime', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='songInfo', full_name='SingNotifyResp.songInfo', index=2,
      number=3, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12509,
  serialized_end=12594,
)


_RICHMANNOTIFICATIONRESP = _descriptor.Descriptor(
  name='RichManNotificationResp',
  full_name='RichManNotificationResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='RichManNotificationResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='countdown', full_name='RichManNotificationResp.countdown', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12596,
  serialized_end=12657,
)


_NIANKILLRESP = _descriptor.Descriptor(
  name='NianKillResp',
  full_name='NianKillResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='NianKillResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='players', full_name='NianKillResp.players', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='countdown', full_name='NianKillResp.countdown', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12659,
  serialized_end=12746,
)


_NIANNOTIFICATIONRESP = _descriptor.Descriptor(
  name='NianNotificationResp',
  full_name='NianNotificationResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='NianNotificationResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='userIds', full_name='NianNotificationResp.userIds', index=1,
      number=2, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12748,
  serialized_end=12804,
)


_NIANKILLCOMPLETERESP = _descriptor.Descriptor(
  name='NianKillCompleteResp',
  full_name='NianKillCompleteResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='NianKillCompleteResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='killed', full_name='NianKillCompleteResp.killed', index=1,
      number=2, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='countdown', full_name='NianKillCompleteResp.countdown', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12806,
  serialized_end=12902,
)


_PETEXPLORESTATUSRESP = _descriptor.Descriptor(
  name='PetExploreStatusResp',
  full_name='PetExploreStatusResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='userId', full_name='PetExploreStatusResp.userId', index=0,
      number=1, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='status', full_name='PetExploreStatusResp.status', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='timeStamp', full_name='PetExploreStatusResp.timeStamp', index=2,
      number=3, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12904,
  serialized_end=12977,
)


_PROPUPDATERESP = _descriptor.Descriptor(
  name='PropUpdateResp',
  full_name='PropUpdateResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='userId', full_name='PropUpdateResp.userId', index=0,
      number=1, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='propNum', full_name='PropUpdateResp.propNum', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='count', full_name='PropUpdateResp.count', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12979,
  serialized_end=13043,
)


_KILLCONFIRMRESP = _descriptor.Descriptor(
  name='KillConfirmResp',
  full_name='KillConfirmResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='KillConfirmResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='killed', full_name='KillConfirmResp.killed', index=1,
      number=2, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13045,
  serialized_end=13117,
)


_HUNTERKILLCONFIRMRESP = _descriptor.Descriptor(
  name='HunterKillConfirmResp',
  full_name='HunterKillConfirmResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='HunterKillConfirmResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='killed', full_name='HunterKillConfirmResp.killed', index=1,
      number=2, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13119,
  serialized_end=13197,
)


_FIREWORKSAWARDMODEL = _descriptor.Descriptor(
  name='FireworksAwardModel',
  full_name='FireworksAwardModel',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='userId', full_name='FireworksAwardModel.userId', index=0,
      number=1, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='seatNum', full_name='FireworksAwardModel.seatNum', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='awardAttributeValue', full_name='FireworksAwardModel.awardAttributeValue', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='props', full_name='FireworksAwardModel.props', index=3,
      number=4, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='headerThumb', full_name='FireworksAwardModel.headerThumb', index=4,
      number=5, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13199,
  serialized_end=13318,
)


_FIREWORKSMODEL = _descriptor.Descriptor(
  name='FireworksModel',
  full_name='FireworksModel',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='fireworksNum', full_name='FireworksModel.fireworksNum', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='awards', full_name='FireworksModel.awards', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='awardAttributeName', full_name='FireworksModel.awardAttributeName', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='awardAttributeIcon', full_name='FireworksModel.awardAttributeIcon', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13321,
  serialized_end=13453,
)


_GUILDTRANSFERRESP = _descriptor.Descriptor(
  name='GuildTransferResp',
  full_name='GuildTransferResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='guildId', full_name='GuildTransferResp.guildId', index=0,
      number=1, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='recordId', full_name='GuildTransferResp.recordId', index=1,
      number=2, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='type', full_name='GuildTransferResp.type', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13455,
  serialized_end=13523,
)


_BEAUTIFULWEREWOLFNOTIFICATIONRESP = _descriptor.Descriptor(
  name='BeautifulWerewolfNotificationResp',
  full_name='BeautifulWerewolfNotificationResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='BeautifulWerewolfNotificationResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='players', full_name='BeautifulWerewolfNotificationResp.players', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='countdown', full_name='BeautifulWerewolfNotificationResp.countdown', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13525,
  serialized_end=13635,
)


_RECREATIONGAMEROOMBOARDTEXTCHANGERESP = _descriptor.Descriptor(
  name='RecreationGameRoomBoardTextChangeResp',
  full_name='RecreationGameRoomBoardTextChangeResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='RecreationGameRoomBoardTextChangeResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='changeType', full_name='RecreationGameRoomBoardTextChangeResp.changeType', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='boardText', full_name='RecreationGameRoomBoardTextChangeResp.boardText', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13637,
  serialized_end=13732,
)


_RECREATIONROOMSEATCHANGERESP = _descriptor.Descriptor(
  name='RecreationRoomSeatChangeResp',
  full_name='RecreationRoomSeatChangeResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='RecreationRoomSeatChangeResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='userId', full_name='RecreationRoomSeatChangeResp.userId', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='lastSeatNum', full_name='RecreationRoomSeatChangeResp.lastSeatNum', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='currentSeatNum', full_name='RecreationRoomSeatChangeResp.currentSeatNum', index=3,
      number=4, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13734,
  serialized_end=13842,
)


_NICKIDCHANGERESP = _descriptor.Descriptor(
  name='NickIdChangeResp',
  full_name='NickIdChangeResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='userId', full_name='NickIdChangeResp.userId', index=0,
      number=1, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='nickId', full_name='NickIdChangeResp.nickId', index=1,
      number=2, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='nickIdPretty', full_name='NickIdChangeResp.nickIdPretty', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13844,
  serialized_end=13916,
)


_RECREATIONROOMSEATINVITERESP = _descriptor.Descriptor(
  name='RecreationRoomSeatInviteResp',
  full_name='RecreationRoomSeatInviteResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='RecreationRoomSeatInviteResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='invitedUserId', full_name='RecreationRoomSeatInviteResp.invitedUserId', index=1,
      number=2, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='expireTime', full_name='RecreationRoomSeatInviteResp.expireTime', index=2,
      number=3, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13918,
  serialized_end=14008,
)


_RECREATIONROOMUSERMUTERESP = _descriptor.Descriptor(
  name='RecreationRoomUserMuteResp',
  full_name='RecreationRoomUserMuteResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='RecreationRoomUserMuteResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='mutedUserId', full_name='RecreationRoomUserMuteResp.mutedUserId', index=1,
      number=2, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='muteCountdown', full_name='RecreationRoomUserMuteResp.muteCountdown', index=2,
      number=3, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='beforeMuteUserType', full_name='RecreationRoomUserMuteResp.beforeMuteUserType', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=14010,
  serialized_end=14127,
)


_AUDIENCECOUNTCHANGERESP = _descriptor.Descriptor(
  name='AudienceCountChangeResp',
  full_name='AudienceCountChangeResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='AudienceCountChangeResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='audienceCount', full_name='AudienceCountChangeResp.audienceCount', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='timestamp', full_name='AudienceCountChangeResp.timestamp', index=2,
      number=3, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=14129,
  serialized_end=14213,
)


_AUDIENCEAPPLYRESP = _descriptor.Descriptor(
  name='AudienceApplyResp',
  full_name='AudienceApplyResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='AudienceApplyResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='userId', full_name='AudienceApplyResp.userId', index=1,
      number=2, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='type', full_name='AudienceApplyResp.type', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='timestamp', full_name='AudienceApplyResp.timestamp', index=3,
      number=4, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=14215,
  serialized_end=14300,
)


_WEREWOLFPLAYERIDENTITYMODEL = _descriptor.Descriptor(
  name='WerewolfPlayerIdentityModel',
  full_name='WerewolfPlayerIdentityModel',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='userId', full_name='WerewolfPlayerIdentityModel.userId', index=0,
      number=1, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='identityType', full_name='WerewolfPlayerIdentityModel.identityType', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='campType', full_name='WerewolfPlayerIdentityModel.campType', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=14302,
  serialized_end=14387,
)


_IDENTITYNOTIFICATIONRESP = _descriptor.Descriptor(
  name='IdentityNotificationResp',
  full_name='IdentityNotificationResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='IdentityNotificationResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='players', full_name='IdentityNotificationResp.players', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=14389,
  serialized_end=14479,
)


_KILLINTHENIGHTRESP = _descriptor.Descriptor(
  name='KillInTheNightResp',
  full_name='KillInTheNightResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='KillInTheNightResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='killType', full_name='KillInTheNightResp.killType', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='players', full_name='KillInTheNightResp.players', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='countdown', full_name='KillInTheNightResp.countdown', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=14481,
  serialized_end=14592,
)


_IMPERIALCONCUBINENOTIFICATIONRESP = _descriptor.Descriptor(
  name='ImperialConcubineNotificationResp',
  full_name='ImperialConcubineNotificationResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='ImperialConcubineNotificationResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='players', full_name='ImperialConcubineNotificationResp.players', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='countdown', full_name='ImperialConcubineNotificationResp.countdown', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=14594,
  serialized_end=14704,
)


_MINISTERNOTIFICATIONRESP = _descriptor.Descriptor(
  name='MinisterNotificationResp',
  full_name='MinisterNotificationResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='MinisterNotificationResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='players', full_name='MinisterNotificationResp.players', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='sameCamp', full_name='MinisterNotificationResp.sameCamp', index=2,
      number=3, type=8, cpp_type=7, label=2,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='countdown', full_name='MinisterNotificationResp.countdown', index=3,
      number=4, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=14706,
  serialized_end=14825,
)


_SPYNOTIFICATIONRESP = _descriptor.Descriptor(
  name='SpyNotificationResp',
  full_name='SpyNotificationResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='SpyNotificationResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='players', full_name='SpyNotificationResp.players', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='countdown', full_name='SpyNotificationResp.countdown', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=14827,
  serialized_end=14923,
)


_WEREWOLFSELECTMODEL = _descriptor.Descriptor(
  name='WerewolfSelectModel',
  full_name='WerewolfSelectModel',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='player', full_name='WerewolfSelectModel.player', index=0,
      number=1, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='selectedBy', full_name='WerewolfSelectModel.selectedBy', index=1,
      number=2, type=5, cpp_type=1, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='disable', full_name='WerewolfSelectModel.disable', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=14925,
  serialized_end=15021,
)


_SPYRESULTRESP = _descriptor.Descriptor(
  name='SpyResultResp',
  full_name='SpyResultResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='SpyResultResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='player', full_name='SpyResultResp.player', index=1,
      number=2, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='countdown', full_name='SpyResultResp.countdown', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='campType', full_name='SpyResultResp.campType', index=3,
      number=4, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=15023,
  serialized_end=15130,
)


_EMPERORNOTIFICATIONRESP = _descriptor.Descriptor(
  name='EmperorNotificationResp',
  full_name='EmperorNotificationResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='EmperorNotificationResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='previousPlayer', full_name='EmperorNotificationResp.previousPlayer', index=1,
      number=2, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='nextPlayer', full_name='EmperorNotificationResp.nextPlayer', index=2,
      number=3, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='countdown', full_name='EmperorNotificationResp.countdown', index=3,
      number=4, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=15133,
  serialized_end=15282,
)


_ROUNDINFOMODEL = _descriptor.Descriptor(
  name='RoundInfoModel',
  full_name='RoundInfoModel',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='type', full_name='RoundInfoModel.type', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='status', full_name='RoundInfoModel.status', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=15284,
  serialized_end=15330,
)


_EMPERORCHOOSENOTIFICATIONRESP = _descriptor.Descriptor(
  name='EmperorChooseNotificationResp',
  full_name='EmperorChooseNotificationResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='EmperorChooseNotificationResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=15332,
  serialized_end=15380,
)


_SELECTCOMPLETERESP = _descriptor.Descriptor(
  name='SelectCompleteResp',
  full_name='SelectCompleteResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='SelectCompleteResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='type', full_name='SelectCompleteResp.type', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=15382,
  serialized_end=15433,
)


_ROUNDNOTIFICATIONRESP = _descriptor.Descriptor(
  name='RoundNotificationResp',
  full_name='RoundNotificationResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='RoundNotificationResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='roundType', full_name='RoundNotificationResp.roundType', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='isStart', full_name='RoundNotificationResp.isStart', index=2,
      number=3, type=8, cpp_type=7, label=2,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=15435,
  serialized_end=15511,
)


_ROOMSEATAPPLYCOUNTRESP = _descriptor.Descriptor(
  name='RoomSeatApplyCountResp',
  full_name='RoomSeatApplyCountResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='RoomSeatApplyCountResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='applyCount', full_name='RoomSeatApplyCountResp.applyCount', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='timestamp', full_name='RoomSeatApplyCountResp.timestamp', index=2,
      number=3, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=15513,
  serialized_end=15593,
)


_AUDIENCEPRESENTHEADRESP = _descriptor.Descriptor(
  name='AudiencePresentHeadResp',
  full_name='AudiencePresentHeadResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='AudiencePresentHeadResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='headThumb', full_name='AudiencePresentHeadResp.headThumb', index=1,
      number=2, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='timestamp', full_name='AudiencePresentHeadResp.timestamp', index=2,
      number=3, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=15595,
  serialized_end=15675,
)


_ANCHORMANAGERCHANGERESP = _descriptor.Descriptor(
  name='AnchorManagerChangeResp',
  full_name='AnchorManagerChangeResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='AnchorManagerChangeResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='userId', full_name='AnchorManagerChangeResp.userId', index=1,
      number=2, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='changeType', full_name='AnchorManagerChangeResp.changeType', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='timestamp', full_name='AnchorManagerChangeResp.timestamp', index=3,
      number=4, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=15677,
  serialized_end=15774,
)


_ROOMBULLETSCREENRESP = _descriptor.Descriptor(
  name='RoomBulletScreenResp',
  full_name='RoomBulletScreenResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='nickName', full_name='RoomBulletScreenResp.nickName', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='popularityLevel', full_name='RoomBulletScreenResp.popularityLevel', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=15776,
  serialized_end=15841,
)


_GOLDENMOUSENOTIFICATIONRESP = _descriptor.Descriptor(
  name='GoldenMouseNotificationResp',
  full_name='GoldenMouseNotificationResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomKey', full_name='GoldenMouseNotificationResp.roomKey', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='gold', full_name='GoldenMouseNotificationResp.gold', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='countdown', full_name='GoldenMouseNotificationResp.countdown', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=15843,
  serialized_end=15922,
)

_REQUESTHEADER.fields_by_name['captcha'].message_type = _CAPTCHA
_REQUEST.fields_by_name['header'].message_type = _REQUESTHEADER
_RESPONSE.fields_by_name['header'].message_type = _RESPONSEHEADER
_ERRORRESP.fields_by_name['errorParams'].message_type = _ERRORPARAM
_GAMEROOMMESSAGE.fields_by_name['present'].message_type = _PRESENTMODEL
_GAMEROOMMESSAGE.fields_by_name['voteInfo'].message_type = _VOTEINFO
_GAMEROOMMESSAGE.fields_by_name['fireworks'].message_type = _FIREWORKSMODEL
_GAMEROOMMESSAGE.fields_by_name['fromUserInfo'].message_type = _USERINFOMODEL
_GAMEROOMMESSAGE.fields_by_name['toUserInfo'].message_type = _USERINFOMODEL
_WEREWOLFKILLMODEL.fields_by_name['player'].message_type = _WEREWOLFPLAYERMODEL
_WEREWOLFRESULTMODEL.fields_by_name['player'].message_type = _WEREWOLFPLAYERMODEL
_WEREWOLFKILLRESP.fields_by_name['players'].message_type = _WEREWOLFKILLMODEL
_PROPHETNOTIFICATIONRESP.fields_by_name['players'].message_type = _WEREWOLFPLAYERMODEL
_PROPHETRESULTRESP.fields_by_name['player'].message_type = _WEREWOLFPLAYERMODEL
_WITCHRESCUENOTIFICATIONRESP.fields_by_name['player'].message_type = _WEREWOLFPLAYERMODEL
_WITCHPOISONNOTIFICATIONRESP.fields_by_name['players'].message_type = _WEREWOLFPLAYERMODEL
_HUNTERKILLRESP.fields_by_name['players'].message_type = _WEREWOLFPLAYERMODEL
_VOTENOTIFICATIONRESP.fields_by_name['players'].message_type = _WEREWOLFPLAYERMODEL
_PLAYERWAITINGRESP.fields_by_name['roundInfo'].message_type = _ROUNDINFOMODEL
_GAMERESULTRESP.fields_by_name['werewolves'].message_type = _WEREWOLFRESULTMODEL
_GAMERESULTRESP.fields_by_name['person'].message_type = _WEREWOLFRESULTMODEL
_GAMERESULTRESP.fields_by_name['lovers'].message_type = _WEREWOLFRESULTMODEL
_GAMERESULTRESP.fields_by_name['hunters'].message_type = _WEREWOLFRESULTMODEL
_GAMERESULTRESP.fields_by_name['nian'].message_type = _WEREWOLFRESULTMODEL
_GAMERESULTRESP.fields_by_name['loyal'].message_type = _WEREWOLFRESULTMODEL
_GAMERESULTRESP.fields_by_name['rebel'].message_type = _WEREWOLFRESULTMODEL
_PRESENTMODEL.fields_by_name['awardModel'].message_type = _AWARDINFOMODEL
_PRIVATEMESSAGE.fields_by_name['present'].message_type = _PRESENTMODEL
_PRIVATEMESSAGE.fields_by_name['gameRoomInvite'].message_type = _GAMEROOMINVITEMODEL
_PRIVATEMESSAGE.fields_by_name['fromUserInfo'].message_type = _USERINFOMODEL
_PRIVATEMESSAGE.fields_by_name['toUserInfo'].message_type = _USERINFOMODEL
_GETPRIVATEMESSAGERESP.fields_by_name['privateMessage'].message_type = _PRIVATEMESSAGE
_USERFRIENDSADDRESP.fields_by_name['userBasic'].message_type = _USERBASICRESP
_USERINFOCHANGERESP.fields_by_name['userBasic'].message_type = _USERBASICRESP
_VYINGIDENTITYRESP.fields_by_name['identities'].message_type = _VYINGIDENTITYMODEL
_CUPIDCHOOSERESP.fields_by_name['players'].message_type = _WEREWOLFPLAYERMODEL
_CUPIDRESULTRESP.fields_by_name['players'].message_type = _WEREWOLFPLAYERMODEL
_VOTESERGEANTRESP.fields_by_name['players'].message_type = _WEREWOLFPLAYERMODEL
_PROMOTIONTOSERGEANTRESP.fields_by_name['sergeant'].message_type = _WEREWOLFPLAYERMODEL
_SERGEANTCHANGERESP.fields_by_name['players'].message_type = _WEREWOLFPLAYERMODEL
_SERGEANTCHANGERESULTRESP.fields_by_name['oldSergeant'].message_type = _WEREWOLFPLAYERMODEL
_SERGEANTCHANGERESULTRESP.fields_by_name['sergeant'].message_type = _WEREWOLFPLAYERMODEL
_GETGUILDMESSAGERESP.fields_by_name['guildMessages'].message_type = _GUILDMESSAGEMODEL
_GUILDMESSAGEMODEL.fields_by_name['privateMessage'].message_type = _PRIVATEMESSAGE
_GUILDOFFLINEMESSAGERESP.fields_by_name['guildMessages'].message_type = _GUILDMESSAGEMODEL
_SELFDESTRUCTIONNOTIFICATION.fields_by_name['players'].message_type = _WEREWOLFPLAYERMODEL
_GUARDNOTIFICATIONRESP.fields_by_name['players'].message_type = _WEREWOLFPLAYERMODEL
_HUNTERKILLINTHENIGHTRESP.fields_by_name['players'].message_type = _WEREWOLFKILLMODEL
_VOTEINFO.fields_by_name['voteModels'].message_type = _USERVOTEINFO
_RECREATIONROOMOWNERCHANGERESP.fields_by_name['lastOwner'].message_type = _RECREATIONROOMOWNERCHANGEMODEL
_RECREATIONROOMOWNERCHANGERESP.fields_by_name['currentOwner'].message_type = _RECREATIONROOMOWNERCHANGEMODEL
_PENGUINFROZENMODEL.fields_by_name['player'].message_type = _WEREWOLFPLAYERMODEL
_PENGUINFROZENRESP.fields_by_name['players'].message_type = _PENGUINFROZENMODEL
_MATCHINGINFORESP.fields_by_name['matchingUsers'].message_type = _MATCHINGUSERMODEL
_QUALIFYINGROOMINFORESP.fields_by_name['roomMembers'].message_type = _ROOMMEMBERMODEL
_BIGEMOJIREQ.fields_by_name['bigEmoji'].message_type = _BIGEMOJIMODEL
_BIGEMOJIRESP.fields_by_name['bigEmoji'].message_type = _BIGEMOJIMODEL
_SONGINFOMODEL.fields_by_name['song'].message_type = _SONGMODEL
_SONGLISTCHANGERESP.fields_by_name['songInfo'].message_type = _SONGINFOMODEL
_SONGSTATUSRESP.fields_by_name['songInfo'].message_type = _SONGINFOMODEL
_SINGNOTIFYRESP.fields_by_name['songInfo'].message_type = _SONGINFOMODEL
_NIANKILLRESP.fields_by_name['players'].message_type = _WEREWOLFKILLMODEL
_NIANKILLCOMPLETERESP.fields_by_name['killed'].message_type = _WEREWOLFPLAYERMODEL
_KILLCONFIRMRESP.fields_by_name['killed'].message_type = _WEREWOLFPLAYERMODEL
_HUNTERKILLCONFIRMRESP.fields_by_name['killed'].message_type = _WEREWOLFPLAYERMODEL
_FIREWORKSMODEL.fields_by_name['awards'].message_type = _FIREWORKSAWARDMODEL
_BEAUTIFULWEREWOLFNOTIFICATIONRESP.fields_by_name['players'].message_type = _WEREWOLFPLAYERMODEL
_IDENTITYNOTIFICATIONRESP.fields_by_name['players'].message_type = _WEREWOLFPLAYERIDENTITYMODEL
_KILLINTHENIGHTRESP.fields_by_name['players'].message_type = _WEREWOLFKILLMODEL
_IMPERIALCONCUBINENOTIFICATIONRESP.fields_by_name['players'].message_type = _WEREWOLFPLAYERMODEL
_MINISTERNOTIFICATIONRESP.fields_by_name['players'].message_type = _WEREWOLFPLAYERMODEL
_SPYNOTIFICATIONRESP.fields_by_name['players'].message_type = _WEREWOLFSELECTMODEL
_WEREWOLFSELECTMODEL.fields_by_name['player'].message_type = _WEREWOLFPLAYERMODEL
_SPYRESULTRESP.fields_by_name['player'].message_type = _WEREWOLFPLAYERMODEL
_EMPERORNOTIFICATIONRESP.fields_by_name['previousPlayer'].message_type = _WEREWOLFPLAYERMODEL
_EMPERORNOTIFICATIONRESP.fields_by_name['nextPlayer'].message_type = _WEREWOLFPLAYERMODEL
DESCRIPTOR.message_types_by_name['RequestHeader'] = _REQUESTHEADER
DESCRIPTOR.message_types_by_name['Captcha'] = _CAPTCHA
DESCRIPTOR.message_types_by_name['Request'] = _REQUEST
DESCRIPTOR.message_types_by_name['ResponseHeader'] = _RESPONSEHEADER
DESCRIPTOR.message_types_by_name['Response'] = _RESPONSE
DESCRIPTOR.message_types_by_name['ErrorResp'] = _ERRORRESP
DESCRIPTOR.message_types_by_name['ErrorParam'] = _ERRORPARAM
DESCRIPTOR.message_types_by_name['SeqSyncReq'] = _SEQSYNCREQ
DESCRIPTOR.message_types_by_name['LoginReq'] = _LOGINREQ
DESCRIPTOR.message_types_by_name['GameRoomUserChangeResp'] = _GAMEROOMUSERCHANGERESP
DESCRIPTOR.message_types_by_name['GameRoomReadyResp'] = _GAMEROOMREADYRESP
DESCRIPTOR.message_types_by_name['GameRoomSeatSwitchResp'] = _GAMEROOMSEATSWITCHRESP
DESCRIPTOR.message_types_by_name['GameRoomMessage'] = _GAMEROOMMESSAGE
DESCRIPTOR.message_types_by_name['LevelUpResp'] = _LEVELUPRESP
DESCRIPTOR.message_types_by_name['WerewolfPlayerModel'] = _WEREWOLFPLAYERMODEL
DESCRIPTOR.message_types_by_name['WerewolfKillModel'] = _WEREWOLFKILLMODEL
DESCRIPTOR.message_types_by_name['WerewolfResultModel'] = _WEREWOLFRESULTMODEL
DESCRIPTOR.message_types_by_name['AllotIdentityResp'] = _ALLOTIDENTITYRESP
DESCRIPTOR.message_types_by_name['WerewolfNotificationResp'] = _WEREWOLFNOTIFICATIONRESP
DESCRIPTOR.message_types_by_name['WerewolfKillResp'] = _WEREWOLFKILLRESP
DESCRIPTOR.message_types_by_name['ProphetNotificationResp'] = _PROPHETNOTIFICATIONRESP
DESCRIPTOR.message_types_by_name['ProphetResultResp'] = _PROPHETRESULTRESP
DESCRIPTOR.message_types_by_name['WitchRescueNotificationResp'] = _WITCHRESCUENOTIFICATIONRESP
DESCRIPTOR.message_types_by_name['WitchPoisonNotificationResp'] = _WITCHPOISONNOTIFICATIONRESP
DESCRIPTOR.message_types_by_name['PlayerDiesNotificationResp'] = _PLAYERDIESNOTIFICATIONRESP
DESCRIPTOR.message_types_by_name['HunterKillResp'] = _HUNTERKILLRESP
DESCRIPTOR.message_types_by_name['VoteNotificationResp'] = _VOTENOTIFICATIONRESP
DESCRIPTOR.message_types_by_name['PlayerWaitingResp'] = _PLAYERWAITINGRESP
DESCRIPTOR.message_types_by_name['DayOrNightResp'] = _DAYORNIGHTRESP
DESCRIPTOR.message_types_by_name['GameResultResp'] = _GAMERESULTRESP
DESCRIPTOR.message_types_by_name['SpeakNotificationResp'] = _SPEAKNOTIFICATIONRESP
DESCRIPTOR.message_types_by_name['LoginResp'] = _LOGINRESP
DESCRIPTOR.message_types_by_name['OffSiteLoginResp'] = _OFFSITELOGINRESP
DESCRIPTOR.message_types_by_name['AgoraVoiceConditionChangeResp'] = _AGORAVOICECONDITIONCHANGERESP
DESCRIPTOR.message_types_by_name['AgoraBanVoiceResp'] = _AGORABANVOICERESP
DESCRIPTOR.message_types_by_name['AgoraFreeSpeakResp'] = _AGORAFREESPEAKRESP
DESCRIPTOR.message_types_by_name['WerewolfKillCompleteResp'] = _WEREWOLFKILLCOMPLETERESP
DESCRIPTOR.message_types_by_name['AbnormalNotificationResp'] = _ABNORMALNOTIFICATIONRESP
DESCRIPTOR.message_types_by_name['ExperienceChangeResp'] = _EXPERIENCECHANGERESP
DESCRIPTOR.message_types_by_name['PresentModel'] = _PRESENTMODEL
DESCRIPTOR.message_types_by_name['UserInfoModel'] = _USERINFOMODEL
DESCRIPTOR.message_types_by_name['AwardInfoModel'] = _AWARDINFOMODEL
DESCRIPTOR.message_types_by_name['PrivateMessage'] = _PRIVATEMESSAGE
DESCRIPTOR.message_types_by_name['PrivateMessageReadConfirmReq'] = _PRIVATEMESSAGEREADCONFIRMREQ
DESCRIPTOR.message_types_by_name['GetPrivateMessageReq'] = _GETPRIVATEMESSAGEREQ
DESCRIPTOR.message_types_by_name['GetPrivateMessageResp'] = _GETPRIVATEMESSAGERESP
DESCRIPTOR.message_types_by_name['GameRoomInviteModel'] = _GAMEROOMINVITEMODEL
DESCRIPTOR.message_types_by_name['UserFriendsAddResp'] = _USERFRIENDSADDRESP
DESCRIPTOR.message_types_by_name['UserFriendsDelResp'] = _USERFRIENDSDELRESP
DESCRIPTOR.message_types_by_name['UserInfoChangeResp'] = _USERINFOCHANGERESP
DESCRIPTOR.message_types_by_name['UserBasicResp'] = _USERBASICRESP
DESCRIPTOR.message_types_by_name['FriendRequestResp'] = _FRIENDREQUESTRESP
DESCRIPTOR.message_types_by_name['SystemMessage'] = _SYSTEMMESSAGE
DESCRIPTOR.message_types_by_name['BalanceChangeResp'] = _BALANCECHANGERESP
DESCRIPTOR.message_types_by_name['VyingIdentityModel'] = _VYINGIDENTITYMODEL
DESCRIPTOR.message_types_by_name['VyingIdentityResp'] = _VYINGIDENTITYRESP
DESCRIPTOR.message_types_by_name['GameStartNotificationResp'] = _GAMESTARTNOTIFICATIONRESP
DESCRIPTOR.message_types_by_name['GameRoomPasswordChangeResp'] = _GAMEROOMPASSWORDCHANGERESP
DESCRIPTOR.message_types_by_name['CupidChooseResp'] = _CUPIDCHOOSERESP
DESCRIPTOR.message_types_by_name['CupidResultResp'] = _CUPIDRESULTRESP
DESCRIPTOR.message_types_by_name['SergeantNotificationResp'] = _SERGEANTNOTIFICATIONRESP
DESCRIPTOR.message_types_by_name['VoteSergeantResp'] = _VOTESERGEANTRESP
DESCRIPTOR.message_types_by_name['PromotionToSergeantResp'] = _PROMOTIONTOSERGEANTRESP
DESCRIPTOR.message_types_by_name['SergeantChangeResp'] = _SERGEANTCHANGERESP
DESCRIPTOR.message_types_by_name['SergeantChangeResultResp'] = _SERGEANTCHANGERESULTRESP
DESCRIPTOR.message_types_by_name['SergeantElectNotificationResp'] = _SERGEANTELECTNOTIFICATIONRESP
DESCRIPTOR.message_types_by_name['SergeantRoundCompleteResp'] = _SERGEANTROUNDCOMPLETERESP
DESCRIPTOR.message_types_by_name['SergeantVoteRoundResp'] = _SERGEANTVOTEROUNDRESP
DESCRIPTOR.message_types_by_name['VoteCompleteResp'] = _VOTECOMPLETERESP
DESCRIPTOR.message_types_by_name['CupidRoundResp'] = _CUPIDROUNDRESP
DESCRIPTOR.message_types_by_name['GuildApplyResp'] = _GUILDAPPLYRESP
DESCRIPTOR.message_types_by_name['GuildMemberChangeResp'] = _GUILDMEMBERCHANGERESP
DESCRIPTOR.message_types_by_name['GuildDissolveResp'] = _GUILDDISSOLVERESP
DESCRIPTOR.message_types_by_name['GuildMemberTitleChangeResp'] = _GUILDMEMBERTITLECHANGERESP
DESCRIPTOR.message_types_by_name['GetGuildMessageReq'] = _GETGUILDMESSAGEREQ
DESCRIPTOR.message_types_by_name['GetGuildMessageResp'] = _GETGUILDMESSAGERESP
DESCRIPTOR.message_types_by_name['GuildMessageModel'] = _GUILDMESSAGEMODEL
DESCRIPTOR.message_types_by_name['GuildOfflineMessageResp'] = _GUILDOFFLINEMESSAGERESP
DESCRIPTOR.message_types_by_name['GuildRecruitCheckResp'] = _GUILDRECRUITCHECKRESP
DESCRIPTOR.message_types_by_name['LogoutNotificationResp'] = _LOGOUTNOTIFICATIONRESP
DESCRIPTOR.message_types_by_name['GameRoomSettingChangeResp'] = _GAMEROOMSETTINGCHANGERESP
DESCRIPTOR.message_types_by_name['GetOfflineMessageReq'] = _GETOFFLINEMESSAGEREQ
DESCRIPTOR.message_types_by_name['GetOfflineMessageResp'] = _GETOFFLINEMESSAGERESP
DESCRIPTOR.message_types_by_name['SelfDestructionNotification'] = _SELFDESTRUCTIONNOTIFICATION
DESCRIPTOR.message_types_by_name['GuardNotificationResp'] = _GUARDNOTIFICATIONRESP
DESCRIPTOR.message_types_by_name['SpeakEndResp'] = _SPEAKENDRESP
DESCRIPTOR.message_types_by_name['SelfDestructionStartResp'] = _SELFDESTRUCTIONSTARTRESP
DESCRIPTOR.message_types_by_name['SelfDestructionEndResp'] = _SELFDESTRUCTIONENDRESP
DESCRIPTOR.message_types_by_name['HunterKillInTheNightResp'] = _HUNTERKILLINTHENIGHTRESP
DESCRIPTOR.message_types_by_name['HunterNotificationResp'] = _HUNTERNOTIFICATIONRESP
DESCRIPTOR.message_types_by_name['VipInfoChangeResp'] = _VIPINFOCHANGERESP
DESCRIPTOR.message_types_by_name['UserLevelChangeResp'] = _USERLEVELCHANGERESP
DESCRIPTOR.message_types_by_name['VoteInfo'] = _VOTEINFO
DESCRIPTOR.message_types_by_name['UserVoteInfo'] = _USERVOTEINFO
DESCRIPTOR.message_types_by_name['RecreationRoomSettingChangeResp'] = _RECREATIONROOMSETTINGCHANGERESP
DESCRIPTOR.message_types_by_name['RecreationRoomOwnerChangeModel'] = _RECREATIONROOMOWNERCHANGEMODEL
DESCRIPTOR.message_types_by_name['RecreationRoomOwnerChangeResp'] = _RECREATIONROOMOWNERCHANGERESP
DESCRIPTOR.message_types_by_name['RecreationRoomHeart'] = _RECREATIONROOMHEART
DESCRIPTOR.message_types_by_name['HunterRoundNotificationResp'] = _HUNTERROUNDNOTIFICATIONRESP
DESCRIPTOR.message_types_by_name['TruthOrDareResp'] = _TRUTHORDARERESP
DESCRIPTOR.message_types_by_name['RoomSoundsResp'] = _ROOMSOUNDSRESP
DESCRIPTOR.message_types_by_name['PenguinFrozenModel'] = _PENGUINFROZENMODEL
DESCRIPTOR.message_types_by_name['PenguinFrozenResp'] = _PENGUINFROZENRESP
DESCRIPTOR.message_types_by_name['WerewolfFrozenResp'] = _WEREWOLFFROZENRESP
DESCRIPTOR.message_types_by_name['CockDiesResp'] = _COCKDIESRESP
DESCRIPTOR.message_types_by_name['BearRoarResp'] = _BEARROARRESP
DESCRIPTOR.message_types_by_name['PenguinFrozenNotificationResp'] = _PENGUINFROZENNOTIFICATIONRESP
DESCRIPTOR.message_types_by_name['TreasureBoxResp'] = _TREASUREBOXRESP
DESCRIPTOR.message_types_by_name['RoomInfoResp'] = _ROOMINFORESP
DESCRIPTOR.message_types_by_name['MatchingUserModel'] = _MATCHINGUSERMODEL
DESCRIPTOR.message_types_by_name['MatchingInfoResp'] = _MATCHINGINFORESP
DESCRIPTOR.message_types_by_name['QualifyingReadyResp'] = _QUALIFYINGREADYRESP
DESCRIPTOR.message_types_by_name['RematchNotificationResp'] = _REMATCHNOTIFICATIONRESP
DESCRIPTOR.message_types_by_name['RoomMemberModel'] = _ROOMMEMBERMODEL
DESCRIPTOR.message_types_by_name['QualifyingRoomInfoResp'] = _QUALIFYINGROOMINFORESP
DESCRIPTOR.message_types_by_name['TinyTierChangeResp'] = _TINYTIERCHANGERESP
DESCRIPTOR.message_types_by_name['UserCreditChangeResp'] = _USERCREDITCHANGERESP
DESCRIPTOR.message_types_by_name['SystemToastResp'] = _SYSTEMTOASTRESP
DESCRIPTOR.message_types_by_name['BigEmojiModel'] = _BIGEMOJIMODEL
DESCRIPTOR.message_types_by_name['BigEmojiReq'] = _BIGEMOJIREQ
DESCRIPTOR.message_types_by_name['BigEmojiResp'] = _BIGEMOJIRESP
DESCRIPTOR.message_types_by_name['SongModel'] = _SONGMODEL
DESCRIPTOR.message_types_by_name['SongInfoModel'] = _SONGINFOMODEL
DESCRIPTOR.message_types_by_name['SongListChangeResp'] = _SONGLISTCHANGERESP
DESCRIPTOR.message_types_by_name['SwitchSongResp'] = _SWITCHSONGRESP
DESCRIPTOR.message_types_by_name['SongStatusResp'] = _SONGSTATUSRESP
DESCRIPTOR.message_types_by_name['SingNotifyResp'] = _SINGNOTIFYRESP
DESCRIPTOR.message_types_by_name['RichManNotificationResp'] = _RICHMANNOTIFICATIONRESP
DESCRIPTOR.message_types_by_name['NianKillResp'] = _NIANKILLRESP
DESCRIPTOR.message_types_by_name['NianNotificationResp'] = _NIANNOTIFICATIONRESP
DESCRIPTOR.message_types_by_name['NianKillCompleteResp'] = _NIANKILLCOMPLETERESP
DESCRIPTOR.message_types_by_name['PetExploreStatusResp'] = _PETEXPLORESTATUSRESP
DESCRIPTOR.message_types_by_name['PropUpdateResp'] = _PROPUPDATERESP
DESCRIPTOR.message_types_by_name['KillConfirmResp'] = _KILLCONFIRMRESP
DESCRIPTOR.message_types_by_name['HunterKillConfirmResp'] = _HUNTERKILLCONFIRMRESP
DESCRIPTOR.message_types_by_name['FireworksAwardModel'] = _FIREWORKSAWARDMODEL
DESCRIPTOR.message_types_by_name['FireworksModel'] = _FIREWORKSMODEL
DESCRIPTOR.message_types_by_name['GuildTransferResp'] = _GUILDTRANSFERRESP
DESCRIPTOR.message_types_by_name['BeautifulWerewolfNotificationResp'] = _BEAUTIFULWEREWOLFNOTIFICATIONRESP
DESCRIPTOR.message_types_by_name['RecreationGameRoomBoardTextChangeResp'] = _RECREATIONGAMEROOMBOARDTEXTCHANGERESP
DESCRIPTOR.message_types_by_name['RecreationRoomSeatChangeResp'] = _RECREATIONROOMSEATCHANGERESP
DESCRIPTOR.message_types_by_name['NickIdChangeResp'] = _NICKIDCHANGERESP
DESCRIPTOR.message_types_by_name['RecreationRoomSeatInviteResp'] = _RECREATIONROOMSEATINVITERESP
DESCRIPTOR.message_types_by_name['RecreationRoomUserMuteResp'] = _RECREATIONROOMUSERMUTERESP
DESCRIPTOR.message_types_by_name['AudienceCountChangeResp'] = _AUDIENCECOUNTCHANGERESP
DESCRIPTOR.message_types_by_name['AudienceApplyResp'] = _AUDIENCEAPPLYRESP
DESCRIPTOR.message_types_by_name['WerewolfPlayerIdentityModel'] = _WEREWOLFPLAYERIDENTITYMODEL
DESCRIPTOR.message_types_by_name['IdentityNotificationResp'] = _IDENTITYNOTIFICATIONRESP
DESCRIPTOR.message_types_by_name['KillInTheNightResp'] = _KILLINTHENIGHTRESP
DESCRIPTOR.message_types_by_name['ImperialConcubineNotificationResp'] = _IMPERIALCONCUBINENOTIFICATIONRESP
DESCRIPTOR.message_types_by_name['MinisterNotificationResp'] = _MINISTERNOTIFICATIONRESP
DESCRIPTOR.message_types_by_name['SpyNotificationResp'] = _SPYNOTIFICATIONRESP
DESCRIPTOR.message_types_by_name['WerewolfSelectModel'] = _WEREWOLFSELECTMODEL
DESCRIPTOR.message_types_by_name['SpyResultResp'] = _SPYRESULTRESP
DESCRIPTOR.message_types_by_name['EmperorNotificationResp'] = _EMPERORNOTIFICATIONRESP
DESCRIPTOR.message_types_by_name['RoundInfoModel'] = _ROUNDINFOMODEL
DESCRIPTOR.message_types_by_name['EmperorChooseNotificationResp'] = _EMPERORCHOOSENOTIFICATIONRESP
DESCRIPTOR.message_types_by_name['SelectCompleteResp'] = _SELECTCOMPLETERESP
DESCRIPTOR.message_types_by_name['RoundNotificationResp'] = _ROUNDNOTIFICATIONRESP
DESCRIPTOR.message_types_by_name['RoomSeatApplyCountResp'] = _ROOMSEATAPPLYCOUNTRESP
DESCRIPTOR.message_types_by_name['AudiencePresentHeadResp'] = _AUDIENCEPRESENTHEADRESP
DESCRIPTOR.message_types_by_name['AnchorManagerChangeResp'] = _ANCHORMANAGERCHANGERESP
DESCRIPTOR.message_types_by_name['RoomBulletScreenResp'] = _ROOMBULLETSCREENRESP
DESCRIPTOR.message_types_by_name['GoldenMouseNotificationResp'] = _GOLDENMOUSENOTIFICATIONRESP

RequestHeader = _reflection.GeneratedProtocolMessageType('RequestHeader', (_message.Message,), dict(
  DESCRIPTOR = _REQUESTHEADER,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:RequestHeader)
  ))
_sym_db.RegisterMessage(RequestHeader)

Captcha = _reflection.GeneratedProtocolMessageType('Captcha', (_message.Message,), dict(
  DESCRIPTOR = _CAPTCHA,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:Captcha)
  ))
_sym_db.RegisterMessage(Captcha)

Request = _reflection.GeneratedProtocolMessageType('Request', (_message.Message,), dict(
  DESCRIPTOR = _REQUEST,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:Request)
  ))
_sym_db.RegisterMessage(Request)

ResponseHeader = _reflection.GeneratedProtocolMessageType('ResponseHeader', (_message.Message,), dict(
  DESCRIPTOR = _RESPONSEHEADER,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:ResponseHeader)
  ))
_sym_db.RegisterMessage(ResponseHeader)

Response = _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), dict(
  DESCRIPTOR = _RESPONSE,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:Response)
  ))
_sym_db.RegisterMessage(Response)

ErrorResp = _reflection.GeneratedProtocolMessageType('ErrorResp', (_message.Message,), dict(
  DESCRIPTOR = _ERRORRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:ErrorResp)
  ))
_sym_db.RegisterMessage(ErrorResp)

ErrorParam = _reflection.GeneratedProtocolMessageType('ErrorParam', (_message.Message,), dict(
  DESCRIPTOR = _ERRORPARAM,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:ErrorParam)
  ))
_sym_db.RegisterMessage(ErrorParam)

SeqSyncReq = _reflection.GeneratedProtocolMessageType('SeqSyncReq', (_message.Message,), dict(
  DESCRIPTOR = _SEQSYNCREQ,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:SeqSyncReq)
  ))
_sym_db.RegisterMessage(SeqSyncReq)

LoginReq = _reflection.GeneratedProtocolMessageType('LoginReq', (_message.Message,), dict(
  DESCRIPTOR = _LOGINREQ,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:LoginReq)
  ))
_sym_db.RegisterMessage(LoginReq)

GameRoomUserChangeResp = _reflection.GeneratedProtocolMessageType('GameRoomUserChangeResp', (_message.Message,), dict(
  DESCRIPTOR = _GAMEROOMUSERCHANGERESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:GameRoomUserChangeResp)
  ))
_sym_db.RegisterMessage(GameRoomUserChangeResp)

GameRoomReadyResp = _reflection.GeneratedProtocolMessageType('GameRoomReadyResp', (_message.Message,), dict(
  DESCRIPTOR = _GAMEROOMREADYRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:GameRoomReadyResp)
  ))
_sym_db.RegisterMessage(GameRoomReadyResp)

GameRoomSeatSwitchResp = _reflection.GeneratedProtocolMessageType('GameRoomSeatSwitchResp', (_message.Message,), dict(
  DESCRIPTOR = _GAMEROOMSEATSWITCHRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:GameRoomSeatSwitchResp)
  ))
_sym_db.RegisterMessage(GameRoomSeatSwitchResp)

GameRoomMessage = _reflection.GeneratedProtocolMessageType('GameRoomMessage', (_message.Message,), dict(
  DESCRIPTOR = _GAMEROOMMESSAGE,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:GameRoomMessage)
  ))
_sym_db.RegisterMessage(GameRoomMessage)

LevelUpResp = _reflection.GeneratedProtocolMessageType('LevelUpResp', (_message.Message,), dict(
  DESCRIPTOR = _LEVELUPRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:LevelUpResp)
  ))
_sym_db.RegisterMessage(LevelUpResp)

WerewolfPlayerModel = _reflection.GeneratedProtocolMessageType('WerewolfPlayerModel', (_message.Message,), dict(
  DESCRIPTOR = _WEREWOLFPLAYERMODEL,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:WerewolfPlayerModel)
  ))
_sym_db.RegisterMessage(WerewolfPlayerModel)

WerewolfKillModel = _reflection.GeneratedProtocolMessageType('WerewolfKillModel', (_message.Message,), dict(
  DESCRIPTOR = _WEREWOLFKILLMODEL,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:WerewolfKillModel)
  ))
_sym_db.RegisterMessage(WerewolfKillModel)

WerewolfResultModel = _reflection.GeneratedProtocolMessageType('WerewolfResultModel', (_message.Message,), dict(
  DESCRIPTOR = _WEREWOLFRESULTMODEL,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:WerewolfResultModel)
  ))
_sym_db.RegisterMessage(WerewolfResultModel)

AllotIdentityResp = _reflection.GeneratedProtocolMessageType('AllotIdentityResp', (_message.Message,), dict(
  DESCRIPTOR = _ALLOTIDENTITYRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:AllotIdentityResp)
  ))
_sym_db.RegisterMessage(AllotIdentityResp)

WerewolfNotificationResp = _reflection.GeneratedProtocolMessageType('WerewolfNotificationResp', (_message.Message,), dict(
  DESCRIPTOR = _WEREWOLFNOTIFICATIONRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:WerewolfNotificationResp)
  ))
_sym_db.RegisterMessage(WerewolfNotificationResp)

WerewolfKillResp = _reflection.GeneratedProtocolMessageType('WerewolfKillResp', (_message.Message,), dict(
  DESCRIPTOR = _WEREWOLFKILLRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:WerewolfKillResp)
  ))
_sym_db.RegisterMessage(WerewolfKillResp)

ProphetNotificationResp = _reflection.GeneratedProtocolMessageType('ProphetNotificationResp', (_message.Message,), dict(
  DESCRIPTOR = _PROPHETNOTIFICATIONRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:ProphetNotificationResp)
  ))
_sym_db.RegisterMessage(ProphetNotificationResp)

ProphetResultResp = _reflection.GeneratedProtocolMessageType('ProphetResultResp', (_message.Message,), dict(
  DESCRIPTOR = _PROPHETRESULTRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:ProphetResultResp)
  ))
_sym_db.RegisterMessage(ProphetResultResp)

WitchRescueNotificationResp = _reflection.GeneratedProtocolMessageType('WitchRescueNotificationResp', (_message.Message,), dict(
  DESCRIPTOR = _WITCHRESCUENOTIFICATIONRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:WitchRescueNotificationResp)
  ))
_sym_db.RegisterMessage(WitchRescueNotificationResp)

WitchPoisonNotificationResp = _reflection.GeneratedProtocolMessageType('WitchPoisonNotificationResp', (_message.Message,), dict(
  DESCRIPTOR = _WITCHPOISONNOTIFICATIONRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:WitchPoisonNotificationResp)
  ))
_sym_db.RegisterMessage(WitchPoisonNotificationResp)

PlayerDiesNotificationResp = _reflection.GeneratedProtocolMessageType('PlayerDiesNotificationResp', (_message.Message,), dict(
  DESCRIPTOR = _PLAYERDIESNOTIFICATIONRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:PlayerDiesNotificationResp)
  ))
_sym_db.RegisterMessage(PlayerDiesNotificationResp)

HunterKillResp = _reflection.GeneratedProtocolMessageType('HunterKillResp', (_message.Message,), dict(
  DESCRIPTOR = _HUNTERKILLRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:HunterKillResp)
  ))
_sym_db.RegisterMessage(HunterKillResp)

VoteNotificationResp = _reflection.GeneratedProtocolMessageType('VoteNotificationResp', (_message.Message,), dict(
  DESCRIPTOR = _VOTENOTIFICATIONRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:VoteNotificationResp)
  ))
_sym_db.RegisterMessage(VoteNotificationResp)

PlayerWaitingResp = _reflection.GeneratedProtocolMessageType('PlayerWaitingResp', (_message.Message,), dict(
  DESCRIPTOR = _PLAYERWAITINGRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:PlayerWaitingResp)
  ))
_sym_db.RegisterMessage(PlayerWaitingResp)

DayOrNightResp = _reflection.GeneratedProtocolMessageType('DayOrNightResp', (_message.Message,), dict(
  DESCRIPTOR = _DAYORNIGHTRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:DayOrNightResp)
  ))
_sym_db.RegisterMessage(DayOrNightResp)

GameResultResp = _reflection.GeneratedProtocolMessageType('GameResultResp', (_message.Message,), dict(
  DESCRIPTOR = _GAMERESULTRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:GameResultResp)
  ))
_sym_db.RegisterMessage(GameResultResp)

SpeakNotificationResp = _reflection.GeneratedProtocolMessageType('SpeakNotificationResp', (_message.Message,), dict(
  DESCRIPTOR = _SPEAKNOTIFICATIONRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:SpeakNotificationResp)
  ))
_sym_db.RegisterMessage(SpeakNotificationResp)

LoginResp = _reflection.GeneratedProtocolMessageType('LoginResp', (_message.Message,), dict(
  DESCRIPTOR = _LOGINRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:LoginResp)
  ))
_sym_db.RegisterMessage(LoginResp)

OffSiteLoginResp = _reflection.GeneratedProtocolMessageType('OffSiteLoginResp', (_message.Message,), dict(
  DESCRIPTOR = _OFFSITELOGINRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:OffSiteLoginResp)
  ))
_sym_db.RegisterMessage(OffSiteLoginResp)

AgoraVoiceConditionChangeResp = _reflection.GeneratedProtocolMessageType('AgoraVoiceConditionChangeResp', (_message.Message,), dict(
  DESCRIPTOR = _AGORAVOICECONDITIONCHANGERESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:AgoraVoiceConditionChangeResp)
  ))
_sym_db.RegisterMessage(AgoraVoiceConditionChangeResp)

AgoraBanVoiceResp = _reflection.GeneratedProtocolMessageType('AgoraBanVoiceResp', (_message.Message,), dict(
  DESCRIPTOR = _AGORABANVOICERESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:AgoraBanVoiceResp)
  ))
_sym_db.RegisterMessage(AgoraBanVoiceResp)

AgoraFreeSpeakResp = _reflection.GeneratedProtocolMessageType('AgoraFreeSpeakResp', (_message.Message,), dict(
  DESCRIPTOR = _AGORAFREESPEAKRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:AgoraFreeSpeakResp)
  ))
_sym_db.RegisterMessage(AgoraFreeSpeakResp)

WerewolfKillCompleteResp = _reflection.GeneratedProtocolMessageType('WerewolfKillCompleteResp', (_message.Message,), dict(
  DESCRIPTOR = _WEREWOLFKILLCOMPLETERESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:WerewolfKillCompleteResp)
  ))
_sym_db.RegisterMessage(WerewolfKillCompleteResp)

AbnormalNotificationResp = _reflection.GeneratedProtocolMessageType('AbnormalNotificationResp', (_message.Message,), dict(
  DESCRIPTOR = _ABNORMALNOTIFICATIONRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:AbnormalNotificationResp)
  ))
_sym_db.RegisterMessage(AbnormalNotificationResp)

ExperienceChangeResp = _reflection.GeneratedProtocolMessageType('ExperienceChangeResp', (_message.Message,), dict(
  DESCRIPTOR = _EXPERIENCECHANGERESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:ExperienceChangeResp)
  ))
_sym_db.RegisterMessage(ExperienceChangeResp)

PresentModel = _reflection.GeneratedProtocolMessageType('PresentModel', (_message.Message,), dict(
  DESCRIPTOR = _PRESENTMODEL,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:PresentModel)
  ))
_sym_db.RegisterMessage(PresentModel)

UserInfoModel = _reflection.GeneratedProtocolMessageType('UserInfoModel', (_message.Message,), dict(
  DESCRIPTOR = _USERINFOMODEL,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:UserInfoModel)
  ))
_sym_db.RegisterMessage(UserInfoModel)

AwardInfoModel = _reflection.GeneratedProtocolMessageType('AwardInfoModel', (_message.Message,), dict(
  DESCRIPTOR = _AWARDINFOMODEL,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:AwardInfoModel)
  ))
_sym_db.RegisterMessage(AwardInfoModel)

PrivateMessage = _reflection.GeneratedProtocolMessageType('PrivateMessage', (_message.Message,), dict(
  DESCRIPTOR = _PRIVATEMESSAGE,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:PrivateMessage)
  ))
_sym_db.RegisterMessage(PrivateMessage)

PrivateMessageReadConfirmReq = _reflection.GeneratedProtocolMessageType('PrivateMessageReadConfirmReq', (_message.Message,), dict(
  DESCRIPTOR = _PRIVATEMESSAGEREADCONFIRMREQ,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:PrivateMessageReadConfirmReq)
  ))
_sym_db.RegisterMessage(PrivateMessageReadConfirmReq)

GetPrivateMessageReq = _reflection.GeneratedProtocolMessageType('GetPrivateMessageReq', (_message.Message,), dict(
  DESCRIPTOR = _GETPRIVATEMESSAGEREQ,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:GetPrivateMessageReq)
  ))
_sym_db.RegisterMessage(GetPrivateMessageReq)

GetPrivateMessageResp = _reflection.GeneratedProtocolMessageType('GetPrivateMessageResp', (_message.Message,), dict(
  DESCRIPTOR = _GETPRIVATEMESSAGERESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:GetPrivateMessageResp)
  ))
_sym_db.RegisterMessage(GetPrivateMessageResp)

GameRoomInviteModel = _reflection.GeneratedProtocolMessageType('GameRoomInviteModel', (_message.Message,), dict(
  DESCRIPTOR = _GAMEROOMINVITEMODEL,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:GameRoomInviteModel)
  ))
_sym_db.RegisterMessage(GameRoomInviteModel)

UserFriendsAddResp = _reflection.GeneratedProtocolMessageType('UserFriendsAddResp', (_message.Message,), dict(
  DESCRIPTOR = _USERFRIENDSADDRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:UserFriendsAddResp)
  ))
_sym_db.RegisterMessage(UserFriendsAddResp)

UserFriendsDelResp = _reflection.GeneratedProtocolMessageType('UserFriendsDelResp', (_message.Message,), dict(
  DESCRIPTOR = _USERFRIENDSDELRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:UserFriendsDelResp)
  ))
_sym_db.RegisterMessage(UserFriendsDelResp)

UserInfoChangeResp = _reflection.GeneratedProtocolMessageType('UserInfoChangeResp', (_message.Message,), dict(
  DESCRIPTOR = _USERINFOCHANGERESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:UserInfoChangeResp)
  ))
_sym_db.RegisterMessage(UserInfoChangeResp)

UserBasicResp = _reflection.GeneratedProtocolMessageType('UserBasicResp', (_message.Message,), dict(
  DESCRIPTOR = _USERBASICRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:UserBasicResp)
  ))
_sym_db.RegisterMessage(UserBasicResp)

FriendRequestResp = _reflection.GeneratedProtocolMessageType('FriendRequestResp', (_message.Message,), dict(
  DESCRIPTOR = _FRIENDREQUESTRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:FriendRequestResp)
  ))
_sym_db.RegisterMessage(FriendRequestResp)

SystemMessage = _reflection.GeneratedProtocolMessageType('SystemMessage', (_message.Message,), dict(
  DESCRIPTOR = _SYSTEMMESSAGE,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:SystemMessage)
  ))
_sym_db.RegisterMessage(SystemMessage)

BalanceChangeResp = _reflection.GeneratedProtocolMessageType('BalanceChangeResp', (_message.Message,), dict(
  DESCRIPTOR = _BALANCECHANGERESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:BalanceChangeResp)
  ))
_sym_db.RegisterMessage(BalanceChangeResp)

VyingIdentityModel = _reflection.GeneratedProtocolMessageType('VyingIdentityModel', (_message.Message,), dict(
  DESCRIPTOR = _VYINGIDENTITYMODEL,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:VyingIdentityModel)
  ))
_sym_db.RegisterMessage(VyingIdentityModel)

VyingIdentityResp = _reflection.GeneratedProtocolMessageType('VyingIdentityResp', (_message.Message,), dict(
  DESCRIPTOR = _VYINGIDENTITYRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:VyingIdentityResp)
  ))
_sym_db.RegisterMessage(VyingIdentityResp)

GameStartNotificationResp = _reflection.GeneratedProtocolMessageType('GameStartNotificationResp', (_message.Message,), dict(
  DESCRIPTOR = _GAMESTARTNOTIFICATIONRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:GameStartNotificationResp)
  ))
_sym_db.RegisterMessage(GameStartNotificationResp)

GameRoomPasswordChangeResp = _reflection.GeneratedProtocolMessageType('GameRoomPasswordChangeResp', (_message.Message,), dict(
  DESCRIPTOR = _GAMEROOMPASSWORDCHANGERESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:GameRoomPasswordChangeResp)
  ))
_sym_db.RegisterMessage(GameRoomPasswordChangeResp)

CupidChooseResp = _reflection.GeneratedProtocolMessageType('CupidChooseResp', (_message.Message,), dict(
  DESCRIPTOR = _CUPIDCHOOSERESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:CupidChooseResp)
  ))
_sym_db.RegisterMessage(CupidChooseResp)

CupidResultResp = _reflection.GeneratedProtocolMessageType('CupidResultResp', (_message.Message,), dict(
  DESCRIPTOR = _CUPIDRESULTRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:CupidResultResp)
  ))
_sym_db.RegisterMessage(CupidResultResp)

SergeantNotificationResp = _reflection.GeneratedProtocolMessageType('SergeantNotificationResp', (_message.Message,), dict(
  DESCRIPTOR = _SERGEANTNOTIFICATIONRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:SergeantNotificationResp)
  ))
_sym_db.RegisterMessage(SergeantNotificationResp)

VoteSergeantResp = _reflection.GeneratedProtocolMessageType('VoteSergeantResp', (_message.Message,), dict(
  DESCRIPTOR = _VOTESERGEANTRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:VoteSergeantResp)
  ))
_sym_db.RegisterMessage(VoteSergeantResp)

PromotionToSergeantResp = _reflection.GeneratedProtocolMessageType('PromotionToSergeantResp', (_message.Message,), dict(
  DESCRIPTOR = _PROMOTIONTOSERGEANTRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:PromotionToSergeantResp)
  ))
_sym_db.RegisterMessage(PromotionToSergeantResp)

SergeantChangeResp = _reflection.GeneratedProtocolMessageType('SergeantChangeResp', (_message.Message,), dict(
  DESCRIPTOR = _SERGEANTCHANGERESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:SergeantChangeResp)
  ))
_sym_db.RegisterMessage(SergeantChangeResp)

SergeantChangeResultResp = _reflection.GeneratedProtocolMessageType('SergeantChangeResultResp', (_message.Message,), dict(
  DESCRIPTOR = _SERGEANTCHANGERESULTRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:SergeantChangeResultResp)
  ))
_sym_db.RegisterMessage(SergeantChangeResultResp)

SergeantElectNotificationResp = _reflection.GeneratedProtocolMessageType('SergeantElectNotificationResp', (_message.Message,), dict(
  DESCRIPTOR = _SERGEANTELECTNOTIFICATIONRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:SergeantElectNotificationResp)
  ))
_sym_db.RegisterMessage(SergeantElectNotificationResp)

SergeantRoundCompleteResp = _reflection.GeneratedProtocolMessageType('SergeantRoundCompleteResp', (_message.Message,), dict(
  DESCRIPTOR = _SERGEANTROUNDCOMPLETERESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:SergeantRoundCompleteResp)
  ))
_sym_db.RegisterMessage(SergeantRoundCompleteResp)

SergeantVoteRoundResp = _reflection.GeneratedProtocolMessageType('SergeantVoteRoundResp', (_message.Message,), dict(
  DESCRIPTOR = _SERGEANTVOTEROUNDRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:SergeantVoteRoundResp)
  ))
_sym_db.RegisterMessage(SergeantVoteRoundResp)

VoteCompleteResp = _reflection.GeneratedProtocolMessageType('VoteCompleteResp', (_message.Message,), dict(
  DESCRIPTOR = _VOTECOMPLETERESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:VoteCompleteResp)
  ))
_sym_db.RegisterMessage(VoteCompleteResp)

CupidRoundResp = _reflection.GeneratedProtocolMessageType('CupidRoundResp', (_message.Message,), dict(
  DESCRIPTOR = _CUPIDROUNDRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:CupidRoundResp)
  ))
_sym_db.RegisterMessage(CupidRoundResp)

GuildApplyResp = _reflection.GeneratedProtocolMessageType('GuildApplyResp', (_message.Message,), dict(
  DESCRIPTOR = _GUILDAPPLYRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:GuildApplyResp)
  ))
_sym_db.RegisterMessage(GuildApplyResp)

GuildMemberChangeResp = _reflection.GeneratedProtocolMessageType('GuildMemberChangeResp', (_message.Message,), dict(
  DESCRIPTOR = _GUILDMEMBERCHANGERESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:GuildMemberChangeResp)
  ))
_sym_db.RegisterMessage(GuildMemberChangeResp)

GuildDissolveResp = _reflection.GeneratedProtocolMessageType('GuildDissolveResp', (_message.Message,), dict(
  DESCRIPTOR = _GUILDDISSOLVERESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:GuildDissolveResp)
  ))
_sym_db.RegisterMessage(GuildDissolveResp)

GuildMemberTitleChangeResp = _reflection.GeneratedProtocolMessageType('GuildMemberTitleChangeResp', (_message.Message,), dict(
  DESCRIPTOR = _GUILDMEMBERTITLECHANGERESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:GuildMemberTitleChangeResp)
  ))
_sym_db.RegisterMessage(GuildMemberTitleChangeResp)

GetGuildMessageReq = _reflection.GeneratedProtocolMessageType('GetGuildMessageReq', (_message.Message,), dict(
  DESCRIPTOR = _GETGUILDMESSAGEREQ,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:GetGuildMessageReq)
  ))
_sym_db.RegisterMessage(GetGuildMessageReq)

GetGuildMessageResp = _reflection.GeneratedProtocolMessageType('GetGuildMessageResp', (_message.Message,), dict(
  DESCRIPTOR = _GETGUILDMESSAGERESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:GetGuildMessageResp)
  ))
_sym_db.RegisterMessage(GetGuildMessageResp)

GuildMessageModel = _reflection.GeneratedProtocolMessageType('GuildMessageModel', (_message.Message,), dict(
  DESCRIPTOR = _GUILDMESSAGEMODEL,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:GuildMessageModel)
  ))
_sym_db.RegisterMessage(GuildMessageModel)

GuildOfflineMessageResp = _reflection.GeneratedProtocolMessageType('GuildOfflineMessageResp', (_message.Message,), dict(
  DESCRIPTOR = _GUILDOFFLINEMESSAGERESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:GuildOfflineMessageResp)
  ))
_sym_db.RegisterMessage(GuildOfflineMessageResp)

GuildRecruitCheckResp = _reflection.GeneratedProtocolMessageType('GuildRecruitCheckResp', (_message.Message,), dict(
  DESCRIPTOR = _GUILDRECRUITCHECKRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:GuildRecruitCheckResp)
  ))
_sym_db.RegisterMessage(GuildRecruitCheckResp)

LogoutNotificationResp = _reflection.GeneratedProtocolMessageType('LogoutNotificationResp', (_message.Message,), dict(
  DESCRIPTOR = _LOGOUTNOTIFICATIONRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:LogoutNotificationResp)
  ))
_sym_db.RegisterMessage(LogoutNotificationResp)

GameRoomSettingChangeResp = _reflection.GeneratedProtocolMessageType('GameRoomSettingChangeResp', (_message.Message,), dict(
  DESCRIPTOR = _GAMEROOMSETTINGCHANGERESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:GameRoomSettingChangeResp)
  ))
_sym_db.RegisterMessage(GameRoomSettingChangeResp)

GetOfflineMessageReq = _reflection.GeneratedProtocolMessageType('GetOfflineMessageReq', (_message.Message,), dict(
  DESCRIPTOR = _GETOFFLINEMESSAGEREQ,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:GetOfflineMessageReq)
  ))
_sym_db.RegisterMessage(GetOfflineMessageReq)

GetOfflineMessageResp = _reflection.GeneratedProtocolMessageType('GetOfflineMessageResp', (_message.Message,), dict(
  DESCRIPTOR = _GETOFFLINEMESSAGERESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:GetOfflineMessageResp)
  ))
_sym_db.RegisterMessage(GetOfflineMessageResp)

SelfDestructionNotification = _reflection.GeneratedProtocolMessageType('SelfDestructionNotification', (_message.Message,), dict(
  DESCRIPTOR = _SELFDESTRUCTIONNOTIFICATION,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:SelfDestructionNotification)
  ))
_sym_db.RegisterMessage(SelfDestructionNotification)

GuardNotificationResp = _reflection.GeneratedProtocolMessageType('GuardNotificationResp', (_message.Message,), dict(
  DESCRIPTOR = _GUARDNOTIFICATIONRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:GuardNotificationResp)
  ))
_sym_db.RegisterMessage(GuardNotificationResp)

SpeakEndResp = _reflection.GeneratedProtocolMessageType('SpeakEndResp', (_message.Message,), dict(
  DESCRIPTOR = _SPEAKENDRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:SpeakEndResp)
  ))
_sym_db.RegisterMessage(SpeakEndResp)

SelfDestructionStartResp = _reflection.GeneratedProtocolMessageType('SelfDestructionStartResp', (_message.Message,), dict(
  DESCRIPTOR = _SELFDESTRUCTIONSTARTRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:SelfDestructionStartResp)
  ))
_sym_db.RegisterMessage(SelfDestructionStartResp)

SelfDestructionEndResp = _reflection.GeneratedProtocolMessageType('SelfDestructionEndResp', (_message.Message,), dict(
  DESCRIPTOR = _SELFDESTRUCTIONENDRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:SelfDestructionEndResp)
  ))
_sym_db.RegisterMessage(SelfDestructionEndResp)

HunterKillInTheNightResp = _reflection.GeneratedProtocolMessageType('HunterKillInTheNightResp', (_message.Message,), dict(
  DESCRIPTOR = _HUNTERKILLINTHENIGHTRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:HunterKillInTheNightResp)
  ))
_sym_db.RegisterMessage(HunterKillInTheNightResp)

HunterNotificationResp = _reflection.GeneratedProtocolMessageType('HunterNotificationResp', (_message.Message,), dict(
  DESCRIPTOR = _HUNTERNOTIFICATIONRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:HunterNotificationResp)
  ))
_sym_db.RegisterMessage(HunterNotificationResp)

VipInfoChangeResp = _reflection.GeneratedProtocolMessageType('VipInfoChangeResp', (_message.Message,), dict(
  DESCRIPTOR = _VIPINFOCHANGERESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:VipInfoChangeResp)
  ))
_sym_db.RegisterMessage(VipInfoChangeResp)

UserLevelChangeResp = _reflection.GeneratedProtocolMessageType('UserLevelChangeResp', (_message.Message,), dict(
  DESCRIPTOR = _USERLEVELCHANGERESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:UserLevelChangeResp)
  ))
_sym_db.RegisterMessage(UserLevelChangeResp)

VoteInfo = _reflection.GeneratedProtocolMessageType('VoteInfo', (_message.Message,), dict(
  DESCRIPTOR = _VOTEINFO,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:VoteInfo)
  ))
_sym_db.RegisterMessage(VoteInfo)

UserVoteInfo = _reflection.GeneratedProtocolMessageType('UserVoteInfo', (_message.Message,), dict(
  DESCRIPTOR = _USERVOTEINFO,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:UserVoteInfo)
  ))
_sym_db.RegisterMessage(UserVoteInfo)

RecreationRoomSettingChangeResp = _reflection.GeneratedProtocolMessageType('RecreationRoomSettingChangeResp', (_message.Message,), dict(
  DESCRIPTOR = _RECREATIONROOMSETTINGCHANGERESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:RecreationRoomSettingChangeResp)
  ))
_sym_db.RegisterMessage(RecreationRoomSettingChangeResp)

RecreationRoomOwnerChangeModel = _reflection.GeneratedProtocolMessageType('RecreationRoomOwnerChangeModel', (_message.Message,), dict(
  DESCRIPTOR = _RECREATIONROOMOWNERCHANGEMODEL,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:RecreationRoomOwnerChangeModel)
  ))
_sym_db.RegisterMessage(RecreationRoomOwnerChangeModel)

RecreationRoomOwnerChangeResp = _reflection.GeneratedProtocolMessageType('RecreationRoomOwnerChangeResp', (_message.Message,), dict(
  DESCRIPTOR = _RECREATIONROOMOWNERCHANGERESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:RecreationRoomOwnerChangeResp)
  ))
_sym_db.RegisterMessage(RecreationRoomOwnerChangeResp)

RecreationRoomHeart = _reflection.GeneratedProtocolMessageType('RecreationRoomHeart', (_message.Message,), dict(
  DESCRIPTOR = _RECREATIONROOMHEART,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:RecreationRoomHeart)
  ))
_sym_db.RegisterMessage(RecreationRoomHeart)

HunterRoundNotificationResp = _reflection.GeneratedProtocolMessageType('HunterRoundNotificationResp', (_message.Message,), dict(
  DESCRIPTOR = _HUNTERROUNDNOTIFICATIONRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:HunterRoundNotificationResp)
  ))
_sym_db.RegisterMessage(HunterRoundNotificationResp)

TruthOrDareResp = _reflection.GeneratedProtocolMessageType('TruthOrDareResp', (_message.Message,), dict(
  DESCRIPTOR = _TRUTHORDARERESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:TruthOrDareResp)
  ))
_sym_db.RegisterMessage(TruthOrDareResp)

RoomSoundsResp = _reflection.GeneratedProtocolMessageType('RoomSoundsResp', (_message.Message,), dict(
  DESCRIPTOR = _ROOMSOUNDSRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:RoomSoundsResp)
  ))
_sym_db.RegisterMessage(RoomSoundsResp)

PenguinFrozenModel = _reflection.GeneratedProtocolMessageType('PenguinFrozenModel', (_message.Message,), dict(
  DESCRIPTOR = _PENGUINFROZENMODEL,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:PenguinFrozenModel)
  ))
_sym_db.RegisterMessage(PenguinFrozenModel)

PenguinFrozenResp = _reflection.GeneratedProtocolMessageType('PenguinFrozenResp', (_message.Message,), dict(
  DESCRIPTOR = _PENGUINFROZENRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:PenguinFrozenResp)
  ))
_sym_db.RegisterMessage(PenguinFrozenResp)

WerewolfFrozenResp = _reflection.GeneratedProtocolMessageType('WerewolfFrozenResp', (_message.Message,), dict(
  DESCRIPTOR = _WEREWOLFFROZENRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:WerewolfFrozenResp)
  ))
_sym_db.RegisterMessage(WerewolfFrozenResp)

CockDiesResp = _reflection.GeneratedProtocolMessageType('CockDiesResp', (_message.Message,), dict(
  DESCRIPTOR = _COCKDIESRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:CockDiesResp)
  ))
_sym_db.RegisterMessage(CockDiesResp)

BearRoarResp = _reflection.GeneratedProtocolMessageType('BearRoarResp', (_message.Message,), dict(
  DESCRIPTOR = _BEARROARRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:BearRoarResp)
  ))
_sym_db.RegisterMessage(BearRoarResp)

PenguinFrozenNotificationResp = _reflection.GeneratedProtocolMessageType('PenguinFrozenNotificationResp', (_message.Message,), dict(
  DESCRIPTOR = _PENGUINFROZENNOTIFICATIONRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:PenguinFrozenNotificationResp)
  ))
_sym_db.RegisterMessage(PenguinFrozenNotificationResp)

TreasureBoxResp = _reflection.GeneratedProtocolMessageType('TreasureBoxResp', (_message.Message,), dict(
  DESCRIPTOR = _TREASUREBOXRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:TreasureBoxResp)
  ))
_sym_db.RegisterMessage(TreasureBoxResp)

RoomInfoResp = _reflection.GeneratedProtocolMessageType('RoomInfoResp', (_message.Message,), dict(
  DESCRIPTOR = _ROOMINFORESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:RoomInfoResp)
  ))
_sym_db.RegisterMessage(RoomInfoResp)

MatchingUserModel = _reflection.GeneratedProtocolMessageType('MatchingUserModel', (_message.Message,), dict(
  DESCRIPTOR = _MATCHINGUSERMODEL,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:MatchingUserModel)
  ))
_sym_db.RegisterMessage(MatchingUserModel)

MatchingInfoResp = _reflection.GeneratedProtocolMessageType('MatchingInfoResp', (_message.Message,), dict(
  DESCRIPTOR = _MATCHINGINFORESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:MatchingInfoResp)
  ))
_sym_db.RegisterMessage(MatchingInfoResp)

QualifyingReadyResp = _reflection.GeneratedProtocolMessageType('QualifyingReadyResp', (_message.Message,), dict(
  DESCRIPTOR = _QUALIFYINGREADYRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:QualifyingReadyResp)
  ))
_sym_db.RegisterMessage(QualifyingReadyResp)

RematchNotificationResp = _reflection.GeneratedProtocolMessageType('RematchNotificationResp', (_message.Message,), dict(
  DESCRIPTOR = _REMATCHNOTIFICATIONRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:RematchNotificationResp)
  ))
_sym_db.RegisterMessage(RematchNotificationResp)

RoomMemberModel = _reflection.GeneratedProtocolMessageType('RoomMemberModel', (_message.Message,), dict(
  DESCRIPTOR = _ROOMMEMBERMODEL,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:RoomMemberModel)
  ))
_sym_db.RegisterMessage(RoomMemberModel)

QualifyingRoomInfoResp = _reflection.GeneratedProtocolMessageType('QualifyingRoomInfoResp', (_message.Message,), dict(
  DESCRIPTOR = _QUALIFYINGROOMINFORESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:QualifyingRoomInfoResp)
  ))
_sym_db.RegisterMessage(QualifyingRoomInfoResp)

TinyTierChangeResp = _reflection.GeneratedProtocolMessageType('TinyTierChangeResp', (_message.Message,), dict(
  DESCRIPTOR = _TINYTIERCHANGERESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:TinyTierChangeResp)
  ))
_sym_db.RegisterMessage(TinyTierChangeResp)

UserCreditChangeResp = _reflection.GeneratedProtocolMessageType('UserCreditChangeResp', (_message.Message,), dict(
  DESCRIPTOR = _USERCREDITCHANGERESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:UserCreditChangeResp)
  ))
_sym_db.RegisterMessage(UserCreditChangeResp)

SystemToastResp = _reflection.GeneratedProtocolMessageType('SystemToastResp', (_message.Message,), dict(
  DESCRIPTOR = _SYSTEMTOASTRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:SystemToastResp)
  ))
_sym_db.RegisterMessage(SystemToastResp)

BigEmojiModel = _reflection.GeneratedProtocolMessageType('BigEmojiModel', (_message.Message,), dict(
  DESCRIPTOR = _BIGEMOJIMODEL,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:BigEmojiModel)
  ))
_sym_db.RegisterMessage(BigEmojiModel)

BigEmojiReq = _reflection.GeneratedProtocolMessageType('BigEmojiReq', (_message.Message,), dict(
  DESCRIPTOR = _BIGEMOJIREQ,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:BigEmojiReq)
  ))
_sym_db.RegisterMessage(BigEmojiReq)

BigEmojiResp = _reflection.GeneratedProtocolMessageType('BigEmojiResp', (_message.Message,), dict(
  DESCRIPTOR = _BIGEMOJIRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:BigEmojiResp)
  ))
_sym_db.RegisterMessage(BigEmojiResp)

SongModel = _reflection.GeneratedProtocolMessageType('SongModel', (_message.Message,), dict(
  DESCRIPTOR = _SONGMODEL,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:SongModel)
  ))
_sym_db.RegisterMessage(SongModel)

SongInfoModel = _reflection.GeneratedProtocolMessageType('SongInfoModel', (_message.Message,), dict(
  DESCRIPTOR = _SONGINFOMODEL,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:SongInfoModel)
  ))
_sym_db.RegisterMessage(SongInfoModel)

SongListChangeResp = _reflection.GeneratedProtocolMessageType('SongListChangeResp', (_message.Message,), dict(
  DESCRIPTOR = _SONGLISTCHANGERESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:SongListChangeResp)
  ))
_sym_db.RegisterMessage(SongListChangeResp)

SwitchSongResp = _reflection.GeneratedProtocolMessageType('SwitchSongResp', (_message.Message,), dict(
  DESCRIPTOR = _SWITCHSONGRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:SwitchSongResp)
  ))
_sym_db.RegisterMessage(SwitchSongResp)

SongStatusResp = _reflection.GeneratedProtocolMessageType('SongStatusResp', (_message.Message,), dict(
  DESCRIPTOR = _SONGSTATUSRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:SongStatusResp)
  ))
_sym_db.RegisterMessage(SongStatusResp)

SingNotifyResp = _reflection.GeneratedProtocolMessageType('SingNotifyResp', (_message.Message,), dict(
  DESCRIPTOR = _SINGNOTIFYRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:SingNotifyResp)
  ))
_sym_db.RegisterMessage(SingNotifyResp)

RichManNotificationResp = _reflection.GeneratedProtocolMessageType('RichManNotificationResp', (_message.Message,), dict(
  DESCRIPTOR = _RICHMANNOTIFICATIONRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:RichManNotificationResp)
  ))
_sym_db.RegisterMessage(RichManNotificationResp)

NianKillResp = _reflection.GeneratedProtocolMessageType('NianKillResp', (_message.Message,), dict(
  DESCRIPTOR = _NIANKILLRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:NianKillResp)
  ))
_sym_db.RegisterMessage(NianKillResp)

NianNotificationResp = _reflection.GeneratedProtocolMessageType('NianNotificationResp', (_message.Message,), dict(
  DESCRIPTOR = _NIANNOTIFICATIONRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:NianNotificationResp)
  ))
_sym_db.RegisterMessage(NianNotificationResp)

NianKillCompleteResp = _reflection.GeneratedProtocolMessageType('NianKillCompleteResp', (_message.Message,), dict(
  DESCRIPTOR = _NIANKILLCOMPLETERESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:NianKillCompleteResp)
  ))
_sym_db.RegisterMessage(NianKillCompleteResp)

PetExploreStatusResp = _reflection.GeneratedProtocolMessageType('PetExploreStatusResp', (_message.Message,), dict(
  DESCRIPTOR = _PETEXPLORESTATUSRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:PetExploreStatusResp)
  ))
_sym_db.RegisterMessage(PetExploreStatusResp)

PropUpdateResp = _reflection.GeneratedProtocolMessageType('PropUpdateResp', (_message.Message,), dict(
  DESCRIPTOR = _PROPUPDATERESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:PropUpdateResp)
  ))
_sym_db.RegisterMessage(PropUpdateResp)

KillConfirmResp = _reflection.GeneratedProtocolMessageType('KillConfirmResp', (_message.Message,), dict(
  DESCRIPTOR = _KILLCONFIRMRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:KillConfirmResp)
  ))
_sym_db.RegisterMessage(KillConfirmResp)

HunterKillConfirmResp = _reflection.GeneratedProtocolMessageType('HunterKillConfirmResp', (_message.Message,), dict(
  DESCRIPTOR = _HUNTERKILLCONFIRMRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:HunterKillConfirmResp)
  ))
_sym_db.RegisterMessage(HunterKillConfirmResp)

FireworksAwardModel = _reflection.GeneratedProtocolMessageType('FireworksAwardModel', (_message.Message,), dict(
  DESCRIPTOR = _FIREWORKSAWARDMODEL,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:FireworksAwardModel)
  ))
_sym_db.RegisterMessage(FireworksAwardModel)

FireworksModel = _reflection.GeneratedProtocolMessageType('FireworksModel', (_message.Message,), dict(
  DESCRIPTOR = _FIREWORKSMODEL,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:FireworksModel)
  ))
_sym_db.RegisterMessage(FireworksModel)

GuildTransferResp = _reflection.GeneratedProtocolMessageType('GuildTransferResp', (_message.Message,), dict(
  DESCRIPTOR = _GUILDTRANSFERRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:GuildTransferResp)
  ))
_sym_db.RegisterMessage(GuildTransferResp)

BeautifulWerewolfNotificationResp = _reflection.GeneratedProtocolMessageType('BeautifulWerewolfNotificationResp', (_message.Message,), dict(
  DESCRIPTOR = _BEAUTIFULWEREWOLFNOTIFICATIONRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:BeautifulWerewolfNotificationResp)
  ))
_sym_db.RegisterMessage(BeautifulWerewolfNotificationResp)

RecreationGameRoomBoardTextChangeResp = _reflection.GeneratedProtocolMessageType('RecreationGameRoomBoardTextChangeResp', (_message.Message,), dict(
  DESCRIPTOR = _RECREATIONGAMEROOMBOARDTEXTCHANGERESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:RecreationGameRoomBoardTextChangeResp)
  ))
_sym_db.RegisterMessage(RecreationGameRoomBoardTextChangeResp)

RecreationRoomSeatChangeResp = _reflection.GeneratedProtocolMessageType('RecreationRoomSeatChangeResp', (_message.Message,), dict(
  DESCRIPTOR = _RECREATIONROOMSEATCHANGERESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:RecreationRoomSeatChangeResp)
  ))
_sym_db.RegisterMessage(RecreationRoomSeatChangeResp)

NickIdChangeResp = _reflection.GeneratedProtocolMessageType('NickIdChangeResp', (_message.Message,), dict(
  DESCRIPTOR = _NICKIDCHANGERESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:NickIdChangeResp)
  ))
_sym_db.RegisterMessage(NickIdChangeResp)

RecreationRoomSeatInviteResp = _reflection.GeneratedProtocolMessageType('RecreationRoomSeatInviteResp', (_message.Message,), dict(
  DESCRIPTOR = _RECREATIONROOMSEATINVITERESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:RecreationRoomSeatInviteResp)
  ))
_sym_db.RegisterMessage(RecreationRoomSeatInviteResp)

RecreationRoomUserMuteResp = _reflection.GeneratedProtocolMessageType('RecreationRoomUserMuteResp', (_message.Message,), dict(
  DESCRIPTOR = _RECREATIONROOMUSERMUTERESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:RecreationRoomUserMuteResp)
  ))
_sym_db.RegisterMessage(RecreationRoomUserMuteResp)

AudienceCountChangeResp = _reflection.GeneratedProtocolMessageType('AudienceCountChangeResp', (_message.Message,), dict(
  DESCRIPTOR = _AUDIENCECOUNTCHANGERESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:AudienceCountChangeResp)
  ))
_sym_db.RegisterMessage(AudienceCountChangeResp)

AudienceApplyResp = _reflection.GeneratedProtocolMessageType('AudienceApplyResp', (_message.Message,), dict(
  DESCRIPTOR = _AUDIENCEAPPLYRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:AudienceApplyResp)
  ))
_sym_db.RegisterMessage(AudienceApplyResp)

WerewolfPlayerIdentityModel = _reflection.GeneratedProtocolMessageType('WerewolfPlayerIdentityModel', (_message.Message,), dict(
  DESCRIPTOR = _WEREWOLFPLAYERIDENTITYMODEL,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:WerewolfPlayerIdentityModel)
  ))
_sym_db.RegisterMessage(WerewolfPlayerIdentityModel)

IdentityNotificationResp = _reflection.GeneratedProtocolMessageType('IdentityNotificationResp', (_message.Message,), dict(
  DESCRIPTOR = _IDENTITYNOTIFICATIONRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:IdentityNotificationResp)
  ))
_sym_db.RegisterMessage(IdentityNotificationResp)

KillInTheNightResp = _reflection.GeneratedProtocolMessageType('KillInTheNightResp', (_message.Message,), dict(
  DESCRIPTOR = _KILLINTHENIGHTRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:KillInTheNightResp)
  ))
_sym_db.RegisterMessage(KillInTheNightResp)

ImperialConcubineNotificationResp = _reflection.GeneratedProtocolMessageType('ImperialConcubineNotificationResp', (_message.Message,), dict(
  DESCRIPTOR = _IMPERIALCONCUBINENOTIFICATIONRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:ImperialConcubineNotificationResp)
  ))
_sym_db.RegisterMessage(ImperialConcubineNotificationResp)

MinisterNotificationResp = _reflection.GeneratedProtocolMessageType('MinisterNotificationResp', (_message.Message,), dict(
  DESCRIPTOR = _MINISTERNOTIFICATIONRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:MinisterNotificationResp)
  ))
_sym_db.RegisterMessage(MinisterNotificationResp)

SpyNotificationResp = _reflection.GeneratedProtocolMessageType('SpyNotificationResp', (_message.Message,), dict(
  DESCRIPTOR = _SPYNOTIFICATIONRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:SpyNotificationResp)
  ))
_sym_db.RegisterMessage(SpyNotificationResp)

WerewolfSelectModel = _reflection.GeneratedProtocolMessageType('WerewolfSelectModel', (_message.Message,), dict(
  DESCRIPTOR = _WEREWOLFSELECTMODEL,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:WerewolfSelectModel)
  ))
_sym_db.RegisterMessage(WerewolfSelectModel)

SpyResultResp = _reflection.GeneratedProtocolMessageType('SpyResultResp', (_message.Message,), dict(
  DESCRIPTOR = _SPYRESULTRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:SpyResultResp)
  ))
_sym_db.RegisterMessage(SpyResultResp)

EmperorNotificationResp = _reflection.GeneratedProtocolMessageType('EmperorNotificationResp', (_message.Message,), dict(
  DESCRIPTOR = _EMPERORNOTIFICATIONRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:EmperorNotificationResp)
  ))
_sym_db.RegisterMessage(EmperorNotificationResp)

RoundInfoModel = _reflection.GeneratedProtocolMessageType('RoundInfoModel', (_message.Message,), dict(
  DESCRIPTOR = _ROUNDINFOMODEL,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:RoundInfoModel)
  ))
_sym_db.RegisterMessage(RoundInfoModel)

EmperorChooseNotificationResp = _reflection.GeneratedProtocolMessageType('EmperorChooseNotificationResp', (_message.Message,), dict(
  DESCRIPTOR = _EMPERORCHOOSENOTIFICATIONRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:EmperorChooseNotificationResp)
  ))
_sym_db.RegisterMessage(EmperorChooseNotificationResp)

SelectCompleteResp = _reflection.GeneratedProtocolMessageType('SelectCompleteResp', (_message.Message,), dict(
  DESCRIPTOR = _SELECTCOMPLETERESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:SelectCompleteResp)
  ))
_sym_db.RegisterMessage(SelectCompleteResp)

RoundNotificationResp = _reflection.GeneratedProtocolMessageType('RoundNotificationResp', (_message.Message,), dict(
  DESCRIPTOR = _ROUNDNOTIFICATIONRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:RoundNotificationResp)
  ))
_sym_db.RegisterMessage(RoundNotificationResp)

RoomSeatApplyCountResp = _reflection.GeneratedProtocolMessageType('RoomSeatApplyCountResp', (_message.Message,), dict(
  DESCRIPTOR = _ROOMSEATAPPLYCOUNTRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:RoomSeatApplyCountResp)
  ))
_sym_db.RegisterMessage(RoomSeatApplyCountResp)

AudiencePresentHeadResp = _reflection.GeneratedProtocolMessageType('AudiencePresentHeadResp', (_message.Message,), dict(
  DESCRIPTOR = _AUDIENCEPRESENTHEADRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:AudiencePresentHeadResp)
  ))
_sym_db.RegisterMessage(AudiencePresentHeadResp)

AnchorManagerChangeResp = _reflection.GeneratedProtocolMessageType('AnchorManagerChangeResp', (_message.Message,), dict(
  DESCRIPTOR = _ANCHORMANAGERCHANGERESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:AnchorManagerChangeResp)
  ))
_sym_db.RegisterMessage(AnchorManagerChangeResp)

RoomBulletScreenResp = _reflection.GeneratedProtocolMessageType('RoomBulletScreenResp', (_message.Message,), dict(
  DESCRIPTOR = _ROOMBULLETSCREENRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:RoomBulletScreenResp)
  ))
_sym_db.RegisterMessage(RoomBulletScreenResp)

GoldenMouseNotificationResp = _reflection.GeneratedProtocolMessageType('GoldenMouseNotificationResp', (_message.Message,), dict(
  DESCRIPTOR = _GOLDENMOUSENOTIFICATIONRESP,
  __module__ = 'protobuf_pb2'
  # @@protoc_insertion_point(class_scope:GoldenMouseNotificationResp)
  ))
_sym_db.RegisterMessage(GoldenMouseNotificationResp)


DESCRIPTOR.has_options = True
DESCRIPTOR._options = _descriptor._ParseOptions(descriptor_pb2.FileOptions(), _b('\n\032com.c2vl.kgamebox.protobuf'))
# @@protoc_insertion_point(module_scope)
