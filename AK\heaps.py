#调整堆顶
def h(li,top,low):
    i=top
    j=2*i+1
    tmp=li[top]
    while j<=low:
        if j+1<=low and li[j+1]>li[j]:
            j+=1
        if tmp<=li[j] and j<=low:
            li[i]=li[j]
            i=j
            j=2*i+1
        else:
            li[i]=tmp
            break
    li[i]=tmp

import random
li=[i for i in range(100)]
random.shuffle(li)

top=0
low=len(li)-1
print(li)

def he(li,k):
    new=li[:k]
    n=len(li)-1
    for i in range((k-2)//2,-1,-1):
        h(new,i,k-1)
    # print('前5个',new)
    while n>=k:
        if li[n]<new[0]:
            new[0]=li[n]
            h(new,0,k-1)
        n-=1
    # print(new)
    for i in range(k-1,-1,-1):
        new[i],new[0]=new[0],new[i]
        h(new,0,i-1)
    print(new)
he(li,12)



