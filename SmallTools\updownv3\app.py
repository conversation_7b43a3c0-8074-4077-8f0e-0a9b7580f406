from flask import Flask, request, render_template, send_from_directory, url_for, Response
import os
import socket
import sys
import tkinter as tk
from tkinter import filedialog
import qrcode
from io import BytesIO
import base64

app = Flask(__name__)

# 全局变量，用于存储已选择的上传路径
_upload_path_cache = None

def get_upload_path():
    """获取上传文件夹路径"""
    global _upload_path_cache

    # 如果已经选择过路径，直接返回
    if _upload_path_cache is not None:
        return _upload_path_cache

    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口

    try:
        print("请选择上传文件保存目录...")
        folder_path = filedialog.askdirectory(title="选择上传文件保存目录")

        if not folder_path:
            print("未选择目录，使用默认目录：当前目录下的 uploads")
            folder_path = os.path.join(os.getcwd(), 'uploads')

        # 缓存选择的路径
        _upload_path_cache = folder_path
        return folder_path
    finally:
        # 确保销毁tkinter窗口
        root.destroy()

# 设置允许的文件类型
app.config['ALLOWED_EXTENSIONS'] = {'png', 'jpg', 'jpeg', 'gif', 'mp4', 'avi', 'mov', 'mkv', 'apk', 'hap', 'txt'}

# 用于存储当前文本的变量
current_text = ""

def get_local_ip():
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(('*******', 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return '127.0.0.1'

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in app.config['ALLOWED_EXTENSIONS']

@app.route('/')
def index():
    """首页，显示上传表单和文件列表"""
    try:
        files = os.listdir(app.config['UPLOAD_FOLDER'])
        host = get_local_ip()
        port = 7000
        access_url = f'http://{host}:{port}'
        return render_template('index.html', files=files, access_url=access_url, upload_folder=app.config['UPLOAD_FOLDER'])
    except Exception as e:
        print(f"访问首页出错: {str(e)}")
        return "加载页面出错"

@app.route('/upload', methods=['POST'])
def upload_file():
    """处理文件上传"""
    if 'files[]' not in request.files:
        return '没有文件上传'

    files = request.files.getlist('files[]')

    if not files:
        return '未选中文件'

    uploaded_files = []
    for file in files:
        if file and allowed_file(file.filename):
            filename = file.filename
            file.save(os.path.join(app.config['UPLOAD_FOLDER'], filename))
            uploaded_files.append(filename)

    return f'文件 "{", ".join(uploaded_files)}" 上传成功！'

@app.route('/download/<filename>')
def download_file(filename):
    """下载文件"""
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename, as_attachment=True)

@app.route('/save-text', methods=['POST'])
def save_text():
    """保存文本到txt文件"""
    text = request.form.get('text', '')
    if not text:
        return '请输入文本内容'

    # 使用当前时间作为文件名
    from datetime import datetime
    filename = f"text_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"

    # 保存文本到文件
    file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(text)

    return f'文本已保存为 {filename}'

@app.route('/update-text', methods=['POST'])
def update_text():
    """更新文本内容"""
    global current_text
    current_text = request.form.get('text', '')
    return 'success'

@app.route('/get-text')
def get_text():
    """获取当前文本内容"""
    return current_text

@app.route('/generate-qr/<path:data>')
def generate_qr(data):
    """生成二维码图片"""
    try:
        # 创建二维码实例
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )

        # 添加数据
        qr.add_data(data)
        qr.make(fit=True)

        # 创建图片
        img = qr.make_image(fill_color="black", back_color="white")

        # 将图片保存到内存中
        img_io = BytesIO()
        img.save(img_io, 'PNG')
        img_io.seek(0)

        # 返回图片响应
        return Response(img_io.getvalue(), mimetype='image/png')
    except Exception as e:
        print(f"生成二维码出错: {str(e)}")
        return "生成二维码失败", 500

if __name__ == '__main__':
    # 获取上传路径
    UPLOAD_PATH = get_upload_path()

    # 确保上传文件夹存在
    if not os.path.exists(UPLOAD_PATH):
        try:
            os.makedirs(UPLOAD_PATH)
            print(f"成功创建文件夹: {UPLOAD_PATH}")
        except Exception as e:
            print(f"创建文件夹失败: {str(e)}")

    # 设置Flask配置
    app.config['UPLOAD_FOLDER'] = UPLOAD_PATH

    # 获取本地IP
    host = socket.gethostbyname(socket.gethostname())
    port = 7000
    print(f"\n服务已启动！")
    print(f"请在浏览器中访问: http://{host}:{port}")
    print(f"上传文件保存在: {UPLOAD_PATH}")
    print("\n关闭此窗口即可停止服务。")

    # 启动Flask应用（禁用debug模式避免重新加载导致的多次目录选择）
    app.run(host='0.0.0.0', port=7000, debug=False)