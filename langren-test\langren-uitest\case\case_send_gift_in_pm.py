"""
<AUTHOR>
@date 2020/7/3 0003
"""
import random
import time
import unittest
import warnings
from typing import Dict, Tuple, List, TypeVar

from biz.gift.domain import Gift
from role.role import DeviceManager
from role.user_role import User
from util.device import start_devices, DeviceContext
from util.log import get_logger
from util.method_tool import retry
from util.rpc import RpcClient
from util.thread_tool import ThreadTool

T = TypeVar('T')


def random_pick(l: List[T]) -> T:
    return l[random.randrange(0, len(l))]


logger = get_logger('SendGiftTestCase')

defined_gift_counts = [11, 66, 99, -1]
defined_gift_names = ['幸运盒', '大白', '泰迪熊']


def random_gift_count() -> int:
    pick = random_pick(defined_gift_counts)
    if pick != -1:
        return pick
    return random.randrange(1, 100)


def random_gift_name() -> str:
    return random_pick(defined_gift_names)


devices = ['W4XUT19B01001844', '127.0.0.1:62026']

device_uid = {'W4XUT19B01001844': 185, '127.0.0.1:62026': 329}
uid_device = {v: k for k, v in device_uid.items()}

send_gift_pair = [(185, 329), (329, 185)]

send_gift_config = [(*send_gift_pair[i % 2], random_gift_name(), random_gift_count()) for i in
                    range(0, 20)]


class SendGiftInPmTestCase(unittest.TestCase):
    thread_tool: ThreadTool = None
    device_ctx: DeviceContext = None
    rpc_clients: Dict[str, RpcClient] = None

    @classmethod
    def setUpClass(cls) -> None:
        warnings.simplefilter("ignore", ResourceWarning)
        cls.thread_tool = ThreadTool()
        cls.device_ctx = start_devices(
            devices)
        cls.rpc_clients = cls.device_ctx.rpc_clients

    def setUp(self) -> None:
        self.poll = True
        self.gifts: Dict[int, List[Tuple[bool, Gift]]] = {}

    def test_send_gift_in_pm(self):
        """私信送礼逻辑"""
        print('send_gift_config: %s' % send_gift_config)
        logger.info('send_gift_config: %s' % send_gift_config)
        # 重启app
        self.thread_tool.run_parallel(lambda dn: self.rpc_clients[dn].get_proxy(DeviceManager).restart_langren_app(),
                                      [(dn,) for dn in device_uid])

        def enter_pm(fuid: int, tuid: int):
            user = self.rpc_clients[uid_device[fuid]].get_proxy(User)
            user.wait_app_started()
            user.enter_private_message(tuid)

        self.thread_tool.run_parallel(enter_pm, [t for t in send_gift_pair])
        self.start_poll_gift()
        time.sleep(2)
        logger.info('start to send gift')
        for fuid, tuid, gn, ct in send_gift_config:
            self.rpc_clients[uid_device[fuid]].get_proxy(User).send_gift_in_pm(gn, ct)

            @retry(5)
            def try_assert():
                time.sleep(2)
                # 校验送礼方
                assert fuid in self.gifts
                f_is_send, f_gift = self.gifts[fuid][-1]
                assert f_is_send is True
                assert f_gift.name == gn
                assert f_gift.count == ct
                # 校验收礼方
                assert tuid in self.gifts
                t_is_send, t_gift = self.gifts[tuid][-1]
                assert t_is_send is False
                assert t_gift.name == gn
                assert t_gift.count == ct
                # 校验人气，特效
                assert t_gift.popularity == f_gift.popularity

                def get_effect_detail(effect: str, kw: str) -> List[str]:
                    if effect.strip() == '':
                        return []
                    return [e[e.index(kw) + len(kw):] for e in effect.split('\n')]

                assert get_effect_detail(t_gift.effect, '获得') == get_effect_detail(f_gift.effect, '获得了')

            try_assert()

    def start_poll_gift(self):

        def print_gift(uid: int, g: Tuple[bool, Gift]):
            txt = '%s [%s] %s' % (uid, 'SEND' if g[0] else 'RECEIVE', g[1])
            print(txt)
            logger.info(txt)

        def poll_gift(uid: int):
            dn = uid_device[uid]
            while self.poll:
                time.sleep(0.5)
                try:
                    lg = self.rpc_clients[dn].get_proxy(User).get_latest_gift_in_pm()
                    if lg is None:
                        continue
                    if uid not in self.gifts:
                        self.gifts[uid] = [lg]
                        print_gift(uid, lg)
                    else:
                        if self.gifts[uid][-1] != lg:
                            self.gifts[uid].append(lg)
                            print_gift(uid, lg)
                except Exception as e:
                    print(e)
                    continue

        for uid in {p[0] for p in send_gift_pair}.union({p[1] for p in send_gift_pair}):
            self.thread_tool.run_async(poll_gift, (uid,))

    def tearDown(self) -> None:
        self.poll = False

    @classmethod
    def tearDownClass(cls) -> None:
        cls.thread_tool.shutdown()
        cls.device_ctx.dispose()


if __name__ == '__main__':
    unittest.main()
