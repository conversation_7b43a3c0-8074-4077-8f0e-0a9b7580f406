"""
<AUTHOR>
@date 2020/7/1 0001
"""
import functools
from typing import Dict, List, Union, Tuple, Any

from util.log import get_logger

logger = get_logger('MethodTool')


def retry(max_try_times):
    def deco(func):
        @functools.wraps(func)
        def wrap(*args, **kwargs):
            try_times = 1
            while True:
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    try_times += 1
                    if try_times > max_try_times:
                        raise e
                    logger.error('error occurred, retry[%s] %s' % (try_times, e.__class__.__name__))

        return wrap

    return deco


def parametrized_test_with(param_map: Dict[str, List[Union[Dict, Tuple, Any]]]):
    def deco(cls: type):
        for tn in param_map:
            p_test_name = 'parametrized_test_%s' % tn
            func = getattr(cls, p_test_name)

            params = param_map[tn]
            for i in range(0, len(params)):
                def parametrized_test_closure(f, p):
                    def test_case(self):
                        if isinstance(p, dict):
                            return f(self, **p)
                        elif isinstance(p, tuple):
                            return f(self, *p)
                        else:
                            return f(self, p)

                    test_case.__doc__ = func.__doc__ + ' ' + str(p)
                    return test_case

                setattr(cls, 'test_%s_%s' % (tn, i), parametrized_test_closure(func, params[i]))
            delattr(cls, p_test_name)
        return cls

    return deco
