import hmac, time, base64, urllib.parse
from hashlib import sha1

# boomAppSecretKey = {'btest': '123456', 'hunter': 'CaMWz3WKZ3E4QAG8A44V2zbQmB7AO4mn'}
boomAppClientSecretKey = {'btest': '123456', 'hunter': 'n6Xr0C7CftwovB4LglvYoxvaB5L4iLQ9','bestwish10':'mcQGC7bALyJzPyGT4M5q1Yx4jkDjOG5t'}
boomtimestamp = str(int(time.time() * 1000))


def getsign(appid='btest'):
    headers = {}
    boominfo = [['boom-app-id', appid], ['boom-nonce', '1611754359'], ['boom-signature-method', 'HMAC-SHA1'],
                ['boom-timestamp', boomtimestamp], ['boom-version', '2.3.1.0'],
                # ['boom-client-token','7427f7ca0afc4d4e35055dcb1182a04c'],
                ['boom-client-ptype', '1'], ['boom-client-uuid', 'e9f9df2a0a1565a235da982df903317d'],
                ['boom-c-imei', '351564353532304'],
                # ['boom-client-bmc','0'],
                ['boom-client-model','aka'],
                ['boom-client-ua','moz'],
                ['boom-c-token',
                 '9ca16ae2e6eed7d661959aafb5c55c909e8ba6c55a939a9a83c560ad8e818cf252f7b0b6b4f72af0febec3b940f0feacc3b92aa884e1aad64b93b7e5a4cc70828f8aa6d85a93908bb8b74ba68683b1e84d92eaa5c300'],
                ['boom-client-source', 'xx00']]
    boominfo.sort()
    strsign = ''
    for key in boominfo:
        strsign += str(key[0] + '=' + key[1] + '&')
        headers[key[0]] = key[1]
    strsign = strsign.strip('&')
    boomsignature = hmac.new(boomAppClientSecretKey[appid].encode(), strsign.encode(),
                             sha1).digest()  # hmac.has1加密(二进制)
    boomsignature = base64.b64encode(boomsignature)  # base64加密
    boomsignature = urllib.parse.quote(boomsignature, safe="")  # urlencode
    headers['boom-signature'] = boomsignature
    return headers
