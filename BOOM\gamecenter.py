
# validation
import base64
import struct

from OpenSSL import crypto
import requests

def verify_signature(playerID, bundleID, timestamp, salt, signature, publicKeyUrl):
    try:
        # Decode Base64-encoded salt and signature
        decoded_salt = base64.b64decode(salt)
        decoded_sig = base64.b64decode(signature)

        # Debug: Print decoded values
        print("Decoded Salt:", decoded_salt)
        print("Decoded Signature:", decoded_sig)

        # Download the certificate
        response = requests.get(publicKeyUrl, stream=True)
        local_filename = publicKeyUrl.split('/')[-1]

        # Save the certificate to a local file in binary mode
        with open(local_filename, 'wb') as f:
            for chunk in response.iter_content(chunk_size=1024):
                if chunk:
                    f.write(chunk)
                    f.flush()

        # Read the certificate in binary mode
        with open(local_filename, 'rb') as f:
            der = f.read()

        # Load the certificate and extract the public key
        x509 = crypto.load_certificate(crypto.FILETYPE_ASN1, der)
        public_key = x509.get_pubkey()

        # Debug: Print public key
        print("Public Key:", public_key)

        # Prepare the payload
        payload = playerID.encode('UTF-8') + bundleID.encode('UTF-8') + struct.pack('>Q', timestamp) + decoded_salt

        # Debug: Print payload
        print("Payload:", payload)

        # Verify the signature using SHA-256
        crypto.verify(x509, decoded_sig, payload, 'sha256')
        print("Signature is valid.")
        return True
    except Exception as e:
        print("Signature verification failed:", e)
        return False

publicKeyUrl = "https://static.gc.apple.com/public-key/gc-prod-10.cer"
signature = 'NvpUkpHU6P+AKMBn4psVBTppFZR9qK9vJ52Q68A8ZvFrt7+VkWd7miibiaxKjg+frIU/95qMgUK/LGlZEB1iGkYH/fbvCDlZMNg4Nka5qdqG9VbONCBd4iL8BVukjqqCZe4FC53L+d5hJOijdKIwEr8FjfMt+KIBCyhv9x4MNhHfAgOG4SDvwM65p4GU8vMVBQCXOE5zViB5mHkV8rgDFDmlIAjV5pgOCT63jbj+g6zTZ/o7mdvJkih/cB2+XR1TBjbeuj2a6GicMpSQGF00lzH7rIMNclgpqYUYNOvb8hFTHyUvmB0fRjmZ3ozjOZUWgG2KRBHww+mGkTcDlGv8KWdde5bIz5qGLaTBrBXtL7FfHZdSRM2DvfVkmutheQft0fQgVRFnVvp8qkXLo2WueLSWFcpgoZD5sx2EuZrKiHSZ8gyKuhP67NqF4eMYJhvh7mRsTkgJGnpGFcCiD0UuYDhHz8qft/IQDwNBqGXvFjp6ib/K8bme534zpEpRBx2aj4+i1/PyNs1ralXHISqSjjJ7i8xc1SJEdB8KnXVnos5ZVKWlNePw24vd1ZUrkfGc778FBcC78TwuSb/zK9G0wN7AWhGmhnH6jNS6Dmrz6uwwyj/ipPNiDLGd6rxJ0S6b4S80o+UA8ImAtfvVPdfEAFj+gHYTP1L/+PUtw4Zkq1I='
salt = "QBqAdA=="
timestamp = 1740387818000
timestamp2 = struct.pack('>Q', int(timestamp))
print(timestamp2)

playerID = "T:_114765eb703dce7c2e9da4118acf299e"
bundleID = "com.sparkinglab.tcamera"




# publicKeyUrl = "https://static.gc.apple.com/public-key/gc-prod-10.cer"
# signature = "YbUyZmmpLztAg+cQq0VDHAtJWi1pvdUFvPPhU7y/ll+0leBdaYDNtJ100Q83PUA7z0Xg1t417K0ZIbAjz3zYueHzFjlaOthL7R03CgCOsKHsalm6Rq2HJSYjyBvgo7XHkKUYdJoR3BWP1q3f2GqWWMAb7iG3ERKoKOILJMrbKcXQ0krSKvIsyRuqn2vrxI7ujDyiGv5AYVRdX248dnG2oJGfi7kELVc7rV01URp5JXz8h34SwPuzQpaPsquibV+cDHZIgNcR0hvylp1JoH7lpy6aIGwoYoJ7/aVi7lCp+gAcDEcCluf8zhdJ76zFWDmyJY0Ec0PysCw1BsCJMMyRzFdX7Mu9JUdrcZiboZIQRqE6JV2k2UdKxpmHpoXm2MP0ZHV4cOIJfL+QvQ7sHWKtKcjrCsaZFRV6t6APSah8zfqrLOScjp80auOKTErYKqm7Zd5DpJrHKLOOtowIW1LrlbbXatNzSnbsLNUqwuu06pjNsF7w+QXCkeU7HKSouSpOZ0iAPn8o5QP0WCy4iN1E9zgTAvSnAj5rH65x0AbwC34g43jjlPLjHfgxBu82GntoZxxy5LmEdTP5EBNGJSt++4ggFaNEtQjjQA5CclU4U43S8N/BfqOim/pPkRrvrwoc/SpNkzkuH1cc8eI5X477YX9l6JVU+Ctu1ZnaGXM1VPw="
# salt = "jOqI8w=="
timestamp = 1736911173408
# timestamp2 = struct.pack('>Q', int(timestamp))
# print(timestamp2)
#
# playerID = "T:_97a3b2f25a798b9eac11ce358113237c"
# bundleID = "com.sparkinglab.tcamera"
result = verify_signature(playerID, bundleID, timestamp, salt, signature, publicKeyUrl)
print(f'result:{result}')