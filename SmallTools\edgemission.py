from selenium import webdriver
from selenium.webdriver.edge.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.edge.options import Options
import random
import time


# 随机生成两个中文字符
def generate_random_chinese():
    chinese_chars = ''.join(
        chr(random.randint(0x4E00, 0x9FFF))  # 基本汉字Unicode范围
        for _ in range(2)
    )
    return chinese_chars


# 配置Edge浏览器选项
edge_options = Options()
# edge_options.add_argument("--headless")  # 默认显示浏览器界面
edge_options.add_argument("--disable-gpu")
edge_options.add_argument("--no-sandbox")
edge_options.add_argument("--disable-dev-shm-usage")
edge_options.add_experimental_option("excludeSwitches", ["enable-automation"])  # 移除自动化标志

# 配置WebDriver（根据实际路径修改）
driver_path = "D:\pystorage\msedgedriver.exe"  # 替换为你的EdgeDriver路径
service = Service(executable_path=driver_path)


def perform_search(driver, search_count):
    """执行单次搜索操作"""
    # 生成随机搜索词
    search_query = generate_random_chinese()
    print(f"搜索 #{search_count}: {search_query}")

    # 打开必应首页
    driver.get("https://cn.bing.com/?mkt=zh-CN")

    # 等待页面加载
    time.sleep(random.uniform(1.0, 2.5))  # 随机等待时间

    # 定位搜索框并输入
    try:
        search_box = driver.find_element(By.ID, "sb_form_q")
        search_box.clear()
        # 模拟人工输入速度
        for char in search_query:
            search_box.send_keys(char)
            time.sleep(random.uniform(0.1, 0.3))

        # 提交搜索
        time.sleep(random.uniform(0.5, 1.2))
        search_box.submit()

        # 等待搜索结果加载
        time.sleep(random.uniform(2.0, 4.0))
        print(f"✅ 完成搜索 #{search_count}")
        return True

    except Exception as e:
        print(f"❌ 搜索 #{search_count} 出错: {str(e)}")
        return False


# 主程序
if __name__ == "__main__":
    try:
        # 初始化Edge浏览器
        driver = webdriver.Edge(service=service, options=edge_options)
        driver.set_window_size(1200, 800)  # 设置窗口尺寸

        # 执行15次搜索
        for i in range(1, 70):
            success = perform_search(driver, i)

            # 如果失败，等待稍长时间后重试
            if not success:
                time.sleep(5)
                print("正在重试...")
                perform_search(driver, i)

            # 随机等待间隔（2-8秒）
            if i < 15:
                wait_time = random.randint(2, 8)
                print(f"等待 {wait_time} 秒后进行下一次搜索...")
                time.sleep(wait_time)

        print("🎉 所有搜索已完成！")

    except Exception as e:
        print(f"⚠️ 主程序出错: {str(e)}")

    finally:
        # 关闭浏览器
        driver.quit()
        print("浏览器已关闭")