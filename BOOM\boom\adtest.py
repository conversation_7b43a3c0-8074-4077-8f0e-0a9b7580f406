import requests, time, urllib3

clicktime = int(time.time() * 1000)
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
baseurl = "https://qa.boom.caniculab.com/boom"

m9 = ('MI9SE', '2408:840c:dd41:290e:8593:ef49:804c:476f',
      'Mozilla/5.0 (Linux; Android 11; MI 9 SE Build/RKQ1.200826.002; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/92.0.4515.131 Mobile Safari/537.36')


# 微博回传
def weibocall(appid, moipua):
    url = baseurl + '/third/weibo/callback'
    params = {
        'IMP': 'your_imp_value',  # 新浪微博广告系统的内部参数，需要广告主在激活消息中回传，第三方必须接收此参数并在匹配激活时回传响应的值给微博，具体见激活回传消息部分
        'LANGUAGE': 'zh_CN',
        'MODEL': moipua[0],  # 设备机型，原值举例：vivo-vivo+X21
        'ad_id': 'ff',  # 计划ID
        'androidid_MD5': '',  # Android设备识别码，可能为空，android id 32位MD5后转大写，仅支持安卓8以下替换
        'appId': appid,
        'campaign_id': 'y7',  # 系列ID
        'campaignid': 'yo8paignid',
        # 广告主自定义的监测ID，与微博无关。如广告需要区分不同的监测任务，可自行添加参数，参数名自定，在此只做示范。注意该字段须使用数字或字母组成字符串格式，使用特殊字符可能存在报错情况。
        'clicktime': clicktime,  # Unix时间戳，13位（毫秒）
        'creative_id': 'yo86id',  # 创意ID
        'customer_id': 'you65ustid',  # 广告主账号
        'devicetype': 'android',  # 设备机型信息，样例 android 或 ios
        'idfa_md5': '',  # IOS设备号，iOS下，IDFA原串大写带连字符，计算MD5，再转大写，是iOS激活监测必须项
        'imei_md5': '',  # 安卓设备号，安卓下，IMEI字母转小写，计算MD5，再转大写，Android激活监测必须项
        'ip': moipua[1],
        'mac_MD5': '',  # 移动设备MAC地址，发送移动设备MAC地址，转换成大写字母，并且取MD5摘要后的结果，32位
        'oaid_md5': '',  # Android Q及更高版本的设备号，Android Q及更高版本的设备号MD5摘要，原值计算MD5，再转大写，Android激活监测必须项
        'osversion': '11',
        'ua': moipua[2]  # 数据上报终端设备user_agent，user agent经过URL编码处理，联调阶段不下发ua，在正式投放环境时会正常下发用户标准ua
    }
    headers = {
        'boom-app-id': appid  # 这是一个示例值，您需要替换为实际的boom-app-id值
    }
    response = requests.get(url, params=params, headers=headers)
    print(response.text)


# weibocall('prod199700',('thunder9876','*************','Mozilla/5.0 (Linux; Android 7.1.2; thunder9876 Build/N2G48C; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/86.0.4240.198 Safari/537.36'))

# 快手回传
def kuaishoucallback(appid, moipua):
    url = baseurl + '/third/kuaishou/callback'
    params = {
        'aid': 1,  # 广告组Id，默认值为0
        'appId': appid,  # 项目id，默认为'LszBate'
        'callback': 'www.baidu.com',  # 必填参数，编码一次的URL
        'cid': 3,  # 创意组Id，默认值为0
        'did': 4,  # 广告计划Id，默认值为0
        # 'idfaMD5': '',  # iOS下的idfa计算MD5
        # 'imeiMD5': '78d2f0a98ae4d38decb08c597ef98df4',  # 对15位数字的IMEI进行MD5
        'ip': moipua[1],  # ⽤⼾终端的公⽹IP地址
        'model': moipua[0],  # 设备型号
        # 'oaid': '',  # Android设备标识，原值
        'os': 0,  # 操作系统平台 安卓：0 IOS：1 其他：3
        'ua': moipua[2]  # 用户代理(User Agent)
    }
    headers = {'boom-app-id': appid}  # header参数: boom-app-id
    response = requests.get(url, params=params, headers=headers)
    print(response.text)


# kuaishoucallback('audit25427',m9)
# 抖音回传
def tiktokcallback(appid, moipua):
    url = baseurl + '/third/tiktok/callback'
    params = {
        'aid': 123456789012377,  # 广告计划Id，替换为实际的广告计划Id
        'appId': appid,  # 项目id
        'callback': "www.baidu,com",
        'campaignId': 123456789012345,  # 广告组Id，替换为实际的广告组Id
        'cid': 123456789012345,  # 广告创意Id，替换为实际的广告创意Id
        'convertId': 123456789012345,  # 转化id，替换为实际的转化id
        'csite': 1,  # 广告投放位置1:今日头条 10001:西瓜视频 30001:火山小视频 40001:抖音
        'idfa': "",  # iOS下的idfa原值，替换为实际的IDFA
        'idfaMD5': '',
        'imeiMD5': '',
        'ip': moipua[1],  # 用户终端的公网IP地址，替换为实际的IP地址
        'model': moipua[0],  # 手机型号，替换为实际的手机型号
        'os': 1,  # 操作系统平台，安卓：0 IOS：1 其他：3
        'ua': moipua[2]  # 用户代理(User Agent)，替换为实际的用户代理
    }
    headers = {'boom-app-id': appid}  # header参数: boom-app-id
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
    response = requests.get(url, params=params, headers=headers, verify=False)
    print(response.text)


# tiktokcallback('hunter',m9)
# tiktokcallback('prod037557',('iPhone14,5','2408:840c:dd41:290e:38b5:6661:6ad5:bc04','Mozilla/5.0 (iPhone; CPU iPhone OS 16_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148'))
# 抖音传播任务回传
def tiktoktrancallback(appid, moipua):
    url = baseurl + '/third/tiktok/transmission/callback'
    params = {
        'appId': appid,  # 项目id，默认为'LszBate'
        'callback': 'www.baidu.com',  # 必填参数，编码一次的URL
        'demandId': 0,  # 计划ID，默认值为0
        # 'ip':'*******',
        'ip': moipua[1],  # ⽤⼾终端的公⽹IP地址
        'itemId': 0,  # 视频ID，默认值为0
        'model': moipua[0],  # 手机型号
        'orderId': 0,  # 任务ID，默认值为0
        'os': 0,  # 操作系统平台 安卓：0 IOS：1 其他：3
        'ua': moipua[2]  # 用户代理(User Agent)
    }
    headers = {'boom-app-id': appid}  # header参数: boom-app-id
    response = requests.get(url, params=params, headers=headers, verify=False)
    print(response.text)


# tiktoktrancallback('prod975103', m9)


# taptap广告
def taptap(appid, moipua):
    url = baseurl + '/third/taptap/callback'
    params = {
        'adsetId': '929',  # 广告组ID
        'adsetName': 'changchengpao',  # 广告组名称
        # 'anid': 'f1099525ac8f11e0',  # Android ID
        'appId': appid,  # 应用ID
        'conversionType': 'TapTapAd2',  # 下载类型
        'creativeId': '99993',  # 创意ID
        'deepCallbackUrl': 'https://dcc.iem.taptap.cn/v1/deep/callback',  # 深度回传链接
        'device': '0',  # 设备类型（0表示Android）
        'deviceBrand': '29testp',  # 设备品牌
        'deviceModel': moipua[0],  # 设备型号
        'gameName': '大炮射蚊子',  # 游戏名称
        # 'idfaMd5': 'dsd',  # IDFA的MD5值
        # 'imei': '864671059532765',  # IMEI
        'ip': moipua[1],  # 用户IP地址
        # 'ipv6': moipua[1],  # 用户的IPV6地址
        # 'oaid': 'bae7473e-f721-43a4-a339-7403259befe8',  # OAID
        'orgId': '打赏',  # 账户ID
        'orgName': '打',  # 账户名称
        'osVersion': '1.0',  # 系统版本号
        'tapProjectId': 'witch',  # 游戏ID
        'tapTrackId': 'dsds',  # 广告归因ID
        'time': clicktime,  # 时间戳
        'webUa': moipua[2]  # 浏览器UA
    }
    headers = {
        'boom-app-id': appid
    }
    #
    response = requests.get(url, params=params, headers=headers, verify=False)
    # print(response.request.text)
    print(response.text)
# taptap('hunter',('ALN-AL10','**************','Mozilla/5.0 (Linux; Android 12; ALN-AL10 Build/HUAWEIALN-AL10P; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/99.0.4844.88 Mobile Safari/537.36'))
taptap('btest',('aka','*************','moz'))