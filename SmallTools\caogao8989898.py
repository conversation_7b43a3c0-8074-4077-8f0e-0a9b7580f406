import os
import requests
from mimetypes import guess_type
def login():
    url = 'https://qa-rancher.langren001.com:90/web/login/v2'
    data = {'userName': 'jm',
            'userPwd': '8c3f195c5b181976f2f60b06f839eab2'}
    response = requests.post(url=url, data=data)
    print(response.cookies.get('SESSION'))
    return response.cookies.get('SESSION')



def gg():
    url='https://qa-rancher.langren001.com:90/gameReplay/user/save/level'
    headers={'cookie':'SESSION=e4ae63da-bf54-4bee-8a6d-fc82521cba92'}
    params={
    "replayGameId": "F01819",
    "level": "0",
    "recommend": "F01819",
    "remark": ""
}
    response=requests.put(url=url,params=params,headers=headers)
    print(response.text)


def getswaggar():
    url = 'https://qa-rancher.langren001.com:90/v2/api-docs'
    response = requests.get(url)
    return response.json()
# login()
# gg()

def pp():
    url='https://qa-rancher.langren001.com:90/userAdmin/banReason/suggestion/config/createOrUpdate'
    headers = {'cookie': 'SESSION=e4ae63da-bf54-4bee-8a6d-fc82521cba92'}
    data={
  "reasonConfigId": 53,
  "subReason": "测试3",
  "suggestionConfigEntities": [
    {
      "banStatus": "0",
      "deductCredits": "",
      "illegalCountFrom": "111",
      "illegalCountTo": "222",
      "kickGameRoom": False,
      "punishmentValue": "",
      "punishmentValueType": 1,
      "timeBanType": 2,
      "roomBanHint": False,
      "status": 0
    }
  ]
}
    response=requests.post(url=url,json=data,headers=headers)
    print(response.text)
pp()


def i(file_path,url,headers):
    file_name = os.path.basename(file_path)
    mime_type, _ = guess_type(file_path)
    extension = os.path.splitext(file_name)[1].lower()
    if not mime_type:
        mime_type = {
            '.txt': 'text/plain',
            '.csv': 'text/csv',
            '.xls': 'application/vnd.ms-excel',
            '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            '.jpg': 'image/jpeg',
            '.png': 'image/png',
            '.zip': 'application/zip',
            # 其他格式继续追加...
        }.get(extension, 'application/octet-stream')
    extension_map = {
        '.txt': 'textFile',
        '.csv': 'csvFile',
        '.xls': 'excelFile',
        '.xlsx': 'excelXlsxFile',
        '.jpg': 'jpegFile',
        '.png': 'pngFile',
        '.zip': 'zipFile',
    }
    field_name = extension_map[extension]
    with open(file_path, 'rb') as file:
        files = {field_name: (file_name, file.read(), mime_type)}
        response = requests.post(url, files=files, headers=headers)
        print(response.text)


file_path = r"D:\download\wolf_admin_test\excel_handle\真心话题库2025-01-20-15-16-25.xls"
url = 'https://qa-rancher.langren001.com:90/werewolf/config/truth/config/upload'

file_path = r"D:\download\wolf_admin_test\excel_handle\海龟汤题.xls"
url = 'https://qa-rancher.langren001.com:90/werewolf/config/turtleSoup/config/upload'

file_path = r"D:\download\wolf_admin_test\excel_handle\狼人杀运营团队-福利商城-2025-01-16-16-53-39.xls"
url = 'https://qa-rancher.langren001.com:90/product/order/import'
headers = {'cookie': 'SESSION=dc1d7467-1b46-418e-8680-1ee904d00d14',
           'content_type' : 'multipart/form-data'}
# i(file_path,url,headers)