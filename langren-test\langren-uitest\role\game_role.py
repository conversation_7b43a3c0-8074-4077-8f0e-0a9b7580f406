"""
<AUTHOR>
@date 2020/6/17 0017
"""
from typing import List, Tuple

from page.page import PageRoot
from role.role import Role
from util.log import get_logger, log


class GameRoleName(object):
    WEREWOLF = '狼人'
    WITCH = '女巫'
    VILLAGER = '村民'


class GameRole(Role):

    @log
    def wait_game_start(self):
        self.page_root.game_room_page.game_progress_label.wait_for_appearance()

    def get_system_msgs(self) -> List[str]:
        return list(map(lambda o: o.get_text(), self.page_root.game_room_page.system_msg_labels))

    def get_latest_system_msg(self) -> str:
        """
        获取最新一条系统消息
        :return:
        """
        return self.page_root.game_room_page.latest_system_msg_label.get_text()

    def get_progress_msg(self) -> str:
        """
        获取流程提示消息
        :return:
        """
        return self.page_root.game_room_page.game_progress_label.get_text()

    @log
    def speak_end(self):
        """
        发言结束
        :return:
        """
        self.page_root.game_room_page.speak_end_btn.click()

    @log
    def vote_select(self, seat_num: int):
        """
        投票选择
        :param seat_num:
        :return:
        """
        self.page_root.game_room_page.select_vote_man_btn(seat_num).click()

    @log
    def vote_confirm(self):
        """
        确定投票
        :return:
        """
        self.page_root.game_room_page.vote_confirm_btn.click()

    @log
    def vote_cancel(self):
        """
        放弃投票
        :return:
        """
        self.page_root.game_room_page.vote_cancel_btn.click()

    @log
    def say_last_words(self):
        """
        遗言
        :return:
        """
        self.page_root.game_room_page.last_words_end_btn.click()

    @log
    def infer_is_game_end(self) -> Tuple[bool, int]:
        """
        推断游戏是否已经结束
        :return:
        """
        if self.page_root.game_room_page.game_result_win_dialog.exists():
            return True, int(self.page_root.game_room_page.first_win_seat_num_label.get_text())
        return False, -1


# 村民
class Villager(GameRole):

    def __init__(self, page_root: PageRoot):
        super().__init__(page_root)
        self.logger = get_logger('Villager')


# 女巫
class Witch(GameRole):

    def __init__(self, page_root: PageRoot):
        super().__init__(page_root)
        self.logger = get_logger('Witch')

    @log
    def poison_select(self, seat_num: int):
        """
        选择下毒对象
        :param seat_num:
        :return:
        """
        self.page_root.game_room_page.select_poison_man_btn(seat_num).click()

    @log
    def poison_confirm(self):
        """
        确定毒
        :return:
        """
        self.page_root.game_room_page.witch_poison_confirm_btn.click()

    @log
    def poison_cancel(self):
        """
        放弃毒
        :return:
        """
        self.page_root.game_room_page.witch_poison_cancel_btn.click()

    @log
    def rescue_confirm(self):
        """
        确定救人
        :return:
        """
        self.page_root.game_room_page.witch_rescue_confirm_btn.click()

    @log
    def rescue_cancel(self):
        """
        放弃救人
        :return:
        """
        self.page_root.game_room_page.witch_rescue_cancel_btn.click()


# 狼人
class Werewolf(GameRole):

    def __init__(self, page_root: PageRoot):
        super().__init__(page_root)
        self.logger = get_logger('Werewolf')

    @log
    def kill_select(self, seat_num: int):
        """
        杀人选择
        :param seat_num:
        :return:
        """
        self.page_root.game_room_page.select_kill_man_btn(seat_num).click()

    def wait_kill_dialog_dismiss(self):
        """
        等待杀人弹窗消失
        :return:
        """
        self.page_root.game_room_page.kill_dialog_voice_btn.wait_for_disappearance()
