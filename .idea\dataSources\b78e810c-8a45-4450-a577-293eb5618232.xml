<?xml version="1.0" encoding="UTF-8"?>
<dataSource name="console.db">
  <database-model serializer="dbm" dbms="SQLITE" family-id="SQLITE" format-version="4.17">
    <root id="1">
      <ServerVersion>3.31.1</ServerVersion>
    </root>
    <schema id="2" parent="1" name="main">
      <Current>1</Current>
    </schema>
    <collation id="3" parent="1" name="BINARY"/>
    <collation id="4" parent="1" name="NOCASE"/>
    <collation id="5" parent="1" name="RTRIM"/>
    <table id="6" parent="2" name="sqlite_master">
      <System>1</System>
    </table>
    <table id="7" parent="2" name="users"/>
    <column id="8" parent="6" name="type">
      <Position>1</Position>
      <DataType>text|0s</DataType>
    </column>
    <column id="9" parent="6" name="name">
      <Position>2</Position>
      <DataType>text|0s</DataType>
    </column>
    <column id="10" parent="6" name="tbl_name">
      <Position>3</Position>
      <DataType>text|0s</DataType>
    </column>
    <column id="11" parent="6" name="rootpage">
      <Position>4</Position>
      <DataType>int|0s</DataType>
    </column>
    <column id="12" parent="6" name="sql">
      <Position>5</Position>
      <DataType>text|0s</DataType>
    </column>
    <column id="13" parent="7" name="id">
      <Position>1</Position>
      <DataType>INTEGER|0s</DataType>
      <NotNull>1</NotNull>
    </column>
    <column id="14" parent="7" name="username">
      <Position>2</Position>
      <DataType>VARCHAR|0s</DataType>
    </column>
    <column id="15" parent="7" name="password">
      <Position>3</Position>
      <DataType>VARCHAR|0s</DataType>
    </column>
    <column id="16" parent="7" name="is_admin">
      <Position>4</Position>
      <DataType>BOOLEAN|0s</DataType>
    </column>
    <index id="17" parent="7" name="ix_users_username">
      <ColNames>username</ColNames>
      <Unique>1</Unique>
    </index>
    <index id="18" parent="7" name="ix_users_id">
      <ColNames>id</ColNames>
    </index>
    <key id="19" parent="7">
      <ColNames>id</ColNames>
      <Primary>1</Primary>
      <UnderlyingIndexName>ix_users_id</UnderlyingIndexName>
    </key>
  </database-model>
</dataSource>