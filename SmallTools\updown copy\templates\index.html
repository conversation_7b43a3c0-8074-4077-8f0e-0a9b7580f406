<!DOCTYPE html>
<html>
<head>
    <title>文件上传系统</title>
    <style>
        .qr-code {
            max-width: 150px;  /* 控制二维码最大宽度 */
            height: auto;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .section {
            margin-bottom: 30px;
        }
        #uploadResult {
            margin-top: 10px;
            color: green;
        }
        /* 添加新的样式 */
        .file-input {
            padding: 15px;
            font-size: 24px;
            width: 400px;
            margin-right: 15px;
            border: 2px solid #ddd;
            border-radius: 6px;
        }
        .upload-btn {
            padding: 18px 36px;
            font-size: 24px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            min-width: 120px;
        }
        .upload-btn:hover {
            background-color: #45a049;
            transform: scale(1.02);
            transition: all 0.2s;
        }
        .upload-form {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .qr-section {
            display: flex;
            align-items: flex-start;
            gap: 20px;
        }
        .text-input-area {
            flex-grow: 1;
        }
        .text-input {
            width: 100%;
            height: 100px;
            padding: 10px;
            font-size: 16px;
            border: 2px solid #ddd;
            border-radius: 6px;
            resize: vertical;
        }
        .save-text-btn {
            padding: 12px 24px;
            font-size: 16px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            margin-top: 10px;
        }
        .save-text-btn:hover {
            background-color: #45a049;
        }
        #textSaveResult {
            margin-top: 10px;
            color: green;
        }
        /* 修改文本区域样式 */
        .text-transfer-section {
            margin: 20px 0;
        }
        .text-area {
            width: 100%;
            height: 150px;
            padding: 10px;
            font-size: 16px;
            border: 2px solid #ddd;
            border-radius: 6px;
            resize: vertical;
            margin-bottom: 10px;
        }
        .button-group {
            display: flex;
            gap: 10px;
        }
        .refresh-btn, .submit-btn {
            padding: 12px 24px;
            font-size: 16px;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
        }
        .refresh-btn {
            background-color: #2196F3;
        }
        .refresh-btn:hover {
            background-color: #1976D2;
        }
        .submit-btn {
            background-color: #4CAF50;
        }
        .submit-btn:hover {
            background-color: #45a049;
        }
        /* 添加复制按钮样式 */
        .copy-btn {
            padding: 12px 24px;
            font-size: 16px;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            background-color: #9C27B0;  /* 紫色，区分其他按钮 */
        }
        .copy-btn:hover {
            background-color: #7B1FA2;
        }
        /* 修改标题栏样式 */
        .section-header {
            display: flex;
            align-items: center;
            gap: 10px;  /* 控制标题和按钮之间的间距 */
            margin-bottom: 10px;
        }
        .clear-btn {
            padding: 8px 16px;
            font-size: 14px;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            background-color: #FF5722;
        }
        .clear-btn:hover {
            background-color: #F4511E;
        }
        /* 添加URL二维码相关样式 */
        .url-qr-section {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
            align-items: flex-start;
        }
        .url-input {
            flex-grow: 1;
            padding: 10px;
            font-size: 16px;
            border: 2px solid #ddd;
            border-radius: 6px;
        }
        .qr-btn {
            padding: 12px 24px;
            font-size: 16px;
            background-color: #2196F3;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
        }
        .qr-btn:hover {
            background-color: #1976D2;
        }
        .clear-url-btn {
            padding: 12px 24px;
            font-size: 16px;
            background-color: #FF5722;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
        }
        .clear-url-btn:hover {
            background-color: #F4511E;
        }
        #qrResult {
            margin-top: 10px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        #qrResult img {
            max-width: 150px;
            height: auto;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="section">
            <h2>扫描二维码访问</h2>
            <div class="qr-section">
                <img src="{{ qr_code_data }}" alt="QR Code" class="qr-code">
                <div class="text-input-area">
                    <textarea class="text-input" id="textInput" placeholder="在此输入文本内容..."></textarea>
                    <button onclick="saveText()" class="save-text-btn">保存为文本文件</button>
                    <div id="textSaveResult"></div>
                </div>
            </div>
        </div>

        <div class="section">
            <!-- 添加URL二维码生成功能 -->
            <div class="url-qr-section">
                <input type="text" id="urlInput" class="url-input" placeholder="输入URL生成二维码...">
                <button onclick="generateQR()" class="qr-btn">生成二维码</button>
                <button onclick="clearURL()" class="clear-url-btn">清空URL</button>
                <div id="qrResult"></div>
            </div>
            <div class="section-header">
                <h2>文本传输</h2>
                <button onclick="clearText()" class="clear-btn">清空并提交</button>
            </div>
            <div class="text-transfer-section">
                <textarea class="text-area" id="textArea" placeholder="在此输入文本..."></textarea>
                <div class="button-group">
                    <button onclick="refreshText()" class="refresh-btn">刷新</button>
                    <button onclick="submitText()" class="submit-btn">提交</button>
                    <button onclick="copyText()" class="copy-btn">复制</button>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>上传文件</h2>
            <form id="uploadForm" enctype="multipart/form-data" class="upload-form">
                <input type="file" name="files[]" multiple class="file-input">
                <input type="button" value="上传" onclick="uploadFiles()" class="upload-btn">
            </form>
            <div id="uploadResult"></div>
        </div>

        <div class="section">
            <div style="display: flex; align-items: center; gap: 20px; margin-bottom: 10px;">
                <h2 style="margin: 0;">已上传文件列表</h2>
                <span style="font-size: 14px; color: #666;">本地目录：{{ upload_folder }}</span>
            </div>
            <div>
                {% for file in files %}
                <div>
                    <a href="{{ url_for('download_file', filename=file) }}">{{ file }}</a>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <script>
        function uploadFiles() {
            var form = document.getElementById('uploadForm');
            var formData = new FormData(form);

            fetch('/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(result => {
                document.getElementById('uploadResult').innerHTML = result;
                // 刷新页面以更新文件列表
                setTimeout(() => location.reload(), 1000);
            })
            .catch(error => {
                console.error('Error:', error);
                document.getElementById('uploadResult').innerHTML = '上传失败！';
            });
        }

        function saveText() {
            var text = document.getElementById('textInput').value;
            if (!text) {
                document.getElementById('textSaveResult').innerHTML = '请输入文本内容';
                return;
            }

            var formData = new FormData();
            formData.append('text', text);

            fetch('/save-text', {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(result => {
                document.getElementById('textSaveResult').innerHTML = result;
                document.getElementById('textInput').value = '';
                // 刷新页面以更新文件列表
                setTimeout(() => location.reload(), 1000);
            })
            .catch(error => {
                console.error('Error:', error);
                document.getElementById('textSaveResult').innerHTML = '保存失败！';
            });
        }

        function submitText() {
            const text = document.getElementById('textArea').value;
            if (!text) {
                return;
            }

            var formData = new FormData();
            formData.append('text', text);

            fetch('/update-text', {
                method: 'POST',
                body: formData
            });
        }

        function refreshText() {
            fetch('/get-text')
            .then(response => response.text())
            .then(text => {
                document.getElementById('textArea').value = text;
            });
        }

        function copyText() {
            const textArea = document.getElementById('textArea');
            textArea.select();
            document.execCommand('copy');
            // 取消选中
            window.getSelection().removeAllRanges();
        }

        function clearText() {
            document.getElementById('textArea').value = '';
            // 同时清空服务器存储的文本
            fetch('/update-text', {
                method: 'POST',
                body: new FormData()
            });
        }

        function generateQR() {
            const urlInput = document.getElementById('urlInput');
            const qrResult = document.getElementById('qrResult');
            const url = urlInput.value.trim();

            if (!url) {
                alert('请输入URL！');
                return;
            }

            // 使用本地API生成二维码
            fetch('/generate-qr', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ url: url })
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    alert('生成二维码失败: ' + data.error);
                    return;
                }

                const qrImage = document.createElement('img');
                qrImage.src = data.qr_code;
                qrImage.alt = 'QR Code';

                // 清除之前的二维码（如果有）
                qrResult.innerHTML = '';
                qrResult.appendChild(qrImage);
            })
            .catch(error => {
                console.error('Error:', error);
                alert('生成二维码失败！');
            });
        }

        function clearURL() {
            document.getElementById('urlInput').value = '';
            document.getElementById('qrResult').innerHTML = '';
        }
    </script>
</body>
</html>