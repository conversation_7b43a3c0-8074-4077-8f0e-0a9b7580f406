<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>文件上传系统</title>
    <style>
        /* 全局样式 */
        * {
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 12px;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0;
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-gap: 16px;
            min-height: calc(100vh - 24px);
        }

        .section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
        }

        .section-full {
            grid-column: 1 / -1;
        }

        .section:hover {
            transform: translateY(-2px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
        }

        h2 {
            color: #2c3e50;
            font-size: 18px;
            font-weight: 600;
            margin: 0 0 12px 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        h2::before {
            content: '';
            width: 3px;
            height: 18px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        /* 二维码样式 */
        .qr-code {
            max-width: 120px;
            height: auto;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            border: 2px solid white;
        }

        .qr-section {
            display: flex;
            align-items: flex-start;
            gap: 16px;
            flex-wrap: wrap;
        }

        .text-input-area {
            flex: 1;
            min-width: 200px;
        }

        /* 输入框样式 */
        .text-input, .text-area, .url-input, .file-input {
            width: 100%;
            padding: 12px 16px;
            font-size: 14px;
            border: 2px solid #e1e8ed;
            border-radius: 12px;
            background: white;
            transition: all 0.3s ease;
            font-family: inherit;
            outline: none;
        }

        .text-input:focus, .text-area:focus, .url-input:focus, .file-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            transform: translateY(-1px);
        }

        .text-input {
            height: 80px;
            resize: vertical;
        }

        .text-area {
            height: 100px;
            resize: vertical;
            margin-bottom: 12px;
        }

        .file-input {
            font-size: 14px;
            padding: 16px;
            margin-right: 12px;
            cursor: pointer;
            border: 2px dashed #667eea;
            background: rgba(102, 126, 234, 0.05);
        }

        .file-input:hover {
            border-color: #764ba2;
            background: rgba(118, 75, 162, 0.05);
        }

        /* 按钮基础样式 */
        .btn {
            padding: 10px 20px;
            font-size: 14px;
            font-weight: 600;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            outline: none;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .btn:active {
            transform: translateY(0);
        }

        /* 按钮颜色变体 */
        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
        }

        .btn-info {
            background: linear-gradient(135deg, #2196F3, #1976D2);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #FF9800, #F57C00);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #FF5722, #F4511E);
            color: white;
        }

        .btn-purple {
            background: linear-gradient(135deg, #9C27B0, #7B1FA2);
            color: white;
        }

        /* 大按钮 */
        .btn-large {
            padding: 14px 28px;
            font-size: 16px;
            border-radius: 14px;
        }

        /* 小按钮 */
        .btn-small {
            padding: 8px 16px;
            font-size: 12px;
            border-radius: 10px;
        }

        /* 布局样式 */
        .upload-form {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            flex-wrap: wrap;
            gap: 12px;
        }

        .button-group {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .section-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 12px;
            flex-wrap: wrap;
            gap: 12px;
        }

        .url-qr-section {
            margin-bottom: 16px;
            display: flex;
            gap: 8px;
            align-items: flex-start;
            flex-wrap: wrap;
        }

        .url-qr-section .url-input {
            flex: 1;
            min-width: 180px;
        }

        /* 结果显示样式 */
        #uploadResult, #textSaveResult {
            margin-top: 8px;
            padding: 8px 12px;
            border-radius: 8px;
            background: rgba(76, 175, 80, 0.1);
            color: #2e7d32;
            border: 1px solid rgba(76, 175, 80, 0.2);
            font-weight: 500;
            font-size: 12px;
        }

        #qrResult {
            margin-top: 12px;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 12px;
            background: rgba(102, 126, 234, 0.05);
            border-radius: 12px;
            border: 1px solid rgba(102, 126, 234, 0.1);
        }

        #qrResult img {
            max-width: 120px;
            height: auto;
            margin-top: 8px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        /* 文件列表样式 */
        .file-list {
            background: rgba(248, 249, 250, 0.8);
            border-radius: 12px;
            padding: 12px;
            border: 1px solid rgba(0, 0, 0, 0.05);
            flex: 1;
            overflow-y: auto;
            max-height: 200px;
        }

        .file-list a {
            display: block;
            padding: 8px 12px;
            margin: 2px 0;
            color: #2c3e50;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.2s ease;
            font-weight: 500;
            font-size: 13px;
        }

        .file-list a:hover {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            transform: translateX(2px);
        }

        .file-info {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 12px;
            flex-wrap: wrap;
        }

        .file-info h2 {
            margin: 0;
        }

        .file-info span {
            font-size: 11px;
            color: #6c757d;
            background: rgba(108, 117, 125, 0.1);
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            body {
                padding: 4px;
                min-height: 100vh;
            }

            .container {
                grid-template-columns: 1fr;
                grid-gap: 8px;
                min-height: calc(100vh - 8px);
            }

            .section {
                padding: 12px;
                border-radius: 10px;
                margin: 0;
            }

            .section-full {
                grid-column: 1;
            }

            h2 {
                font-size: 16px;
                margin: 0 0 8px 0;
            }

            h2::before {
                width: 2px;
                height: 16px;
            }

            .qr-section {
                flex-direction: column;
                align-items: center;
                gap: 8px;
                text-align: center;
            }

            .qr-code {
                max-width: 100px;
                flex-shrink: 0;
                margin-bottom: 8px;
            }

            .text-input-area {
                min-width: auto;
                width: 100%;
            }

            .text-input {
                height: 60px;
                font-size: 13px;
                padding: 8px 12px;
            }

            .text-area {
                height: 70px;
                font-size: 13px;
                padding: 8px 12px;
                margin-bottom: 8px;
            }

            .url-input, .file-input {
                font-size: 13px;
                padding: 8px 12px;
            }

            .upload-form {
                flex-direction: column;
                align-items: stretch;
                gap: 8px;
            }

            .file-input {
                margin-right: 0;
                margin-bottom: 0;
                padding: 12px;
            }

            .btn {
                padding: 8px 16px;
                font-size: 13px;
                border-radius: 8px;
            }

            .btn-large {
                padding: 10px 20px;
                font-size: 14px;
            }

            .btn-small {
                padding: 6px 12px;
                font-size: 11px;
            }

            .button-group {
                justify-content: center;
                gap: 6px;
            }

            .url-qr-section {
                flex-direction: column;
                gap: 6px;
                margin-bottom: 8px;
            }

            .url-qr-section .url-input {
                min-width: auto;
                margin-bottom: 6px;
            }

            .url-qr-section .btn {
                margin: 2px;
            }

            .section-header {
                margin-bottom: 8px;
                gap: 8px;
            }

            .file-list {
                max-height: 120px;
                padding: 8px;
            }

            .file-list a {
                padding: 6px 8px;
                font-size: 12px;
                margin: 1px 0;
            }

            .file-info {
                gap: 8px;
                margin-bottom: 8px;
            }

            .file-info span {
                font-size: 10px;
                padding: 3px 6px;
            }

            #uploadResult, #textSaveResult {
                margin-top: 6px;
                padding: 6px 8px;
                font-size: 11px;
            }

            #qrResult {
                margin-top: 8px;
                padding: 8px;
            }

            #qrResult img {
                max-width: 80px;
                margin-top: 4px;
            }

            /* 移动端特殊优化 */
            .container {
                display: flex;
                flex-direction: column;
                gap: 8px;
            }

            .section {
                flex: none;
            }

            /* 确保内容不会太高 */
            .section:nth-child(1), .section:nth-child(2) {
                flex: 0 0 auto;
            }

            .section:nth-child(3), .section:nth-child(4) {
                flex: 0 0 auto;
            }

            .section-full {
                flex: 1;
                min-height: 0;
            }
        }

        /* 超小屏幕优化 */
        @media (max-width: 480px) {
            body {
                padding: 2px;
            }

            .container {
                grid-gap: 6px;
            }

            .section {
                padding: 8px;
                border-radius: 8px;
            }

            h2 {
                font-size: 14px;
                margin: 0 0 6px 0;
            }

            .qr-code {
                max-width: 60px;
            }

            .text-input, .text-area {
                height: 50px;
                font-size: 12px;
                padding: 6px 8px;
            }

            .btn {
                padding: 6px 12px;
                font-size: 12px;
                gap: 4px;
            }

            .btn-large {
                padding: 8px 16px;
                font-size: 13px;
            }

            .file-list {
                max-height: 100px;
                padding: 6px;
            }

            .file-list a {
                padding: 4px 6px;
                font-size: 11px;
            }
        }

        /* 动画效果 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .section {
            animation: fadeInUp 0.6s ease-out;
        }

        .section:nth-child(2) { animation-delay: 0.1s; }
        .section:nth-child(3) { animation-delay: 0.2s; }
        .section:nth-child(4) { animation-delay: 0.3s; }
        .section:nth-child(5) { animation-delay: 0.4s; }
    </style>
</head>
<body>
    <div class="container">
        <!-- 左上：二维码访问 -->
        <div class="section">
            <h2>📱 扫描二维码访问</h2>
            <div class="qr-section">
                <img src="{{ qr_code_data }}" alt="QR Code" class="qr-code">
                <div class="text-input-area">
                    <textarea class="text-input" id="textInput" placeholder="输入文本内容..."></textarea>
                    <button onclick="saveText()" class="btn btn-success">💾 保存文件</button>
                    <div id="textSaveResult"></div>
                </div>
            </div>
        </div>

        <!-- 右上：URL二维码生成 -->
        <div class="section">
            <h2>🔗 URL二维码生成</h2>
            <div class="url-qr-section">
                <input type="text" id="urlInput" class="url-input" placeholder="输入URL生成二维码...">
                <div class="button-group">
                    <button onclick="generateQR()" class="btn btn-info">生成</button>
                    <button onclick="clearURL()" class="btn btn-danger btn-small">清空</button>
                </div>
            </div>
            <div id="qrResult"></div>
        </div>

        <!-- 左下：文本传输 -->
        <div class="section">
            <div class="section-header">
                <h2>📝 文本传输</h2>
                <button onclick="clearText()" class="btn btn-danger btn-small">🗑️ 清空</button>
            </div>
            <textarea class="text-area" id="textArea" placeholder="在此输入文本..."></textarea>
            <div class="button-group">
                <button onclick="refreshText()" class="btn btn-info">🔄 刷新</button>
                <button onclick="submitText()" class="btn btn-success">📤 提交</button>
                <button onclick="copyText()" class="btn btn-purple">📋 复制</button>
            </div>
        </div>

        <!-- 右下：文件上传 -->
        <div class="section">
            <h2>📁 文件上传</h2>
            <form id="uploadForm" enctype="multipart/form-data" class="upload-form">
                <input type="file" name="files[]" multiple class="file-input">
                <button type="button" onclick="uploadFiles()" class="btn btn-success btn-large">上传</button>
            </form>
            <div id="uploadResult"></div>
        </div>

        <!-- 底部：文件列表 -->
        <div class="section section-full">
            <div class="file-info">
                <h2>📂 已上传文件列表</h2>
                <span>{{ upload_folder }}</span>
            </div>
            <div class="file-list">
                {% for file in files %}
                    <a href="{{ url_for('download_file', filename=file) }}">📄 {{ file }}</a>
                {% endfor %}
                {% if not files %}
                    <div style="text-align: center; color: #6c757d; padding: 12px; font-size: 12px;">
                        暂无文件
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <script>
        function uploadFiles() {
            var form = document.getElementById('uploadForm');
            var formData = new FormData(form);

            fetch('/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(result => {
                document.getElementById('uploadResult').innerHTML = result;
                // 刷新页面以更新文件列表
                setTimeout(() => location.reload(), 1000);
            })
            .catch(error => {
                console.error('Error:', error);
                document.getElementById('uploadResult').innerHTML = '上传失败！';
            });
        }

        function saveText() {
            var text = document.getElementById('textInput').value;
            if (!text) {
                document.getElementById('textSaveResult').innerHTML = '请输入文本内容';
                return;
            }

            var formData = new FormData();
            formData.append('text', text);

            fetch('/save-text', {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(result => {
                document.getElementById('textSaveResult').innerHTML = result;
                document.getElementById('textInput').value = '';
                // 刷新页面以更新文件列表
                setTimeout(() => location.reload(), 1000);
            })
            .catch(error => {
                console.error('Error:', error);
                document.getElementById('textSaveResult').innerHTML = '保存失败！';
            });
        }

        function submitText() {
            const text = document.getElementById('textArea').value;
            if (!text) {
                return;
            }

            var formData = new FormData();
            formData.append('text', text);

            fetch('/update-text', {
                method: 'POST',
                body: formData
            });
        }

        function refreshText() {
            fetch('/get-text')
            .then(response => response.text())
            .then(text => {
                document.getElementById('textArea').value = text;
            });
        }

        function copyText() {
            const textArea = document.getElementById('textArea');
            textArea.select();
            document.execCommand('copy');
            // 取消选中
            window.getSelection().removeAllRanges();
        }

        function clearText() {
            document.getElementById('textArea').value = '';
            // 同时清空服务器存储的文本
            fetch('/update-text', {
                method: 'POST',
                body: new FormData()
            });
        }

        function generateQR() {
            const urlInput = document.getElementById('urlInput');
            const qrResult = document.getElementById('qrResult');
            const url = urlInput.value.trim();

            if (!url) {
                alert('请输入URL！');
                return;
            }

            // 使用本地API生成二维码
            fetch('/generate-qr', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ url: url })
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    alert('生成二维码失败: ' + data.error);
                    return;
                }

                const qrImage = document.createElement('img');
                qrImage.src = data.qr_code;
                qrImage.alt = 'QR Code';

                // 清除之前的二维码（如果有）
                qrResult.innerHTML = '';
                qrResult.appendChild(qrImage);
            })
            .catch(error => {
                console.error('Error:', error);
                alert('生成二维码失败！');
            });
        }

        function clearURL() {
            document.getElementById('urlInput').value = '';
            document.getElementById('qrResult').innerHTML = '';
        }
    </script>
</body>
</html>