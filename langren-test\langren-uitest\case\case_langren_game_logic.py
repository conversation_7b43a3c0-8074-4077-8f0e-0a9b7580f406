"""
<AUTHOR>
@date 2020/7/1 0001
"""
import time
import unittest
import warnings
from typing import Callable, Dict

import paramunittest

from biz.game.case_reader import all_cases
from biz.game.domain import GameContext, ActionProvider, ExcelCaseActionProvider, SeatProfile
from biz.game.engine import Engine
from biz.game.game import Night, Day
from role.game_role import <PERSON>RoleName, Villager, Witch, Werewolf
from role.role import <PERSON><PERSON><PERSON><PERSON><PERSON>, Role<PERSON>ana<PERSON>
from role.room_role import Owner, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>
from role.user_role import User
from util.device import start_devices, DeviceContext
from util.log import get_logger
from util.rpc import RpcClient
from util.thread_tool import ThreadTool

logger = get_logger('LangrenGameLogic')

devices = ['FMGNW19412002254', 'W4XUT19B01001844', '127.0.0.1:62001', '127.0.0.1:62025', '127.0.0.1:62028',
           '127.0.0.1:62027']
cases = all_cases()

param = []
for cn in cases:
    def closure_func(c: str) -> Callable[[GameContext], ActionProvider]:
        def mk_excel_ap(ctx: GameContext) -> ActionProvider:
            case = cases[c]
            # 初始化用例
            real_seat_nums = []
            real_seat_nums.extend(ctx.werewolf_seat_nums)
            real_seat_nums.append(ctx.witch_seat_num)
            real_seat_nums.extend(ctx.villager_seat_nums)
            for turn in case:
                case[turn].change_to_real_seat_num(real_seat_nums)

            seat_mapping = ['case seat mapping:']
            for i in range(0, len(real_seat_nums)):
                seat_mapping.append('%s -> %s' % (i + 1, real_seat_nums[i]))
            logger.info('\n'.join(seat_mapping))
            return ExcelCaseActionProvider(ctx, case)

        return mk_excel_ap


    param.append({'case_name': cn, 'ap_supplier': closure_func(cn)})


@paramunittest.parametrized(*param)
class LangrenGameLogicTestCase(unittest.TestCase):
    thread_tool: ThreadTool = None
    device_ctx: DeviceContext = None
    rpc_clients: Dict[str, RpcClient] = None

    @classmethod
    def setUpClass(cls) -> None:
        warnings.simplefilter("ignore", ResourceWarning)
        cls.thread_tool = ThreadTool()
        cls.device_ctx = start_devices(
            devices)
        cls.rpc_clients = cls.device_ctx.rpc_clients

    def setParameters(self, case_name: str, ap_supplier: Callable[[GameContext], ActionProvider]):
        self.case_name = case_name
        self.ap_supplier = ap_supplier

    def test_game_logic(self):
        """游戏逻辑"""
        rpc_clients = self.rpc_clients

        # 重启app
        self.thread_tool.run_parallel(lambda dn: rpc_clients[dn].get_proxy(DeviceManager).restart_langren_app(),
                                      [(dn,) for dn in devices])

        # 第一台设备是房主
        own_dn = devices[0]
        # 分配角色
        self.thread_tool.run_parallel(lambda dn: rpc_clients[dn].get_proxy(RoleManager).assign_role(
            Owner if dn == own_dn else SeatUser), [(dn,) for dn in devices])

        # 获取昵称
        nicknames = self.thread_tool.run_parallel(lambda dn: rpc_clients[dn].get_proxy(User).infer_user_nickname(),
                                                  [(dn,) for dn in devices])

        # 创房
        owner = rpc_clients[own_dn].get_proxy(Owner)
        room_num = owner.create_room()
        # 进房
        self.thread_tool.run_parallel(lambda dn: rpc_clients[dn].get_proxy(SeatUser).join_room(room_num),
                                      [(dn,) for dn in devices[1:]])
        # 获取座位号
        time.sleep(1)
        seat_dict = rpc_clients[own_dn].get_proxy(RoomRole).infer_seat_num()
        if len(seat_dict) != len(rpc_clients):
            time.sleep(4)
            seat_dict = rpc_clients[own_dn].get_proxy(RoomRole).infer_seat_num()
        seat_profiles: Dict[int, SeatProfile] = {}

        idx = 0
        for dn in rpc_clients:
            nn = nicknames[idx]
            sn = seat_dict[nn]
            seat_profiles[sn] = SeatProfile(sn, rpc_clients[dn], nn, '')
            idx += 1

        # 开始游戏
        owner.start_game()

        # 获取游戏角色
        def infer_game_role(sn: int):
            sp = seat_profiles[sn]

            role = sp.rpc_client.get_proxy(RoomRole).infer_game_role()
            logger.info('%s -> %s' % (sn, role))
            sp.role_name = role

            # 分配角色
            manager = sp.rpc_client.get_proxy(RoleManager)
            if role == GameRoleName.VILLAGER:
                manager.assign_role(Villager)
            elif role == GameRoleName.WITCH:
                manager.assign_role(Witch)
            elif role == GameRoleName.WEREWOLF:
                manager.assign_role(Werewolf)

        self.thread_tool.run_parallel(infer_game_role, [(sn,) for sn in seat_profiles])

        # 初始化游戏上下文
        ctx = GameContext(self.thread_tool)
        ctx.current_turn = 1
        ctx.seat_profiles = seat_profiles
        ctx.seat_nums = sorted(seat_profiles.keys())
        for sn in ctx.seat_nums:
            if seat_profiles[sn].role_name == GameRoleName.WEREWOLF:
                ctx.werewolf_seat_nums.append(sn)
            elif seat_profiles[sn].role_name == GameRoleName.WITCH:
                ctx.witch_seat_num = sn
            elif seat_profiles[sn].role_name == GameRoleName.VILLAGER:
                ctx.villager_seat_nums.append(sn)
        ctx.action_provider = self.ap_supplier(ctx)

        # 开始游戏流程
        engine = Engine(Night.NightState.NIGHT_INITIAL, ctx)
        engine.bind(Night())
        engine.bind(Day())

        engine.start()
        engine.wait_end()

    @classmethod
    def tearDownClass(cls) -> None:
        for dn in cls.device_ctx.rpc_clients:
            cls.device_ctx.rpc_clients[dn].get_proxy(DeviceManager).stop_langren_app()

        cls.thread_tool.shutdown()
        cls.device_ctx.dispose()


if __name__ == '__main__':
    unittest.main()
