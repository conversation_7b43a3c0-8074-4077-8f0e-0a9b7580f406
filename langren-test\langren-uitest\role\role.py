"""
<AUTHOR>
@date 2020/6/16 0016
"""
import base64
import time
from io import BytesIO
from typing import Type, TypeVar, Optional

from PIL import Image
from airtest.core import api
from airtest.core.device import Device

from page.android_page import AndroidPageRoot
from page.page import PageRoot
from util import ocr
from util.rpc import RpcServer
from util.thread_tool import ThreadTool


class Role(object):
    def __init__(self, page_root: PageRoot):
        self.page_root: PageRoot = page_root
        self.gift_panel_index = 0

    def __str__(self):
        return '%s' % self.__class__.__name__

    def wait_app_started(self):
        self.page_root.home_tab_page.nickname_label.wait_for_appearance()

    def click(self, x: int, y: int):
        api.click((x, y))

    def send_gift(self, gift_name: str, num: int = None):
        gift_panel = self.page_root.gift_panel
        # 从第一页开始找
        while self.gift_panel_index > 0:
            gift_panel.swipe_prev()
            self.gift_panel_index -= 1

        first_gift_name: Optional[str] = None
        while True:
            gift_names = gift_panel.gift_names()

            gift_num = len(gift_names)
            if gift_num == 0:
                raise AssertionError('gift %s not exists' % gift_name)

            for gi in range(0, gift_num):
                gs, gn = gift_names[gi]
                text = gn.get_text()
                if text == gift_name:
                    if not gs:
                        gn.click()

                    if num is not None:
                        gift_panel.open_select_num_btn.click()
                        num_btn = gift_panel.gift_num_btn(str(num))
                        if num_btn.exists():
                            num_btn.click()
                        else:
                            gift_panel.gift_num_btn('其他数量').click()
                            gift_panel.gift_num_input.set_text(str(num))
                            gift_panel.gift_num_input_confirm_btn.click()

                    gift_panel.confirm_send_btn.click()
                    return

                if gi == 0:
                    if text == first_gift_name:
                        raise AssertionError('gift %s not exists' % gift_name)
                    else:
                        first_gift_name = text
            gift_panel.swipe_next()
            time.sleep(0.2)
            self.gift_panel_index += 1

    def wait_toast(self, txt: str, max_sample_times: int = 3, error_on_failed: bool = True) -> bool:
        """
        等待toast出现 每隔0.3s采样一张截图
        :param txt:
        :param max_sample_times:
        :param error_on_failed:
        :return:
        """
        found = [False]

        def do_recognize(s: str):
            img = Image.open(BytesIO(base64.b64decode(s)))
            # toast 在下半屏幕，截取后处理
            # todo ios
            w, h = img.size
            img = img.crop((0, h * 0.8, w, h))
            result = ocr.img_to_str(img)
            if txt in result:
                found[0] = True

        with ThreadTool() as tool:
            sample_times = 1
            while not found[0] and sample_times <= max_sample_times:
                b64, fmt = self.page_root.poco.snapshot()
                tool.run_async(do_recognize, (b64,))
                sample_times += 1
                time.sleep(0.3)
        if not found[0] and error_on_failed:
            raise AssertionError
        return found[0]


T = TypeVar('T', bound=Role)


class RoleManager(object):
    def __init__(self, server: RpcServer):
        self.__server = server
        # TODO ios
        self.page_root: PageRoot = AndroidPageRoot()

    def assign_role(self, role_type: Type[T]):
        self.__server.handlers[role_type] = role_type(self.page_root)

    def dispose(self):
        self.page_root.dispose()


class DeviceManager(object):

    def __init__(self, device: Device):
        # TODO ios
        self.__device = device

    def start_langren_app(self):
        self.__device.start_app('com.c2vl.kgamebox', activity='activity.LoadingActivity')

    def stop_langren_app(self):
        self.__device.stop_app('com.c2vl.kgamebox')

    def restart_langren_app(self):
        self.stop_langren_app()
        self.start_langren_app()
