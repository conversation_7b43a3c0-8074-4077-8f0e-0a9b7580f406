# 查询google退款订单
import google.auth
from googleapiclient.discovery import build
from google.oauth2 import service_account
import datetime

# 设置服务帐号的密钥文件路径
SERVICE_ACCOUNT_FILE = 'google_auth.json'  # 替换为你的路径

# 设置使用的范围
SCOPES = ['https://www.googleapis.com/auth/androidpublisher']

# 创建凭证
credentials = service_account.Credentials.from_service_account_file(
    SERVICE_ACCOUNT_FILE, scopes=SCOPES)

# 构建 API 服务对象
service = build('androidpublisher', 'v3', credentials=credentials)

# 参数
package_name = 'com.jiamiantech.voyage.demo'  # 替换为你的应用包名
max_results = 100  # 设置需要获取的最大结果条数


# 将毫秒转换为日期时间格式
def convert_voided_time_to_datetime(voided_time_millis):
    try:
        # 确保 voided_time_millis 是整数类型
        if isinstance(voided_time_millis, str):
            voided_time_millis = int(voided_time_millis)
        seconds = voided_time_millis / 1000.0
        date_time = datetime.datetime.fromtimestamp(seconds)
        formatted_datetime = date_time.strftime('%Y-%m-%d %H:%M:%S')
        return formatted_datetime
    except ValueError:
        return "无效的时间格式"


# 查询退款订单
def get_refund_orders(package_name, max_results):
    try:
        # 调用退款订单查询接口
        request = service.purchases().voidedpurchases().list(packageName=package_name, maxResults=max_results)
        response = request.execute()
        # print(response)
        # 处理退款订单信息
        if 'voidedPurchases' in response:
            for order in response['voidedPurchases']:
                voided_time_datetime = convert_voided_time_to_datetime(order['voidedTimeMillis'])
                print(f"Order ID: {order['orderId']}, Voided Time: {voided_time_datetime}")
        else:
            print("没有找到退款订单。")

    except Exception as e:
        print(f"请求失败: {e}")


get_refund_orders(package_name, max_results)
