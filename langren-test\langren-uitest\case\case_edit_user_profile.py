"""
<AUTHOR>
@date 2020/7/9 0009
"""
import random
import unittest
import warnings
from typing import TypeVar, List

from role.role import Devi<PERSON><PERSON>anager
from role.user_role import User
from util.device import start_devices, DeviceContext

T = TypeVar('T')


def random_pick(l: List[T]) -> T:
    return l[random.randrange(0, len(l))]


head_imgs = ['1.jpg', '2.jpg']
background_imgs = head_imgs

device_name = 'W4XUT19B01001844'


class EditUserProfileTestCase(unittest.TestCase):
    device_ctx: DeviceContext = None
    user: User = None

    @classmethod
    def setUpClass(cls) -> None:
        warnings.simplefilter("ignore", ResourceWarning)
        cls.device_ctx = start_devices([device_name])
        rpc_client = cls.device_ctx.rpc_clients[device_name]

        rpc_client.get_proxy(DeviceManager).restart_langren_app()
        cls.user = rpc_client.get_proxy(User)
        cls.user.wait_app_started()

    @classmethod
    def tearDownClass(cls) -> None:
        cls.device_ctx.dispose()

    @unittest.skip
    def test_edit_nickname(self):
        """编辑昵称"""
        new_nickname = 'hello' + str(random.randrange(0, 10))

        original_nickname, current_nickname = self.user.edit_nickname(new_nickname)

        print("nickname: %s -> %s" % (original_nickname, new_nickname))
        self.assertEqual(current_nickname, new_nickname)

    @unittest.skip
    def test_edit_signature(self):
        """编辑个性签名"""
        new_signature = 'world' + str(random.randrange(0, 10))

        original_signature, current_signature = self.user.edit_signature(new_signature)

        print("signature: %s -> %s" % (original_signature, new_signature))
        self.assertEqual(current_signature, new_signature)

    def test_edit_head_img_with_photo(self):
        """编辑头像（相册选择）"""
        new_head_img_file_name = random_pick(head_imgs)

        self.user.edit_head_img_with_photo(new_head_img_file_name)
        print("head_img: %s" % new_head_img_file_name)

    def test_edit_background_img_with_photo(self):
        """编辑背景图（相册选择）"""
        new_bg_img_file_name = random_pick(background_imgs)
        vip_days = self.user.infer_vip_days()
        success = self.user.edit_background_img_with_photo(new_bg_img_file_name)

        self.assertIs(success, vip_days > 0)
        if success:
            print("background_img: %s vip_days: %s" % (new_bg_img_file_name, vip_days))
        else:
            print("not vip. vip_days: %s" % vip_days)

    @unittest.skip
    def test_change_gender(self):
        """编辑性别"""
        original_gender, current_gender, free_edit = self.user.change_gender()

        def get_gender_text(g):
            return '女' if g == 0 else '男'

        print("gender: %s->%s [%s]" % (
            get_gender_text(original_gender), get_gender_text(current_gender), '免费修改' if free_edit else '使用性别修改卡修改'))


if __name__ == '__main__':
    unittest.main()
