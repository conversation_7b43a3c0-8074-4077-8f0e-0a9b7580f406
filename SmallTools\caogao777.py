from appium import webdriver
from appium.options.android import UiAutomator2Options

# 使用AppiumOptions设置启动参数
options = UiAutomator2Options()
options.platform_name = 'Android'
options.platform_version = '7.1.2'
options.device_name = 'emulator-5554'
options.app_package = 'com.c2vl.kgamebox'
options.app_activity = 'com.c2vl.kgamebox.activity.LoadingActivity'
options.automation_name = 'UiAutomator2'
options.no_reset = True

# 初始化 WebDriver
driver = webdriver.Remote('http://127.0.0.1:4723/wd/hub', options=options)

# 等待一段时间，让应用启动
driver.implicitly_wait(10)

# 你的测试代码将在这里执行，例如：
# driver.find_element_by_id('element_id').click()

# 结束会话
driver.quit()

# {
#   "platformName": "Android",
#   "appium:platformVersion": "7.1.2",
#   "appium:deviceName": "emulator-5554",
#   "appium:appPackage": "com.c2vl.kgamebox",
#   "appium:appActivity": "com.c2vl.kgamebox.activity.LoadingActivity"
# }
#adb shell CLASSPATH=/sdcard/monkey.jar:/sdcard/framework.jar exec app_process /system/bin tv.panda.test.monkey.Monkey -p com.c2vl.kgamebox --uiautomatormix --running-minutes 60 -v -v
#adb shell CLASSPATH=/sdcard/monkey.jar:/sdcard/framework.jar exec app_process /system/bin tv.panda.test.monkey.Monkey -p com.c2vl.kgamebox --uiautomatormix --running-minutes 60 --throttle 300 --act-blacklist-file /sdcard/awl.string -v -v



# java -jar appcrawler-2.7.4-hogwarts.jar --capability "appPackage=com.c2vl.kgamebox,appActivity=com.c2vl.kgamebox.activity.LoadingActivity"