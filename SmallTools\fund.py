import datetime


def calculate_investment_returns():
    # 输入部分
    print("投资收益计算器")
    print("日期格式说明：请直接输入连续数字，如2025年6月20日应输入20250620")

    start_date_str = input("请输入起始日期(格式如20250620): ")
    end_date_str = input("请输入截止日期(格式如20250620): ")
    initial_amount = float(input("请输入起始金额: "))
    current_amount = float(input("请输入当前金额: "))

    try:
        # 解析8位数字格式的日期
        start_date = datetime.datetime.strptime(start_date_str, "%Y%m%d").date()
        end_date = datetime.datetime.strptime(end_date_str, "%Y%m%d").date()

        # 计算天数差
        days = (end_date - start_date).days
        if days <= 0:
            print("错误：截止日期必须晚于起始日期")
            return

        # 计算盈利
        profit = current_amount - initial_amount
        profit_rate = (profit / initial_amount) * 100  # 总盈利率（相对于初始金额）

        # 计算每日盈利和每日盈利率
        daily_profit = profit / days
        daily_profit_rate = (profit / initial_amount) / days * 100

        # 计算年化收益率（每日盈利率×365）
        annualized_rate = daily_profit_rate * 365

        # 输出结果
        print("\n计算结果:")
        print(f"起始日期: {start_date.strftime('%Y-%m-%d')}")
        print(f"截止日期: {end_date.strftime('%Y-%m-%d')}")
        print(f"投资天数: {days} 天")
        print(f"起始金额: {initial_amount:,.2f}")
        print(f"当前金额: {current_amount:,.2f}")
        print(f"盈利金额: {profit:+,.2f}")  # 使用+号显示正负
        print(f"总盈利率: {profit_rate:+.2f}%")  # 使用+号显示正负
        print(f"每日平均盈利: {daily_profit:+,.4f}")
        print(f"每日平均盈利率: {daily_profit_rate:+.6f}%")
        print(f"年化收益率: {annualized_rate:+.2f}%")

    except ValueError as e:
        print(f"输入错误: {e}。请确保日期为8位数字(如20250620)，金额为数字")


if __name__ == "__main__":
    calculate_investment_returns()