import requests
from Appsign import getsign
import json

baseurl = {'voyage1': 'http://k8s-voyageqa-ingressv-0e43378f0b-1537062284.ap-southeast-1.elb.amazonaws.com/voyage/',
           'voyage':'https://qa-voyage.digitalconch.com/voyage/',
           'boom': 'https://qa.boom.caniculab.com/boom/'}


class App():
    def __init__(self, appname='boom', appid='btest', ph='14955336964'):
        self.appid = appid
        self.headers = getsign(True, appname, appid)
        print(self.headers)
        self.ph = ph
        self.baseurl = baseurl[appname]

    # 获取隐私政策
    def gerplo(self):
        url = self.baseurl + 'open/privacyPolicy'
        query = {'appId': self.appid, 'phoneType': '2'}
        response = requests.get(url, params=query, headers=self.headers)
        print("*********获取隐私政策***********")
        print(response.text)

    # 获取初始配置
    def getconfig(self):
        url = self.baseurl + 'client/init/config'
        response = requests.get(url, headers=self.headers)

        print("*********获取初始配置***********")
        print(response.text)

    # 安卓验证
    def initandroid(self):
        url = self.baseurl + 'client/init/config'
        query = {'packageName': 'com.man4fun.test', 'signature': '4b35d673684f04423b04bb88cbddf9d5'}
        response = requests.get(url, headers=self.headers)
        print("*********安卓验证***********")
        print(response.text)

    # ios验证
    def initios(self):
        url = self.baseurl + 'client/init/ios'
        query = {
            'bundleId': 'com.sparkinglab.tcamera',
            'idfa': '42CA2CDC-8D2D-4E18-9FC1-82EAB2D3A7AF',
            'uniqueId': '52493723CFA616FF0B3DAB41584C71AE',
            'uuid': '909f9f5346c3456e8b98ae4806cf87b3'
        }
        response = requests.get(url, params=query, headers=self.headers)
        print("*********ios验证***********")
        print(response.text)

    # 获取验证码
    def getcode(self, ph):
        url = self.baseurl + 'client/security/verificationCode'
        query = {"phoneNumber": ph,
                 'codeVerificationType': '0',
                 # 'bundleId': 'com.sparkinglab.tcamera',
                 # 'idfa': '42CA2CDC-8D2D-4E18-9FC1-82EAB2D3A7AF',
                 # 'uniqueId': '52493723CFA616FF0B3DAB41584C71AE',
                 # 'uuid': '909f9f5346c3456e8b98ae4806cf87b3'
                 }
        response = requests.get(url, params=query, headers=self.headers)
        print("*********获取验证码***********")
        print(response.text)

    # 手机号登录
    def phonelogin(self, ph):
        url = self.baseurl + 'client/security/phone/login'
        query = {"phoneNumber": ph,
                 "verificationCode": "1234",
                 'cancelClosing': '0',
                 'bundleId': 'com.sparkinglab.tcamera',
                 'idfa': '42CA2CDC-8D2D-4E18-9FC1-82EAB2D3A7AF',
                 'uniqueId': '52493723CFA616FF0B3DAB41584C71AE',
                 'uuid': '909f9f5346c3456e8b98ae4806cf87b3',
                 'openId': ''
                 }
        query = sorted(query.items())
        query = dict(query)
        response = requests.post(url, data=query, headers=self.headers)
        print(response)
        # boom_token=json.loads(response.text)["result"]['token']
        # return boom_token

    # 注销提示框，非实际注销
    def logout(self):
        url = self.baseurl + 'client/security/logout'
        query = {'bundleId': 'com.sparkinglab.tcamera',
                 'idfa': '42CA2CDC-8D2D-4E18-9FC1-82EAB2D3A7AF',
                 'uniqueId': '52493723CFA616FF0B3DAB41584C71AE',
                 'uuid': '909f9f5346c3456e8b98ae4806cf87b3',
                 'openId': ''}
        response = requests.post(url, data=query, headers=self.headers)
        print(response.text)

    def truelogout(self):
        url = self.baseurl + 'client/security/logout/check'
        query = {'waitingDays': 1}
        response = requests.post(url, data=query, headers=self.headers)
        print(response.text)

    # 检测unqiueid是否可用
    def checku(self):
        url = self.baseurl + 'client/security/guest/check'
        query = {'uniqueId': '2024092999'
                 }
        response = requests.post(url, data=query, headers=self.headers)
        print(response.text)

    # 游客登陆
    def guestlogin(self):
        url = self.baseurl + 'client/security/guest/login'
        query = {'uniqueId': '2024101804'
                 }
        response = requests.post(url, data=query, headers=self.headers)
        print(response.text)
    def dataupload(self):
        url=self.baseurl+'client/data'
        query={"data":[
    {
      "aaid": "string2",
      "androidId": "string2",
      "code": 0,
      "eventId": 11,
      # "exStr1": "string",
      # "exStr2": "string",
      # "idfa": "string",
      "model": "strin2g",
      "netType": "string2",
      "opTime": 0,
      "openId": "yg28295931-qa-10067",
      "reason": "string2",
      "resolution": "strin2g",
      "source": "strin2g",
      "uploadTime": '1729240586000',
      "uuid": "stri2ng",
      "version": "string"
    }
  ]}
        response = requests.post(url, json=query, headers=self.headers)
        print(response.text)


    def loginbound(self):
        url=self.baseurl+"client/security/login/bound/apple"
        data={'authorizationCode':'',
              'userStr':""
        }
        url = self.baseurl + "client/security/login/bound/facebook"
        data = {'jwt': '',
                'token': ""
                }
        url = self.baseurl + "client/security/login/bound/gamecenter"
        data = {'bundleId': '',
                'publicKeyUrl': "",
                'salt':'',
                'signature':'',
                'teamPlayerId':'',
                'timestamp':""
                }
        url = self.baseurl + "client/security/login/bound/google"
        data = {
                'token': "3bdcf39f4e04b67deb990d5389156a5d"
                }
        # url = self.baseurl + "client/security/login/bound/line"
        # data = {
        #     'token': ""
        # }
        # url = self.baseurl + "client/security/login/bound/playGames"
        # data = {'authCode': '',
        #         'displayName': "",
        #         'playerId': '',
        #         'title': '',
        #         }


        response=requests.post(url=url,data=data,headers=self.headers)
        print(response.text)

    def getboundlist(self):
        url=self.baseurl+'client/security/login/bound/list'
        headers=self.headers
        # headers['voyage-client-token']='e94ab5b8dd7d93edbde42c8d9fece7cc'
        response=requests.get(url,headers=headers)
        print(response.text)

    def start(self):
        # self.gerplo()
        # self.getconfig()
        self.initandroid()
        self.initios()
        # self.getcode(self.ph)
        # self.phonelogin(self.ph)
        self.checku()
        # self.guestlogin()
        # self.dataupload()


b = App('voyage', 'yg28295931')
b.getconfig()
# b.getboundlist()
# b.loginbound()
# b.start()
