"""
<AUTHOR>
@date 2020/6/23 0023
"""
from typing import Dict, List

import xlrd
from xlrd import Book
from xlrd.sheet import Cell

from biz.game.domain import ExcelGameCase

cases: Dict[str, Dict[int, ExcelGameCase]] = {}


def get_case(cn: str) -> Dict[int, ExcelGameCase]:
    return cases[cn]


def all_cases() -> Dict[str, Dict[int, ExcelGameCase]]:
    return cases


workbook: Book = xlrd.open_workbook(r'./resource/case/simple.xlsx')

for sheet in workbook.sheets():
    case_name: str = sheet.name

    if not case_name.startswith('CASE'):
        continue
    case: Dict[int, ExcelGameCase] = {}

    for i in range(1, sheet.nrows):
        row: List[Cell] = sheet.row(i)

        turn = int(row[0].value)

        case.setdefault(turn, ExcelGameCase())
        game_case = case[turn]

        action = row[1].value
        v2 = row[2].value
        v3 = row[3].value
        if action == 'kill':
            game_case.kill[int(v2)] = int(v3)
        elif action == 'rescue_confirm':
            game_case.rescue_confirm = int(v2)
        elif action == 'rescue_cancel':
            game_case.rescue_cancel = int(v2)
        elif action == 'poison_select':
            game_case.poison_select = (int(v2), int(v3))
        elif action == 'poison_confirm':
            game_case.poison_confirm = int(v2)
        elif action == 'poison_cancel':
            game_case.poison_cancel = int(v2)
        elif action == 'speak_end':
            game_case.speak_end.append(int(v2))
        elif action == 'vote_select':
            game_case.vote_select[int(v2)] = int(v3)
        elif action == 'vote_confirm':
            game_case.vote_confirm.append(int(v2))
        elif action == 'vote_cancel':
            game_case.vote_cancel.append(int(v2))
        elif action == 'last_words_end':
            game_case.last_words_end = int(v2)
        elif action == 'assert':
            game_case.asserts[v2] = v3
        else:
            raise RuntimeError("unknown action: " + action)
    for turn in case:
        case[turn].validate_case()
    cases[case_name] = case
