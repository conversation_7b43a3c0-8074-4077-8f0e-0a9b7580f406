#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试二维码生成功能
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from app import generate_qr_code

def test_qr_generation():
    """测试二维码生成功能"""
    try:
        # 测试生成二维码
        test_url = "https://www.example.com"
        qr_data = generate_qr_code(test_url)
        
        print("✓ 二维码生成成功！")
        print(f"生成的数据长度: {len(qr_data)} 字符")
        print(f"数据格式: {qr_data[:50]}...")
        
        # 验证是否是有效的base64数据URL
        if qr_data.startswith("data:image/png;base64,"):
            print("✓ 二维码格式正确")
            return True
        else:
            print("✗ 二维码格式错误")
            return False
            
    except Exception as e:
        print(f"✗ 二维码生成失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("开始测试二维码生成功能...")
    success = test_qr_generation()
    
    if success:
        print("\n🎉 所有测试通过！二维码生成功能正常工作。")
    else:
        print("\n❌ 测试失败！请检查代码。")
