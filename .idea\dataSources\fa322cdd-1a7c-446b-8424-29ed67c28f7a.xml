<?xml version="1.0" encoding="UTF-8"?>
<dataSource name="boom">
  <database-model serializer="dbm" dbms="MYSQL" family-id="MYSQL" format-version="4.17">
    <root id="1">
      <ServerVersion>5.7.28</ServerVersion>
      <DefaultEngine>InnoDB</DefaultEngine>
      <DefaultTmpEngine>MyISAM</DefaultTmpEngine>
      <DefaultCasing>lower/lower</DefaultCasing>
    </root>
    <schema id="2" parent="1" name="boom_account">
      <Current>1</Current>
      <Collation>utf8mb4_general_ci</Collation>
    </schema>
    <schema id="3" parent="1" name="boom_admin">
      <Collation>utf8mb4_general_ci</Collation>
    </schema>
    <schema id="4" parent="1" name="boom_data">
      <Collation>utf8mb4_general_ci</Collation>
    </schema>
    <schema id="5" parent="1" name="boom_system">
      <Collation>utf8mb4_general_ci</Collation>
    </schema>
    <schema id="6" parent="1" name="boom_user">
      <Collation>utf8mb4_general_ci</Collation>
    </schema>
    <schema id="7" parent="1" name="information_schema">
      <Collation>utf8_general_ci</Collation>
    </schema>
    <schema id="8" parent="1" name="mysql">
      <Collation>utf8mb4_general_ci</Collation>
    </schema>
    <collation id="9" parent="1" name="armscii8_bin">
      <Charset>armscii8</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="10" parent="1" name="armscii8_general_ci">
      <Charset>armscii8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="11" parent="1" name="ascii_bin">
      <Charset>ascii</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="12" parent="1" name="ascii_general_ci">
      <Charset>ascii</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="13" parent="1" name="big5_bin">
      <Charset>big5</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="14" parent="1" name="big5_chinese_ci">
      <Charset>big5</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="15" parent="1" name="binary">
      <Charset>binary</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="16" parent="1" name="cp1250_bin">
      <Charset>cp1250</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="17" parent="1" name="cp1250_croatian_ci">
      <Charset>cp1250</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="18" parent="1" name="cp1250_czech_cs">
      <Charset>cp1250</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="19" parent="1" name="cp1250_general_ci">
      <Charset>cp1250</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="20" parent="1" name="cp1250_polish_ci">
      <Charset>cp1250</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="21" parent="1" name="cp1251_bin">
      <Charset>cp1251</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="22" parent="1" name="cp1251_bulgarian_ci">
      <Charset>cp1251</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="23" parent="1" name="cp1251_general_ci">
      <Charset>cp1251</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="24" parent="1" name="cp1251_general_cs">
      <Charset>cp1251</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="25" parent="1" name="cp1251_ukrainian_ci">
      <Charset>cp1251</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="26" parent="1" name="cp1256_bin">
      <Charset>cp1256</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="27" parent="1" name="cp1256_general_ci">
      <Charset>cp1256</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="28" parent="1" name="cp1257_bin">
      <Charset>cp1257</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="29" parent="1" name="cp1257_general_ci">
      <Charset>cp1257</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="30" parent="1" name="cp1257_lithuanian_ci">
      <Charset>cp1257</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="31" parent="1" name="cp850_bin">
      <Charset>cp850</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="32" parent="1" name="cp850_general_ci">
      <Charset>cp850</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="33" parent="1" name="cp852_bin">
      <Charset>cp852</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="34" parent="1" name="cp852_general_ci">
      <Charset>cp852</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="35" parent="1" name="cp866_bin">
      <Charset>cp866</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="36" parent="1" name="cp866_general_ci">
      <Charset>cp866</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="37" parent="1" name="cp932_bin">
      <Charset>cp932</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="38" parent="1" name="cp932_japanese_ci">
      <Charset>cp932</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="39" parent="1" name="dec8_bin">
      <Charset>dec8</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="40" parent="1" name="dec8_swedish_ci">
      <Charset>dec8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="41" parent="1" name="eucjpms_bin">
      <Charset>eucjpms</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="42" parent="1" name="eucjpms_japanese_ci">
      <Charset>eucjpms</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="43" parent="1" name="euckr_bin">
      <Charset>euckr</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="44" parent="1" name="euckr_korean_ci">
      <Charset>euckr</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="45" parent="1" name="gb18030_bin">
      <Charset>gb18030</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="46" parent="1" name="gb18030_chinese_ci">
      <Charset>gb18030</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="47" parent="1" name="gb18030_unicode_520_ci">
      <Charset>gb18030</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="48" parent="1" name="gb2312_bin">
      <Charset>gb2312</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="49" parent="1" name="gb2312_chinese_ci">
      <Charset>gb2312</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="50" parent="1" name="gbk_bin">
      <Charset>gbk</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="51" parent="1" name="gbk_chinese_ci">
      <Charset>gbk</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="52" parent="1" name="geostd8_bin">
      <Charset>geostd8</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="53" parent="1" name="geostd8_general_ci">
      <Charset>geostd8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="54" parent="1" name="greek_bin">
      <Charset>greek</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="55" parent="1" name="greek_general_ci">
      <Charset>greek</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="56" parent="1" name="hebrew_bin">
      <Charset>hebrew</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="57" parent="1" name="hebrew_general_ci">
      <Charset>hebrew</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="58" parent="1" name="hp8_bin">
      <Charset>hp8</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="59" parent="1" name="hp8_english_ci">
      <Charset>hp8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="60" parent="1" name="keybcs2_bin">
      <Charset>keybcs2</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="61" parent="1" name="keybcs2_general_ci">
      <Charset>keybcs2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="62" parent="1" name="koi8r_bin">
      <Charset>koi8r</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="63" parent="1" name="koi8r_general_ci">
      <Charset>koi8r</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="64" parent="1" name="koi8u_bin">
      <Charset>koi8u</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="65" parent="1" name="koi8u_general_ci">
      <Charset>koi8u</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="66" parent="1" name="latin1_bin">
      <Charset>latin1</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="67" parent="1" name="latin1_danish_ci">
      <Charset>latin1</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="68" parent="1" name="latin1_general_ci">
      <Charset>latin1</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="69" parent="1" name="latin1_general_cs">
      <Charset>latin1</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="70" parent="1" name="latin1_german1_ci">
      <Charset>latin1</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="71" parent="1" name="latin1_german2_ci">
      <Charset>latin1</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="72" parent="1" name="latin1_spanish_ci">
      <Charset>latin1</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="73" parent="1" name="latin1_swedish_ci">
      <Charset>latin1</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="74" parent="1" name="latin2_bin">
      <Charset>latin2</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="75" parent="1" name="latin2_croatian_ci">
      <Charset>latin2</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="76" parent="1" name="latin2_czech_cs">
      <Charset>latin2</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="77" parent="1" name="latin2_general_ci">
      <Charset>latin2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="78" parent="1" name="latin2_hungarian_ci">
      <Charset>latin2</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="79" parent="1" name="latin5_bin">
      <Charset>latin5</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="80" parent="1" name="latin5_turkish_ci">
      <Charset>latin5</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="81" parent="1" name="latin7_bin">
      <Charset>latin7</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="82" parent="1" name="latin7_estonian_cs">
      <Charset>latin7</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="83" parent="1" name="latin7_general_ci">
      <Charset>latin7</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="84" parent="1" name="latin7_general_cs">
      <Charset>latin7</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="85" parent="1" name="macce_bin">
      <Charset>macce</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="86" parent="1" name="macce_general_ci">
      <Charset>macce</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="87" parent="1" name="macroman_bin">
      <Charset>macroman</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="88" parent="1" name="macroman_general_ci">
      <Charset>macroman</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="89" parent="1" name="sjis_bin">
      <Charset>sjis</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="90" parent="1" name="sjis_japanese_ci">
      <Charset>sjis</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="91" parent="1" name="swe7_bin">
      <Charset>swe7</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="92" parent="1" name="swe7_swedish_ci">
      <Charset>swe7</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="93" parent="1" name="tis620_bin">
      <Charset>tis620</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="94" parent="1" name="tis620_thai_ci">
      <Charset>tis620</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="95" parent="1" name="ucs2_bin">
      <Charset>ucs2</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="96" parent="1" name="ucs2_croatian_ci">
      <Charset>ucs2</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="97" parent="1" name="ucs2_czech_ci">
      <Charset>ucs2</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="98" parent="1" name="ucs2_danish_ci">
      <Charset>ucs2</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="99" parent="1" name="ucs2_esperanto_ci">
      <Charset>ucs2</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="100" parent="1" name="ucs2_estonian_ci">
      <Charset>ucs2</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="101" parent="1" name="ucs2_general_ci">
      <Charset>ucs2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="102" parent="1" name="ucs2_general_mysql500_ci">
      <Charset>ucs2</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="103" parent="1" name="ucs2_german2_ci">
      <Charset>ucs2</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="104" parent="1" name="ucs2_hungarian_ci">
      <Charset>ucs2</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="105" parent="1" name="ucs2_icelandic_ci">
      <Charset>ucs2</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="106" parent="1" name="ucs2_latvian_ci">
      <Charset>ucs2</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="107" parent="1" name="ucs2_lithuanian_ci">
      <Charset>ucs2</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="108" parent="1" name="ucs2_persian_ci">
      <Charset>ucs2</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="109" parent="1" name="ucs2_polish_ci">
      <Charset>ucs2</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="110" parent="1" name="ucs2_roman_ci">
      <Charset>ucs2</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="111" parent="1" name="ucs2_romanian_ci">
      <Charset>ucs2</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="112" parent="1" name="ucs2_sinhala_ci">
      <Charset>ucs2</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="113" parent="1" name="ucs2_slovak_ci">
      <Charset>ucs2</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="114" parent="1" name="ucs2_slovenian_ci">
      <Charset>ucs2</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="115" parent="1" name="ucs2_spanish2_ci">
      <Charset>ucs2</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="116" parent="1" name="ucs2_spanish_ci">
      <Charset>ucs2</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="117" parent="1" name="ucs2_swedish_ci">
      <Charset>ucs2</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="118" parent="1" name="ucs2_turkish_ci">
      <Charset>ucs2</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="119" parent="1" name="ucs2_unicode_520_ci">
      <Charset>ucs2</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="120" parent="1" name="ucs2_unicode_ci">
      <Charset>ucs2</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="121" parent="1" name="ucs2_vietnamese_ci">
      <Charset>ucs2</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="122" parent="1" name="ujis_bin">
      <Charset>ujis</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="123" parent="1" name="ujis_japanese_ci">
      <Charset>ujis</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="124" parent="1" name="utf16_bin">
      <Charset>utf16</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="125" parent="1" name="utf16_croatian_ci">
      <Charset>utf16</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="126" parent="1" name="utf16_czech_ci">
      <Charset>utf16</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="127" parent="1" name="utf16_danish_ci">
      <Charset>utf16</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="128" parent="1" name="utf16_esperanto_ci">
      <Charset>utf16</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="129" parent="1" name="utf16_estonian_ci">
      <Charset>utf16</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="130" parent="1" name="utf16_general_ci">
      <Charset>utf16</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="131" parent="1" name="utf16_german2_ci">
      <Charset>utf16</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="132" parent="1" name="utf16_hungarian_ci">
      <Charset>utf16</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="133" parent="1" name="utf16_icelandic_ci">
      <Charset>utf16</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="134" parent="1" name="utf16_latvian_ci">
      <Charset>utf16</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="135" parent="1" name="utf16_lithuanian_ci">
      <Charset>utf16</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="136" parent="1" name="utf16_persian_ci">
      <Charset>utf16</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="137" parent="1" name="utf16_polish_ci">
      <Charset>utf16</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="138" parent="1" name="utf16_roman_ci">
      <Charset>utf16</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="139" parent="1" name="utf16_romanian_ci">
      <Charset>utf16</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="140" parent="1" name="utf16_sinhala_ci">
      <Charset>utf16</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="141" parent="1" name="utf16_slovak_ci">
      <Charset>utf16</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="142" parent="1" name="utf16_slovenian_ci">
      <Charset>utf16</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="143" parent="1" name="utf16_spanish2_ci">
      <Charset>utf16</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="144" parent="1" name="utf16_spanish_ci">
      <Charset>utf16</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="145" parent="1" name="utf16_swedish_ci">
      <Charset>utf16</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="146" parent="1" name="utf16_turkish_ci">
      <Charset>utf16</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="147" parent="1" name="utf16_unicode_520_ci">
      <Charset>utf16</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="148" parent="1" name="utf16_unicode_ci">
      <Charset>utf16</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="149" parent="1" name="utf16_vietnamese_ci">
      <Charset>utf16</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="150" parent="1" name="utf16le_bin">
      <Charset>utf16le</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="151" parent="1" name="utf16le_general_ci">
      <Charset>utf16le</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="152" parent="1" name="utf32_bin">
      <Charset>utf32</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="153" parent="1" name="utf32_croatian_ci">
      <Charset>utf32</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="154" parent="1" name="utf32_czech_ci">
      <Charset>utf32</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="155" parent="1" name="utf32_danish_ci">
      <Charset>utf32</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="156" parent="1" name="utf32_esperanto_ci">
      <Charset>utf32</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="157" parent="1" name="utf32_estonian_ci">
      <Charset>utf32</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="158" parent="1" name="utf32_general_ci">
      <Charset>utf32</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="159" parent="1" name="utf32_german2_ci">
      <Charset>utf32</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="160" parent="1" name="utf32_hungarian_ci">
      <Charset>utf32</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="161" parent="1" name="utf32_icelandic_ci">
      <Charset>utf32</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="162" parent="1" name="utf32_latvian_ci">
      <Charset>utf32</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="163" parent="1" name="utf32_lithuanian_ci">
      <Charset>utf32</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="164" parent="1" name="utf32_persian_ci">
      <Charset>utf32</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="165" parent="1" name="utf32_polish_ci">
      <Charset>utf32</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="166" parent="1" name="utf32_roman_ci">
      <Charset>utf32</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="167" parent="1" name="utf32_romanian_ci">
      <Charset>utf32</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="168" parent="1" name="utf32_sinhala_ci">
      <Charset>utf32</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="169" parent="1" name="utf32_slovak_ci">
      <Charset>utf32</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="170" parent="1" name="utf32_slovenian_ci">
      <Charset>utf32</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="171" parent="1" name="utf32_spanish2_ci">
      <Charset>utf32</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="172" parent="1" name="utf32_spanish_ci">
      <Charset>utf32</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="173" parent="1" name="utf32_swedish_ci">
      <Charset>utf32</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="174" parent="1" name="utf32_turkish_ci">
      <Charset>utf32</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="175" parent="1" name="utf32_unicode_520_ci">
      <Charset>utf32</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="176" parent="1" name="utf32_unicode_ci">
      <Charset>utf32</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="177" parent="1" name="utf32_vietnamese_ci">
      <Charset>utf32</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="178" parent="1" name="utf8_bin">
      <Charset>utf8</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="179" parent="1" name="utf8_croatian_ci">
      <Charset>utf8</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="180" parent="1" name="utf8_czech_ci">
      <Charset>utf8</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="181" parent="1" name="utf8_danish_ci">
      <Charset>utf8</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="182" parent="1" name="utf8_esperanto_ci">
      <Charset>utf8</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="183" parent="1" name="utf8_estonian_ci">
      <Charset>utf8</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="184" parent="1" name="utf8_general_ci">
      <Charset>utf8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="185" parent="1" name="utf8_general_mysql500_ci">
      <Charset>utf8</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="186" parent="1" name="utf8_german2_ci">
      <Charset>utf8</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="187" parent="1" name="utf8_hungarian_ci">
      <Charset>utf8</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="188" parent="1" name="utf8_icelandic_ci">
      <Charset>utf8</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="189" parent="1" name="utf8_latvian_ci">
      <Charset>utf8</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="190" parent="1" name="utf8_lithuanian_ci">
      <Charset>utf8</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="191" parent="1" name="utf8_persian_ci">
      <Charset>utf8</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="192" parent="1" name="utf8_polish_ci">
      <Charset>utf8</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="193" parent="1" name="utf8_roman_ci">
      <Charset>utf8</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="194" parent="1" name="utf8_romanian_ci">
      <Charset>utf8</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="195" parent="1" name="utf8_sinhala_ci">
      <Charset>utf8</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="196" parent="1" name="utf8_slovak_ci">
      <Charset>utf8</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="197" parent="1" name="utf8_slovenian_ci">
      <Charset>utf8</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="198" parent="1" name="utf8_spanish2_ci">
      <Charset>utf8</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="199" parent="1" name="utf8_spanish_ci">
      <Charset>utf8</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="200" parent="1" name="utf8_swedish_ci">
      <Charset>utf8</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="201" parent="1" name="utf8_turkish_ci">
      <Charset>utf8</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="202" parent="1" name="utf8_unicode_520_ci">
      <Charset>utf8</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="203" parent="1" name="utf8_unicode_ci">
      <Charset>utf8</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="204" parent="1" name="utf8_vietnamese_ci">
      <Charset>utf8</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="205" parent="1" name="utf8mb4_bin">
      <Charset>utf8mb4</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="206" parent="1" name="utf8mb4_croatian_ci">
      <Charset>utf8mb4</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="207" parent="1" name="utf8mb4_czech_ci">
      <Charset>utf8mb4</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="208" parent="1" name="utf8mb4_danish_ci">
      <Charset>utf8mb4</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="209" parent="1" name="utf8mb4_esperanto_ci">
      <Charset>utf8mb4</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="210" parent="1" name="utf8mb4_estonian_ci">
      <Charset>utf8mb4</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="211" parent="1" name="utf8mb4_general_ci">
      <Charset>utf8mb4</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="212" parent="1" name="utf8mb4_german2_ci">
      <Charset>utf8mb4</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="213" parent="1" name="utf8mb4_hungarian_ci">
      <Charset>utf8mb4</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="214" parent="1" name="utf8mb4_icelandic_ci">
      <Charset>utf8mb4</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="215" parent="1" name="utf8mb4_latvian_ci">
      <Charset>utf8mb4</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="216" parent="1" name="utf8mb4_lithuanian_ci">
      <Charset>utf8mb4</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="217" parent="1" name="utf8mb4_persian_ci">
      <Charset>utf8mb4</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="218" parent="1" name="utf8mb4_polish_ci">
      <Charset>utf8mb4</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="219" parent="1" name="utf8mb4_roman_ci">
      <Charset>utf8mb4</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="220" parent="1" name="utf8mb4_romanian_ci">
      <Charset>utf8mb4</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="221" parent="1" name="utf8mb4_sinhala_ci">
      <Charset>utf8mb4</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="222" parent="1" name="utf8mb4_slovak_ci">
      <Charset>utf8mb4</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="223" parent="1" name="utf8mb4_slovenian_ci">
      <Charset>utf8mb4</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="224" parent="1" name="utf8mb4_spanish2_ci">
      <Charset>utf8mb4</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="225" parent="1" name="utf8mb4_spanish_ci">
      <Charset>utf8mb4</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="226" parent="1" name="utf8mb4_swedish_ci">
      <Charset>utf8mb4</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="227" parent="1" name="utf8mb4_turkish_ci">
      <Charset>utf8mb4</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="228" parent="1" name="utf8mb4_unicode_520_ci">
      <Charset>utf8mb4</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="229" parent="1" name="utf8mb4_unicode_ci">
      <Charset>utf8mb4</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <collation id="230" parent="1" name="utf8mb4_vietnamese_ci">
      <Charset>utf8mb4</Charset>
      <DefaultForCharset>0</DefaultForCharset>
    </collation>
    <table id="231" parent="4" name="bmc_game_role_info">
      <Comment>bmc游戏角色信息</Comment>
    </table>
    <table id="232" parent="4" name="data_collection">
      <Comment>数据收集&#xd;
</Comment>
    </table>
    <table id="233" parent="4" name="data_collection_error">
      <Comment>数据收集错误原因</Comment>
    </table>
    <table id="234" parent="4" name="data_collection_extend">
      <Comment>数据收集扩展字段表</Comment>
    </table>
    <table id="235" parent="4" name="data_collection_statistics">
      <Comment>数据收集统计</Comment>
    </table>
    <table id="236" parent="4" name="device_statistics">
      <Comment>设备统计</Comment>
    </table>
    <table id="237" parent="4" name="feedback_reply_bot_config">
      <Comment>问题反馈回复机器人配置</Comment>
    </table>
    <table id="238" parent="4" name="openid_apple_ad_record">
      <Comment>苹果广告数据记录</Comment>
    </table>
    <table id="239" parent="4" name="openid_bilibili_ad_record">
      <Comment>bilibili广告数据记录</Comment>
    </table>
    <table id="240" parent="4" name="openid_kuaishou_ad_record">
      <Comment>快手广告数据记录</Comment>
    </table>
    <table id="241" parent="4" name="openid_login_hour_statistics">
      <Comment>openId登录每小时统计</Comment>
    </table>
    <table id="242" parent="4" name="openid_login_statistics">
      <Comment>openId登录统计</Comment>
    </table>
    <table id="243" parent="4" name="openid_recharge_hour_statistics">
      <Comment>openId充值每小时统计</Comment>
    </table>
    <table id="244" parent="4" name="openid_recharge_statistics">
      <Comment>openId充值统计</Comment>
    </table>
    <table id="245" parent="4" name="openid_register_statistics">
      <Comment>openId注册统计</Comment>
    </table>
    <table id="246" parent="4" name="openid_tiktok_ad_record">
      <Comment>抖音广告数据记录</Comment>
    </table>
    <table id="247" parent="4" name="openid_tiktok_transmission_record">
      <Comment>openId 抖音传播任务数据记录</Comment>
    </table>
    <table id="248" parent="4" name="openid_weibo_ad_record">
      <Comment>微博广告数据记录</Comment>
    </table>
    <table id="249" parent="4" name="user_feedback">
      <Comment>问题反馈</Comment>
    </table>
    <table id="250" parent="4" name="user_feedback_reply">
      <Comment>问题反馈回复</Comment>
    </table>
    <table id="251" parent="4" name="user_feedback_screenshots">
      <Comment>问题反馈截图</Comment>
    </table>
    <table id="252" parent="4" name="uuid_apple_ad_record">
      <Comment>苹果广告数据记录</Comment>
    </table>
    <table id="253" parent="4" name="uuid_bilibili_ad_record">
      <Comment>bilibili广告数据记录</Comment>
    </table>
    <table id="254" parent="4" name="uuid_conver_statistics">
      <Comment>uuid转化统计</Comment>
    </table>
    <table id="255" parent="4" name="uuid_kuaishou_ad_record">
      <Comment>快手广告数据记录</Comment>
    </table>
    <table id="256" parent="4" name="uuid_login_hour_statistics">
      <Comment>uuid登录每小时统计</Comment>
    </table>
    <table id="257" parent="4" name="uuid_login_statistics">
      <Comment>uuid登录统计</Comment>
    </table>
    <table id="258" parent="4" name="uuid_recharge_hour_statistics">
      <Comment>uuid充值每小时统计</Comment>
    </table>
    <table id="259" parent="4" name="uuid_recharge_statistics">
      <Comment>uuid充值统计</Comment>
    </table>
    <table id="260" parent="4" name="uuid_register_statistics">
      <Comment>uuid注册统计</Comment>
    </table>
    <table id="261" parent="4" name="uuid_tiktok_ad_record">
      <Comment>抖音广告数据记录</Comment>
    </table>
    <table id="262" parent="4" name="uuid_tiktok_transmission_record">
      <Comment>uuid 抖音传播任务数据记录</Comment>
    </table>
    <table id="263" parent="4" name="uuid_weibo_ad_record">
      <Comment>微博广告数据记录</Comment>
    </table>
    <table id="264" parent="4" name="yidun_device_statistics">
      <Comment>易盾设备统计</Comment>
    </table>
    <column id="265" parent="231" name="bmcGameRoleInfo">
      <Position>1</Position>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <SequenceIdentity>1</SequenceIdentity>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="266" parent="231" name="appId">
      <Position>2</Position>
      <Comment>appId</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="267" parent="231" name="openId">
      <Position>3</Position>
      <Comment>openId</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="268" parent="231" name="bmcType">
      <Position>4</Position>
      <Comment>bmcType枚举</Comment>
      <DataType>int(4)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="269" parent="231" name="serviceId">
      <Position>5</Position>
      <Comment>服务id</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="270" parent="231" name="serviceName">
      <Position>6</Position>
      <Comment>服务名</Comment>
      <DataType>varchar(100)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="271" parent="231" name="roleName">
      <Position>7</Position>
      <Comment>角色名</Comment>
      <DataType>varchar(50)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="272" parent="231" name="roleId">
      <Position>8</Position>
      <Comment>角色Id</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="273" parent="231" name="roleLv">
      <Position>9</Position>
      <Comment>角色等级</Comment>
      <DataType>varchar(20)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="274" parent="231" name="roleBalance">
      <Position>10</Position>
      <Comment>余额</Comment>
      <DataType>varchar(20)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="275" parent="231" name="rolePower">
      <Position>11</Position>
      <Comment>战斗力</Comment>
      <DataType>varchar(50)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="276" parent="231" name="vipLv">
      <Position>12</Position>
      <Comment>vip等级</Comment>
      <DataType>varchar(20)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="277" parent="231" name="chapter">
      <Position>13</Position>
      <Comment>角色解锁关卡</Comment>
      <DataType>varchar(50)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="278" parent="231" name="guildName">
      <Position>14</Position>
      <Comment>公会名</Comment>
      <DataType>varchar(100)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="279" parent="231" name="ext">
      <Position>15</Position>
      <Comment>扩展字段</Comment>
      <DataType>json|0s</DataType>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="280" parent="231" name="status">
      <Position>16</Position>
      <Comment>状态 0默认</Comment>
      <DataType>int(4)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="281" parent="231" name="createdBy">
      <Position>17</Position>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="282" parent="231" name="createdOn">
      <Position>18</Position>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="283" parent="231" name="modifiedBy">
      <Position>19</Position>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="284" parent="231" name="modifiedOn">
      <Position>20</Position>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <index id="285" parent="231" name="ix_appId_openId">
      <ColNames>appId
openId</ColNames>
      <Unique>1</Unique>
      <Type>btree</Type>
    </index>
    <index id="286" parent="231" name="ix_roleId">
      <ColNames>roleId</ColNames>
      <Type>btree</Type>
    </index>
    <key id="287" parent="231" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <ColNames>bmcGameRoleInfo</ColNames>
      <Primary>1</Primary>
    </key>
    <key id="288" parent="231" name="ix_appId_openId">
      <ColNames>appId
openId</ColNames>
      <UnderlyingIndexName>ix_appId_openId</UnderlyingIndexName>
    </key>
    <column id="289" parent="232" name="dataId">
      <Position>1</Position>
      <DataType>int(11)|0s</DataType>
      <NotNull>1</NotNull>
      <SequenceIdentity>1</SequenceIdentity>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="290" parent="232" name="appId">
      <Position>2</Position>
      <Comment>appId</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="291" parent="232" name="eventId">
      <Position>3</Position>
      <Comment>上报事件id</Comment>
      <DataType>int(4)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="292" parent="232" name="openId">
      <Position>4</Position>
      <Comment>openId</Comment>
      <DataType>varchar(32)|0s</DataType>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="293" parent="232" name="userId">
      <Position>5</Position>
      <Comment>userId</Comment>
      <DataType>bigint(11)|0s</DataType>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="294" parent="232" name="sdkVersion">
      <Position>6</Position>
      <Comment>sdk版本</Comment>
      <DataType>varchar(20)|0s</DataType>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="295" parent="232" name="uuid">
      <Position>7</Position>
      <Comment>设备Id</Comment>
      <DataType>varchar(100)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="296" parent="232" name="phoneType">
      <Position>8</Position>
      <Comment>安卓1 ios 2</Comment>
      <DataType>tinyint(4)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="297" parent="232" name="bmcType">
      <Position>9</Position>
      <Comment>bmcType枚举 0boom</Comment>
      <DataType>int(11)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="298" parent="232" name="source">
      <Position>10</Position>
      <Comment>渠道</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="299" parent="232" name="uploadTime">
      <Position>11</Position>
      <Comment>日志上报时间</Comment>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="300" parent="232" name="userIp">
      <Position>12</Position>
      <Comment>用户ip</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="301" parent="232" name="model">
      <Position>13</Position>
      <Comment>手机型号</Comment>
      <DataType>varchar(32)|0s</DataType>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="302" parent="232" name="version">
      <Position>14</Position>
      <Comment>系统版本</Comment>
      <DataType>varchar(255)|0s</DataType>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="303" parent="232" name="resolution">
      <Position>15</Position>
      <Comment>分辨率</Comment>
      <DataType>varchar(10)|0s</DataType>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="304" parent="232" name="netType">
      <Position>16</Position>
      <Comment>上网方式</Comment>
      <DataType>varchar(255)|0s</DataType>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="305" parent="232" name="opTime">
      <Position>17</Position>
      <Comment>操作时间</Comment>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="306" parent="232" name="imei">
      <Position>18</Position>
      <Comment>国际移动设备识别码</Comment>
      <DataType>varchar(17)|0s</DataType>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="307" parent="232" name="oaid">
      <Position>19</Position>
      <Comment>广告标识符（安卓）无需MD5</Comment>
      <DataType>varchar(64)|0s</DataType>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="308" parent="232" name="androidId">
      <Position>20</Position>
      <Comment>安卓设备ID（安卓）无需MD5</Comment>
      <DataType>varchar(64)|0s</DataType>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="309" parent="232" name="idfa">
      <Position>21</Position>
      <Comment>IOS广告标识符（IOS）无需MD5</Comment>
      <DataType>varchar(36)|0s</DataType>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="310" parent="232" name="createdBy">
      <Position>22</Position>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="311" parent="232" name="createdOn">
      <Position>23</Position>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <index id="312" parent="232" name="ix_appId_eventId_openId">
      <ColNames>appId
eventId
openId</ColNames>
      <Type>btree</Type>
    </index>
    <key id="313" parent="232" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <ColNames>dataId
createdOn</ColNames>
      <Primary>1</Primary>
    </key>
    <column id="314" parent="233" name="dataId">
      <Position>1</Position>
      <DataType>int(11)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="315" parent="233" name="errorCode">
      <Position>2</Position>
      <Comment>错误吗</Comment>
      <DataType>int(4)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="316" parent="233" name="errorReason">
      <Position>3</Position>
      <Comment>错误原因</Comment>
      <DataType>text|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <key id="317" parent="233" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <ColNames>dataId</ColNames>
      <Primary>1</Primary>
    </key>
    <column id="318" parent="234" name="dataId">
      <Position>1</Position>
      <DataType>int(11)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="319" parent="234" name="exStr1">
      <Position>2</Position>
      <Comment>扩展字段1</Comment>
      <DataType>varchar(255)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="320" parent="234" name="exStr2">
      <Position>3</Position>
      <Comment>扩展字段2</Comment>
      <DataType>varchar(255)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <index id="321" parent="234" name="ix_exstr1">
      <ColNames>exStr1</ColNames>
      <Type>btree</Type>
    </index>
    <key id="322" parent="234" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <ColNames>dataId</ColNames>
      <Primary>1</Primary>
    </key>
    <column id="323" parent="235" name="dataStatisticsId">
      <Position>1</Position>
      <DataType>int(11)|0s</DataType>
      <NotNull>1</NotNull>
      <SequenceIdentity>1</SequenceIdentity>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="324" parent="235" name="appId">
      <Position>2</Position>
      <Comment>appId</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="325" parent="235" name="dateStr">
      <Position>3</Position>
      <Comment>日期YYYY-MM-dd</Comment>
      <DataType>char(10)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="326" parent="235" name="eventId">
      <Position>4</Position>
      <Comment>上报事件id</Comment>
      <DataType>int(4)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="327" parent="235" name="bmcType">
      <Position>5</Position>
      <Comment>bmcType枚举 0boom</Comment>
      <DataType>int(11)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="328" parent="235" name="exStr1">
      <Position>6</Position>
      <Comment>扩展字段1</Comment>
      <DataType>varchar(255)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="329" parent="235" name="exStr2">
      <Position>7</Position>
      <Comment>扩展字段2</Comment>
      <DataType>varchar(255)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="330" parent="235" name="count">
      <Position>8</Position>
      <Comment>数量</Comment>
      <DataType>int(11)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="331" parent="235" name="createdBy">
      <Position>9</Position>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="332" parent="235" name="createdOn">
      <Position>10</Position>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <index id="333" parent="235" name="ix_appId_dateStr_openId">
      <ColNames>appId
dateStr</ColNames>
      <Type>btree</Type>
    </index>
    <key id="334" parent="235" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <ColNames>dataStatisticsId</ColNames>
      <Primary>1</Primary>
    </key>
    <column id="335" parent="236" name="deviceStatisticsId">
      <Position>1</Position>
      <DataType>int(11)|0s</DataType>
      <NotNull>1</NotNull>
      <SequenceIdentity>1</SequenceIdentity>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="336" parent="236" name="appId">
      <Position>2</Position>
      <Comment>appId</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="337" parent="236" name="uuid">
      <Position>3</Position>
      <Comment>设备uuid</Comment>
      <DataType>varchar(64)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="338" parent="236" name="phoneType">
      <Position>4</Position>
      <Comment>设备类型</Comment>
      <DataType>tinyint(4)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="339" parent="236" name="createdBy">
      <Position>5</Position>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="340" parent="236" name="createdOn">
      <Position>6</Position>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="341" parent="236" name="modifiedBy">
      <Position>7</Position>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="342" parent="236" name="modifiedOn">
      <Position>8</Position>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <index id="343" parent="236" name="ix_appId_uuid">
      <ColNames>appId
uuid</ColNames>
      <Unique>1</Unique>
      <Type>btree</Type>
    </index>
    <key id="344" parent="236" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <ColNames>deviceStatisticsId</ColNames>
      <Primary>1</Primary>
    </key>
    <key id="345" parent="236" name="ix_appId_uuid">
      <ColNames>appId
uuid</ColNames>
      <UnderlyingIndexName>ix_appId_uuid</UnderlyingIndexName>
    </key>
    <column id="346" parent="237" name="replyBotId">
      <Position>1</Position>
      <DataType>int(11)|0s</DataType>
      <NotNull>1</NotNull>
      <SequenceIdentity>1</SequenceIdentity>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="347" parent="237" name="name">
      <Position>2</Position>
      <Comment>截图地址</Comment>
      <DataType>varchar(20)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="348" parent="237" name="describe">
      <Position>3</Position>
      <Comment>描述</Comment>
      <DataType>varchar(50)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="349" parent="237" name="reply">
      <Position>4</Position>
      <DataType>text|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="350" parent="237" name="createdOn">
      <Position>5</Position>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="351" parent="237" name="modifiedBy">
      <Position>6</Position>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="352" parent="237" name="modifiedOn">
      <Position>7</Position>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <key id="353" parent="237" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <ColNames>replyBotId</ColNames>
      <Primary>1</Primary>
    </key>
    <column id="354" parent="238" name="appId">
      <Position>1</Position>
      <Comment>appId</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="355" parent="238" name="openId">
      <Position>2</Position>
      <Comment>openId</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="356" parent="238" name="recordId">
      <Position>3</Position>
      <Comment>apple_promotion_event_record id</Comment>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="357" parent="238" name="repeatDevice">
      <Position>4</Position>
      <Comment>idfa 或 uuid 重复</Comment>
      <DataType>tinyint(2)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="358" parent="238" name="attribution">
      <Position>5</Position>
      <Comment>归因值</Comment>
      <DataType>tinyint(4)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="359" parent="238" name="orgId">
      <Position>6</Position>
      <Comment>拥有活动的组织的标识符</Comment>
      <DataType>int(11)|0s</DataType>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="360" parent="238" name="campaignId">
      <Position>7</Position>
      <Comment>活动的唯一标识符</Comment>
      <DataType>int(11)|0s</DataType>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="361" parent="238" name="conversionType">
      <Position>8</Position>
      <Comment>转换类型 Download新下载的 Redownload再次下载</Comment>
      <DataType>varchar(20)|0s</DataType>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="362" parent="238" name="clickDate">
      <Position>9</Position>
      <Comment>点击广告时间</Comment>
      <DataType>datetime|0s</DataType>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="363" parent="238" name="adGroupId">
      <Position>10</Position>
      <Comment>广告组的标识符</Comment>
      <DataType>int(11)|0s</DataType>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="364" parent="238" name="countryOrRegion">
      <Position>11</Position>
      <Comment>活动的国家或地区</Comment>
      <DataType>varchar(20)|0s</DataType>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="365" parent="238" name="keywordId">
      <Position>12</Position>
      <Comment>关键字的唯一标识符</Comment>
      <DataType>int(11)|0s</DataType>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="366" parent="238" name="creativeSetId">
      <Position>13</Position>
      <Comment>创意集id</Comment>
      <DataType>int(11)|0s</DataType>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="367" parent="238" name="activationTime">
      <Position>14</Position>
      <Comment>激活时间</Comment>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="368" parent="238" name="createdOn">
      <Position>15</Position>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <index id="369" parent="238" name="ix_orgId">
      <ColNames>orgId</ColNames>
      <Type>btree</Type>
    </index>
    <index id="370" parent="238" name="ix_campaignId">
      <ColNames>campaignId</ColNames>
      <Type>btree</Type>
    </index>
    <index id="371" parent="238" name="ix_adGroupId">
      <ColNames>adGroupId</ColNames>
      <Type>btree</Type>
    </index>
    <index id="372" parent="238" name="ix_creativeSetId">
      <ColNames>creativeSetId</ColNames>
      <Type>btree</Type>
    </index>
    <key id="373" parent="238" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <ColNames>appId
openId</ColNames>
      <Primary>1</Primary>
    </key>
    <column id="374" parent="239" name="appId">
      <Position>1</Position>
      <Comment>appId</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="375" parent="239" name="openId">
      <Position>2</Position>
      <Comment>openId</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="376" parent="239" name="recordId">
      <Position>3</Position>
      <Comment>apple_promotion_event_record id</Comment>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="377" parent="239" name="campaignId">
      <Position>4</Position>
      <Comment>b站计划Id</Comment>
      <DataType>varchar(50)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="378" parent="239" name="creativeId">
      <Position>5</Position>
      <Comment>b站创意计划Id</Comment>
      <DataType>varchar(50)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="379" parent="239" name="activationTime">
      <Position>6</Position>
      <Comment>激活时间</Comment>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="380" parent="239" name="createdOn">
      <Position>7</Position>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <index id="381" parent="239" name="ix_campaignId">
      <ColNames>campaignId</ColNames>
      <Type>btree</Type>
    </index>
    <index id="382" parent="239" name="ix_creativeId">
      <ColNames>creativeId</ColNames>
      <Type>btree</Type>
    </index>
    <key id="383" parent="239" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <ColNames>appId
openId</ColNames>
      <Primary>1</Primary>
    </key>
    <column id="384" parent="240" name="appId">
      <Position>1</Position>
      <Comment>appId</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="385" parent="240" name="openId">
      <Position>2</Position>
      <Comment>openId</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="386" parent="240" name="recordId">
      <Position>3</Position>
      <Comment>apple_promotion_event_record id</Comment>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="387" parent="240" name="aid">
      <Position>4</Position>
      <Comment>广告组Id</Comment>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="388" parent="240" name="cid">
      <Position>5</Position>
      <Comment>创意组Id</Comment>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="389" parent="240" name="did">
      <Position>6</Position>
      <Comment>广告计划Id</Comment>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="390" parent="240" name="activationTime">
      <Position>7</Position>
      <Comment>激活时间</Comment>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="391" parent="240" name="createdOn">
      <Position>8</Position>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <index id="392" parent="240" name="ix_aid">
      <ColNames>aid</ColNames>
      <Type>btree</Type>
    </index>
    <index id="393" parent="240" name="ix_cid">
      <ColNames>cid</ColNames>
      <Type>btree</Type>
    </index>
    <index id="394" parent="240" name="ix_did">
      <ColNames>did</ColNames>
      <Type>btree</Type>
    </index>
    <key id="395" parent="240" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <ColNames>appId
openId</ColNames>
      <Primary>1</Primary>
    </key>
    <column id="396" parent="241" name="openIdLoginHourStatisticsId">
      <Position>1</Position>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <SequenceIdentity>1</SequenceIdentity>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="397" parent="241" name="appId">
      <Position>2</Position>
      <Comment>appId</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="398" parent="241" name="openId">
      <Position>3</Position>
      <Comment>openId</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="399" parent="241" name="hourStr">
      <Position>4</Position>
      <Comment>日期YYYY-MM-dd HH:00:00</Comment>
      <DataType>char(19)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="400" parent="241" name="times">
      <Position>5</Position>
      <Comment>登录次数</Comment>
      <DataType>int(11)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="401" parent="241" name="totalTime">
      <Position>6</Position>
      <Comment>总时长(秒)</Comment>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="402" parent="241" name="lastOnlineTime">
      <Position>7</Position>
      <Comment>最后在线时间</Comment>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="403" parent="241" name="createdBy">
      <Position>8</Position>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="404" parent="241" name="createdOn">
      <Position>9</Position>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="405" parent="241" name="modifiedBy">
      <Position>10</Position>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="406" parent="241" name="modifiedOn">
      <Position>11</Position>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <index id="407" parent="241" name="ix_appId_openId_hourStr">
      <ColNames>appId
openId
hourStr</ColNames>
      <Unique>1</Unique>
      <Type>btree</Type>
    </index>
    <key id="408" parent="241" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <ColNames>openIdLoginHourStatisticsId</ColNames>
      <Primary>1</Primary>
    </key>
    <key id="409" parent="241" name="ix_appId_openId_hourStr">
      <ColNames>appId
openId
hourStr</ColNames>
      <UnderlyingIndexName>ix_appId_openId_hourStr</UnderlyingIndexName>
    </key>
    <column id="410" parent="242" name="openIdLoginStatisticsId">
      <Position>1</Position>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <SequenceIdentity>1</SequenceIdentity>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="411" parent="242" name="appId">
      <Position>2</Position>
      <Comment>appId</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="412" parent="242" name="openId">
      <Position>3</Position>
      <Comment>openId</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="413" parent="242" name="dateStr">
      <Position>4</Position>
      <Comment>日期YYYY-MM-dd</Comment>
      <DataType>char(10)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="414" parent="242" name="times">
      <Position>5</Position>
      <Comment>当日登录次数</Comment>
      <DataType>int(11)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="415" parent="242" name="totalTime">
      <Position>6</Position>
      <Comment>总时长(秒)</Comment>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="416" parent="242" name="lastOnlineTime">
      <Position>7</Position>
      <Comment>最后在线时间</Comment>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="417" parent="242" name="createdBy">
      <Position>8</Position>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="418" parent="242" name="createdOn">
      <Position>9</Position>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="419" parent="242" name="modifiedBy">
      <Position>10</Position>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="420" parent="242" name="modifiedOn">
      <Position>11</Position>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <index id="421" parent="242" name="ix_appId_openId_dateStr">
      <ColNames>appId
openId
dateStr</ColNames>
      <Unique>1</Unique>
      <Type>btree</Type>
    </index>
    <key id="422" parent="242" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <ColNames>openIdLoginStatisticsId</ColNames>
      <Primary>1</Primary>
    </key>
    <key id="423" parent="242" name="ix_appId_openId_dateStr">
      <ColNames>appId
openId
dateStr</ColNames>
      <UnderlyingIndexName>ix_appId_openId_dateStr</UnderlyingIndexName>
    </key>
    <column id="424" parent="243" name="openIdRechargeHourStatisticsId">
      <Position>1</Position>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <SequenceIdentity>1</SequenceIdentity>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="425" parent="243" name="appId">
      <Position>2</Position>
      <Comment>appId</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="426" parent="243" name="openId">
      <Position>3</Position>
      <Comment>openId</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="427" parent="243" name="hourStr">
      <Position>4</Position>
      <Comment>日期YYYY-MM-dd HH:00:00</Comment>
      <DataType>char(19)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="428" parent="243" name="totalRecharge">
      <Position>5</Position>
      <Comment>总充值金额 单位 元, 订单创建时间计算</Comment>
      <DataType>decimal(18,3 digit)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0.000</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="429" parent="243" name="totalRefund">
      <Position>6</Position>
      <Comment>总退款金额 单位 元, 订单创建时间计算</Comment>
      <DataType>decimal(18,3 digit)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0.000</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="430" parent="243" name="totalAmount">
      <Position>7</Position>
      <Comment>totalRecharge + totalRefund 单位 元</Comment>
      <DataType>decimal(18,3 digit)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0.000</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="431" parent="243" name="rechargeCount">
      <Position>8</Position>
      <Comment>充值次数</Comment>
      <DataType>int(4)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="432" parent="243" name="refundCount">
      <Position>9</Position>
      <Comment>退款次数</Comment>
      <DataType>int(4)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="433" parent="243" name="createdBy">
      <Position>10</Position>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="434" parent="243" name="createdOn">
      <Position>11</Position>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="435" parent="243" name="modifiedBy">
      <Position>12</Position>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="436" parent="243" name="modifiedOn">
      <Position>13</Position>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <index id="437" parent="243" name="ix_appId_openId_hourStr">
      <ColNames>appId
openId
hourStr</ColNames>
      <Unique>1</Unique>
      <Type>btree</Type>
    </index>
    <key id="438" parent="243" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <ColNames>openIdRechargeHourStatisticsId</ColNames>
      <Primary>1</Primary>
    </key>
    <key id="439" parent="243" name="ix_appId_openId_hourStr">
      <ColNames>appId
openId
hourStr</ColNames>
      <UnderlyingIndexName>ix_appId_openId_hourStr</UnderlyingIndexName>
    </key>
    <column id="440" parent="244" name="openIdRechargeStatisticsId">
      <Position>1</Position>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <SequenceIdentity>1</SequenceIdentity>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="441" parent="244" name="appId">
      <Position>2</Position>
      <Comment>appId</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="442" parent="244" name="openId">
      <Position>3</Position>
      <Comment>openId</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="443" parent="244" name="dateStr">
      <Position>4</Position>
      <Comment>日期YYYY-MM-dd</Comment>
      <DataType>char(10)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="444" parent="244" name="totalRecharge">
      <Position>5</Position>
      <Comment>总充值金额 单位 元, 订单创建时间计算</Comment>
      <DataType>decimal(18,3 digit)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0.000</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="445" parent="244" name="totalRefund">
      <Position>6</Position>
      <Comment>总退款金额 单位 元, 订单创建时间计算</Comment>
      <DataType>decimal(18,3 digit)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0.000</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="446" parent="244" name="totalAmount">
      <Position>7</Position>
      <Comment>totalRecharge + totalRefund 单位 元</Comment>
      <DataType>decimal(18,3 digit)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0.000</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="447" parent="244" name="rechargeCount">
      <Position>8</Position>
      <Comment>充值次数</Comment>
      <DataType>int(4)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="448" parent="244" name="refundCount">
      <Position>9</Position>
      <Comment>退款次数</Comment>
      <DataType>int(4)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="449" parent="244" name="createdBy">
      <Position>10</Position>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="450" parent="244" name="createdOn">
      <Position>11</Position>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="451" parent="244" name="modifiedBy">
      <Position>12</Position>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="452" parent="244" name="modifiedOn">
      <Position>13</Position>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <index id="453" parent="244" name="ix_appId_openId_dateStr">
      <ColNames>appId
openId
dateStr</ColNames>
      <Unique>1</Unique>
      <Type>btree</Type>
    </index>
    <key id="454" parent="244" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <ColNames>openIdRechargeStatisticsId</ColNames>
      <Primary>1</Primary>
    </key>
    <key id="455" parent="244" name="ix_appId_openId_dateStr">
      <ColNames>appId
openId
dateStr</ColNames>
      <UnderlyingIndexName>ix_appId_openId_dateStr</UnderlyingIndexName>
    </key>
    <column id="456" parent="245" name="openIdStatisticsId">
      <Position>1</Position>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <SequenceIdentity>1</SequenceIdentity>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="457" parent="245" name="appId">
      <Position>2</Position>
      <Comment>appId</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="458" parent="245" name="openId">
      <Position>3</Position>
      <Comment>openId</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="459" parent="245" name="uuid">
      <Position>4</Position>
      <Comment>uuid</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="460" parent="245" name="registerType">
      <Position>5</Position>
      <Comment>注册类型 0手机注册, 1游客 </Comment>
      <DataType>int(4)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="461" parent="245" name="channel">
      <Position>6</Position>
      <Comment>渠道 1快手 2抖音 2bilibili 3apple</Comment>
      <DataType>int(4)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="462" parent="245" name="bmcType">
      <Position>7</Position>
      <Comment>bmc类型 0:boom 1:oppo</Comment>
      <DataType>int(4)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="463" parent="245" name="phoneType">
      <Position>8</Position>
      <Comment>设备类型1安卓 2ios2 3pc</Comment>
      <DataType>tinyint(4)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="464" parent="245" name="sdkVersion">
      <Position>9</Position>
      <Comment>sdk版本</Comment>
      <DataType>varchar(20)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="465" parent="245" name="source">
      <Position>10</Position>
      <Comment>注册数据上报的渠道</Comment>
      <DataType>varchar(32)|0s</DataType>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="466" parent="245" name="model">
      <Position>11</Position>
      <Comment>注册数据上报的手机型号</Comment>
      <DataType>varchar(32)|0s</DataType>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="467" parent="245" name="version">
      <Position>12</Position>
      <Comment>注册数据上报的系统版本</Comment>
      <DataType>varchar(255)|0s</DataType>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="468" parent="245" name="registerTime">
      <Position>13</Position>
      <Comment>注册时间</Comment>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="469" parent="245" name="old">
      <Position>14</Position>
      <Comment>老数据标记,0新数据,1老数据</Comment>
      <DataType>tinyint(2)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="470" parent="245" name="status">
      <Position>15</Position>
      <Comment>状态 0默认</Comment>
      <DataType>int(4)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="471" parent="245" name="createdBy">
      <Position>16</Position>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="472" parent="245" name="createdOn">
      <Position>17</Position>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="473" parent="245" name="modifiedBy">
      <Position>18</Position>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="474" parent="245" name="modifiedOn">
      <Position>19</Position>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <index id="475" parent="245" name="ix_appId_openId">
      <ColNames>appId
openId</ColNames>
      <Unique>1</Unique>
      <Type>btree</Type>
    </index>
    <index id="476" parent="245" name="ix_channel">
      <ColNames>channel</ColNames>
      <Type>btree</Type>
    </index>
    <index id="477" parent="245" name="ix_bmcType">
      <ColNames>bmcType</ColNames>
      <Type>btree</Type>
    </index>
    <index id="478" parent="245" name="ix_phoneType">
      <ColNames>phoneType</ColNames>
      <Type>btree</Type>
    </index>
    <index id="479" parent="245" name="ix_registerTime">
      <ColNames>registerTime</ColNames>
      <Type>btree</Type>
    </index>
    <key id="480" parent="245" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <ColNames>openIdStatisticsId</ColNames>
      <Primary>1</Primary>
    </key>
    <key id="481" parent="245" name="ix_appId_openId">
      <ColNames>appId
openId</ColNames>
      <UnderlyingIndexName>ix_appId_openId</UnderlyingIndexName>
    </key>
    <column id="482" parent="246" name="appId">
      <Position>1</Position>
      <Comment>appId</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="483" parent="246" name="openId">
      <Position>2</Position>
      <Comment>openId</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="484" parent="246" name="recordId">
      <Position>3</Position>
      <Comment>apple_promotion_event_record id</Comment>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="485" parent="246" name="aid">
      <Position>4</Position>
      <Comment>广告计划Id</Comment>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="486" parent="246" name="campaignId">
      <Position>5</Position>
      <Comment>广告组Id</Comment>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="487" parent="246" name="cid">
      <Position>6</Position>
      <Comment>创意组Id</Comment>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="488" parent="246" name="csite">
      <Position>7</Position>
      <Comment>广告投放位置1:今日头条 10001:西瓜视频 30001:火山小视频 40001:抖音</Comment>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="489" parent="246" name="convertId">
      <Position>8</Position>
      <Comment>转化id</Comment>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="490" parent="246" name="activationTime">
      <Position>9</Position>
      <Comment>激活时间</Comment>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="491" parent="246" name="createdOn">
      <Position>10</Position>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <index id="492" parent="246" name="ix_aid">
      <ColNames>aid</ColNames>
      <Type>btree</Type>
    </index>
    <index id="493" parent="246" name="ix_campaignId">
      <ColNames>campaignId</ColNames>
      <Type>btree</Type>
    </index>
    <index id="494" parent="246" name="ix_cid">
      <ColNames>cid</ColNames>
      <Type>btree</Type>
    </index>
    <index id="495" parent="246" name="ix_convertId">
      <ColNames>convertId</ColNames>
      <Type>btree</Type>
    </index>
    <key id="496" parent="246" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <ColNames>appId
openId</ColNames>
      <Primary>1</Primary>
    </key>
    <column id="497" parent="247" name="appId">
      <Position>1</Position>
      <Comment>appId</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="498" parent="247" name="openId">
      <Position>2</Position>
      <Comment>openId</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="499" parent="247" name="transmissionRecordId">
      <Position>3</Position>
      <Comment>tiktok_transmission_callback_record id</Comment>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="500" parent="247" name="demandId">
      <Position>4</Position>
      <Comment>计划ID，一次营销活动生成一个计划ID</Comment>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="501" parent="247" name="orderId">
      <Position>5</Position>
      <Comment>任务ID，标识一次达人投稿活动</Comment>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="502" parent="247" name="itemId">
      <Position>6</Position>
      <Comment>视频ID，与星图平台前端video_url中展现的视频id一致</Comment>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="503" parent="247" name="activationTime">
      <Position>7</Position>
      <Comment>激活时间</Comment>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="504" parent="247" name="createdOn">
      <Position>8</Position>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <index id="505" parent="247" name="ix_demandId">
      <ColNames>demandId</ColNames>
      <Type>btree</Type>
    </index>
    <index id="506" parent="247" name="ix_orderId">
      <ColNames>orderId</ColNames>
      <Type>btree</Type>
    </index>
    <index id="507" parent="247" name="ix_citemId">
      <ColNames>itemId</ColNames>
      <Type>btree</Type>
    </index>
    <key id="508" parent="247" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <ColNames>appId
openId</ColNames>
      <Primary>1</Primary>
    </key>
    <column id="509" parent="248" name="appId">
      <Position>1</Position>
      <Comment>appId</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="510" parent="248" name="openId">
      <Position>2</Position>
      <Comment>openId</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="511" parent="248" name="recordId">
      <Position>3</Position>
      <Comment>apple_promotion_event_record id</Comment>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="512" parent="248" name="campaignId">
      <Position>4</Position>
      <Comment>广告主自定义的监测ID</Comment>
      <DataType>varchar(255)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="513" parent="248" name="customerId">
      <Position>5</Position>
      <Comment>广告主账号</Comment>
      <DataType>varchar(255)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="514" parent="248" name="wbCampaignId">
      <Position>6</Position>
      <Comment>系列ID</Comment>
      <DataType>varchar(255)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="515" parent="248" name="adId">
      <Position>7</Position>
      <Comment>计划ID</Comment>
      <DataType>varchar(255)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="516" parent="248" name="creativeId">
      <Position>8</Position>
      <Comment>创意ID</Comment>
      <DataType>varchar(255)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="517" parent="248" name="activationTime">
      <Position>9</Position>
      <Comment>激活时间</Comment>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="518" parent="248" name="createdOn">
      <Position>10</Position>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <index id="519" parent="248" name="ix_campaignId">
      <ColNames>campaignId</ColNames>
      <Type>btree</Type>
    </index>
    <index id="520" parent="248" name="ix_customerId">
      <ColNames>customerId</ColNames>
      <Type>btree</Type>
    </index>
    <index id="521" parent="248" name="ix_adId">
      <ColNames>adId</ColNames>
      <Type>btree</Type>
    </index>
    <index id="522" parent="248" name="ix_creativeId">
      <ColNames>creativeId</ColNames>
      <Type>btree</Type>
    </index>
    <key id="523" parent="248" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <ColNames>appId
openId</ColNames>
      <Primary>1</Primary>
    </key>
    <column id="524" parent="249" name="feedbackId">
      <Position>1</Position>
      <Comment>不重复的单号，时间戳+随机数（四位）</Comment>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="525" parent="249" name="appId">
      <Position>2</Position>
      <Comment>appId</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="526" parent="249" name="openId">
      <Position>3</Position>
      <Comment>openId</Comment>
      <DataType>varchar(32)|0s</DataType>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="527" parent="249" name="userId">
      <Position>4</Position>
      <Comment>userId</Comment>
      <DataType>bigint(11)|0s</DataType>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="528" parent="249" name="sdkVersion">
      <Position>5</Position>
      <Comment>sdk版本</Comment>
      <DataType>varchar(20)|0s</DataType>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="529" parent="249" name="uuid">
      <Position>6</Position>
      <Comment>设备Id</Comment>
      <DataType>varchar(100)|0s</DataType>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="530" parent="249" name="phoneType">
      <Position>7</Position>
      <Comment>安卓1 ios 2</Comment>
      <DataType>tinyint(4)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="531" parent="249" name="bmcType">
      <Position>8</Position>
      <Comment>bmc类型 0:boom 1:oppo</Comment>
      <DataType>int(4)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="532" parent="249" name="issueType">
      <Position>9</Position>
      <Comment>问题类型 1意见反馈,2产品BUG,3性能问题,4登录问题,5充值问题,6其他问题</Comment>
      <DataType>int(4)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="533" parent="249" name="issueTime">
      <Position>10</Position>
      <Comment>问题时间</Comment>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="534" parent="249" name="issue">
      <Position>11</Position>
      <Comment>问题描述</Comment>
      <DataType>text|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="535" parent="249" name="logFile">
      <Position>12</Position>
      <Comment>错误日志文件名</Comment>
      <DataType>varchar(255)|0s</DataType>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="536" parent="249" name="contact">
      <Position>13</Position>
      <Comment>联系方式</Comment>
      <DataType>varchar(50)|0s</DataType>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="537" parent="249" name="userIp">
      <Position>14</Position>
      <Comment>用户ip</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="538" parent="249" name="model">
      <Position>15</Position>
      <Comment>手机型号</Comment>
      <DataType>varchar(32)|0s</DataType>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="539" parent="249" name="version">
      <Position>16</Position>
      <Comment>系统版本</Comment>
      <DataType>varchar(255)|0s</DataType>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="540" parent="249" name="resolution">
      <Position>17</Position>
      <Comment>分辨率</Comment>
      <DataType>varchar(10)|0s</DataType>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="541" parent="249" name="netType">
      <Position>18</Position>
      <Comment>上网方式</Comment>
      <DataType>varchar(255)|0s</DataType>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="542" parent="249" name="language">
      <Position>19</Position>
      <Comment>系统语言</Comment>
      <DataType>varchar(20)|0s</DataType>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="543" parent="249" name="reply">
      <Position>20</Position>
      <Comment>0 未回复 1 已回复</Comment>
      <DataType>tinyint(2)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="544" parent="249" name="createdBy">
      <Position>21</Position>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="545" parent="249" name="createdOn">
      <Position>22</Position>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <index id="546" parent="249" name="ix_appId_openId">
      <ColNames>appId
openId</ColNames>
      <Type>btree</Type>
    </index>
    <index id="547" parent="249" name="ix_userId">
      <ColNames>userId</ColNames>
      <Type>btree</Type>
    </index>
    <key id="548" parent="249" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <ColNames>feedbackId</ColNames>
      <Primary>1</Primary>
    </key>
    <column id="549" parent="250" name="feedbackId">
      <Position>1</Position>
      <Comment>反馈id</Comment>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="550" parent="250" name="reply">
      <Position>2</Position>
      <Comment>截图文件名</Comment>
      <DataType>text|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="551" parent="250" name="replyBotId">
      <Position>3</Position>
      <Comment>回复机器人</Comment>
      <DataType>int(4)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="552" parent="250" name="createdBy">
      <Position>4</Position>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="553" parent="250" name="createdOn">
      <Position>5</Position>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="554" parent="250" name="modifiedBy">
      <Position>6</Position>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="555" parent="250" name="modifiedOn">
      <Position>7</Position>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <key id="556" parent="250" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <ColNames>feedbackId</ColNames>
      <Primary>1</Primary>
    </key>
    <column id="557" parent="251" name="screenshotId">
      <Position>1</Position>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <SequenceIdentity>1</SequenceIdentity>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="558" parent="251" name="feedbackId">
      <Position>2</Position>
      <Comment>反馈id</Comment>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="559" parent="251" name="screenshot">
      <Position>3</Position>
      <Comment>截图文件名</Comment>
      <DataType>varchar(255)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="560" parent="251" name="createdBy">
      <Position>4</Position>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="561" parent="251" name="createdOn">
      <Position>5</Position>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <index id="562" parent="251" name="feedbackId">
      <ColNames>feedbackId</ColNames>
      <Type>btree</Type>
    </index>
    <key id="563" parent="251" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <ColNames>screenshotId</ColNames>
      <Primary>1</Primary>
    </key>
    <column id="564" parent="252" name="appId">
      <Position>1</Position>
      <Comment>appId</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="565" parent="252" name="uuid">
      <Position>2</Position>
      <Comment>uuid</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="566" parent="252" name="recordId">
      <Position>3</Position>
      <Comment>apple_promotion_event_record id</Comment>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="567" parent="252" name="repeatDevice">
      <Position>4</Position>
      <Comment>idfa 或 uuid 重复</Comment>
      <DataType>tinyint(2)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="568" parent="252" name="attribution">
      <Position>5</Position>
      <Comment>归因值</Comment>
      <DataType>tinyint(4)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="569" parent="252" name="orgId">
      <Position>6</Position>
      <Comment>拥有活动的组织的标识符</Comment>
      <DataType>int(11)|0s</DataType>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="570" parent="252" name="campaignId">
      <Position>7</Position>
      <Comment>活动的唯一标识符</Comment>
      <DataType>int(11)|0s</DataType>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="571" parent="252" name="conversionType">
      <Position>8</Position>
      <Comment>转换类型 Download新下载的 Redownload再次下载</Comment>
      <DataType>varchar(20)|0s</DataType>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="572" parent="252" name="clickDate">
      <Position>9</Position>
      <Comment>点击广告时间</Comment>
      <DataType>datetime|0s</DataType>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="573" parent="252" name="adGroupId">
      <Position>10</Position>
      <Comment>广告组的标识符</Comment>
      <DataType>int(11)|0s</DataType>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="574" parent="252" name="countryOrRegion">
      <Position>11</Position>
      <Comment>活动的国家或地区</Comment>
      <DataType>varchar(20)|0s</DataType>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="575" parent="252" name="keywordId">
      <Position>12</Position>
      <Comment>关键字的唯一标识符</Comment>
      <DataType>int(11)|0s</DataType>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="576" parent="252" name="creativeSetId">
      <Position>13</Position>
      <Comment>创意集id</Comment>
      <DataType>int(11)|0s</DataType>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="577" parent="252" name="activationTime">
      <Position>14</Position>
      <Comment>激活时间</Comment>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="578" parent="252" name="createdOn">
      <Position>15</Position>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <index id="579" parent="252" name="ix_orgId">
      <ColNames>orgId</ColNames>
      <Type>btree</Type>
    </index>
    <index id="580" parent="252" name="ix_campaignId">
      <ColNames>campaignId</ColNames>
      <Type>btree</Type>
    </index>
    <index id="581" parent="252" name="ix_adGroupId">
      <ColNames>adGroupId</ColNames>
      <Type>btree</Type>
    </index>
    <index id="582" parent="252" name="ix_creativeSetId">
      <ColNames>creativeSetId</ColNames>
      <Type>btree</Type>
    </index>
    <key id="583" parent="252" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <ColNames>appId
uuid</ColNames>
      <Primary>1</Primary>
    </key>
    <column id="584" parent="253" name="appId">
      <Position>1</Position>
      <Comment>appId</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="585" parent="253" name="uuid">
      <Position>2</Position>
      <Comment>uuid</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="586" parent="253" name="recordId">
      <Position>3</Position>
      <Comment>apple_promotion_event_record id</Comment>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="587" parent="253" name="campaignId">
      <Position>4</Position>
      <Comment>b站计划Id</Comment>
      <DataType>varchar(50)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="588" parent="253" name="creativeId">
      <Position>5</Position>
      <Comment>b站创意计划Id</Comment>
      <DataType>varchar(50)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="589" parent="253" name="activationTime">
      <Position>6</Position>
      <Comment>激活时间</Comment>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="590" parent="253" name="createdOn">
      <Position>7</Position>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <index id="591" parent="253" name="ix_campaignId">
      <ColNames>campaignId</ColNames>
      <Type>btree</Type>
    </index>
    <index id="592" parent="253" name="ix_creativeId">
      <ColNames>creativeId</ColNames>
      <Type>btree</Type>
    </index>
    <key id="593" parent="253" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <ColNames>appId
uuid</ColNames>
      <Primary>1</Primary>
    </key>
    <column id="594" parent="254" name="appId">
      <Position>1</Position>
      <Comment>appId</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="595" parent="254" name="uuid">
      <Position>2</Position>
      <Comment>设备uuid</Comment>
      <DataType>varchar(64)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="596" parent="254" name="registerOpenId">
      <Position>3</Position>
      <Comment>注册转换的openId</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="597" parent="254" name="registerConvertDays">
      <Position>4</Position>
      <Comment>注册转换openId距离uuid创建天数</Comment>
      <DataType>int(11)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>-1</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="598" parent="254" name="registerConvertOn">
      <Position>5</Position>
      <Comment>openId转换日期</Comment>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="599" parent="254" name="loginOpenId">
      <Position>6</Position>
      <Comment>登录转换的openId</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="600" parent="254" name="loginConvertDays">
      <Position>7</Position>
      <Comment>登录转换openId距离uuid创建天数</Comment>
      <DataType>int(11)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>-1</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="601" parent="254" name="loginConvertOn">
      <Position>8</Position>
      <Comment>openId转换日期</Comment>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="602" parent="254" name="dataRegisterOpenId">
      <Position>9</Position>
      <Comment>数据上报注册转换的openId</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="603" parent="254" name="dataRegisterConvertDays">
      <Position>10</Position>
      <Comment>数据上报注册转换openId距离uuid创建天数</Comment>
      <DataType>int(11)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>-1</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="604" parent="254" name="dataRegisterConvertOn">
      <Position>11</Position>
      <Comment>openId转换日期</Comment>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="605" parent="254" name="dataLoginOpenId">
      <Position>12</Position>
      <Comment>数据上报登录转换的openId</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="606" parent="254" name="dataLoginConvertDays">
      <Position>13</Position>
      <Comment>数据上报登录转换openId距离uuid创建天数</Comment>
      <DataType>int(11)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>-1</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="607" parent="254" name="dataLoginConvertOn">
      <Position>14</Position>
      <Comment>openId转换日期</Comment>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="608" parent="254" name="createdBy">
      <Position>15</Position>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="609" parent="254" name="createdOn">
      <Position>16</Position>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="610" parent="254" name="modifiedBy">
      <Position>17</Position>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="611" parent="254" name="modifiedOn">
      <Position>18</Position>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <index id="612" parent="254" name="ix_registerConvertDays">
      <ColNames>registerConvertDays</ColNames>
      <Type>btree</Type>
    </index>
    <index id="613" parent="254" name="ix_loginConvertDays">
      <ColNames>loginConvertDays</ColNames>
      <Type>btree</Type>
    </index>
    <index id="614" parent="254" name="ix_dataRegisterConvertDays">
      <ColNames>dataRegisterConvertDays</ColNames>
      <Type>btree</Type>
    </index>
    <index id="615" parent="254" name="ix_dataLoginConvertDays">
      <ColNames>dataLoginConvertDays</ColNames>
      <Type>btree</Type>
    </index>
    <key id="616" parent="254" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <ColNames>appId
uuid</ColNames>
      <Primary>1</Primary>
    </key>
    <column id="617" parent="255" name="appId">
      <Position>1</Position>
      <Comment>appId</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="618" parent="255" name="uuid">
      <Position>2</Position>
      <Comment>uuid</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="619" parent="255" name="recordId">
      <Position>3</Position>
      <Comment>apple_promotion_event_record id</Comment>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="620" parent="255" name="aid">
      <Position>4</Position>
      <Comment>广告组Id</Comment>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="621" parent="255" name="cid">
      <Position>5</Position>
      <Comment>创意组Id</Comment>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="622" parent="255" name="did">
      <Position>6</Position>
      <Comment>广告计划Id</Comment>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="623" parent="255" name="activationTime">
      <Position>7</Position>
      <Comment>激活时间</Comment>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="624" parent="255" name="createdOn">
      <Position>8</Position>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <index id="625" parent="255" name="ix_aid">
      <ColNames>aid</ColNames>
      <Type>btree</Type>
    </index>
    <index id="626" parent="255" name="ix_cid">
      <ColNames>cid</ColNames>
      <Type>btree</Type>
    </index>
    <index id="627" parent="255" name="ix_did">
      <ColNames>did</ColNames>
      <Type>btree</Type>
    </index>
    <key id="628" parent="255" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <ColNames>appId
uuid</ColNames>
      <Primary>1</Primary>
    </key>
    <column id="629" parent="256" name="uuidLoginHourStatisticsId">
      <Position>1</Position>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <SequenceIdentity>1</SequenceIdentity>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="630" parent="256" name="appId">
      <Position>2</Position>
      <Comment>appId</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="631" parent="256" name="uuid">
      <Position>3</Position>
      <Comment>uuid</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="632" parent="256" name="hourStr">
      <Position>4</Position>
      <Comment>日期YYYY-MM-dd HH:00:00</Comment>
      <DataType>char(19)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="633" parent="256" name="times">
      <Position>5</Position>
      <Comment>当日登录次数</Comment>
      <DataType>int(11)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="634" parent="256" name="totalTime">
      <Position>6</Position>
      <Comment>总时长(秒)</Comment>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="635" parent="256" name="lastOnlineTime">
      <Position>7</Position>
      <Comment>最后在线时间</Comment>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="636" parent="256" name="createdBy">
      <Position>8</Position>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="637" parent="256" name="createdOn">
      <Position>9</Position>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="638" parent="256" name="modifiedBy">
      <Position>10</Position>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="639" parent="256" name="modifiedOn">
      <Position>11</Position>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <index id="640" parent="256" name="ix_appId_uuid_hourStr">
      <ColNames>appId
uuid
hourStr</ColNames>
      <Unique>1</Unique>
      <Type>btree</Type>
    </index>
    <key id="641" parent="256" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <ColNames>uuidLoginHourStatisticsId</ColNames>
      <Primary>1</Primary>
    </key>
    <key id="642" parent="256" name="ix_appId_uuid_hourStr">
      <ColNames>appId
uuid
hourStr</ColNames>
      <UnderlyingIndexName>ix_appId_uuid_hourStr</UnderlyingIndexName>
    </key>
    <column id="643" parent="257" name="uuidLoginStatisticsId">
      <Position>1</Position>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <SequenceIdentity>1</SequenceIdentity>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="644" parent="257" name="appId">
      <Position>2</Position>
      <Comment>appId</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="645" parent="257" name="uuid">
      <Position>3</Position>
      <Comment>uuid</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="646" parent="257" name="dateStr">
      <Position>4</Position>
      <Comment>日期YYYY-MM-dd</Comment>
      <DataType>char(10)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="647" parent="257" name="times">
      <Position>5</Position>
      <Comment>当日登录次数</Comment>
      <DataType>int(11)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="648" parent="257" name="totalTime">
      <Position>6</Position>
      <Comment>总时长(秒)</Comment>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="649" parent="257" name="lastOnlineTime">
      <Position>7</Position>
      <Comment>最后在线时间</Comment>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="650" parent="257" name="createdBy">
      <Position>8</Position>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="651" parent="257" name="createdOn">
      <Position>9</Position>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="652" parent="257" name="modifiedBy">
      <Position>10</Position>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="653" parent="257" name="modifiedOn">
      <Position>11</Position>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <index id="654" parent="257" name="ix_appId_uuid_dateStr">
      <ColNames>appId
uuid
dateStr</ColNames>
      <Unique>1</Unique>
      <Type>btree</Type>
    </index>
    <key id="655" parent="257" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <ColNames>uuidLoginStatisticsId</ColNames>
      <Primary>1</Primary>
    </key>
    <key id="656" parent="257" name="ix_appId_uuid_dateStr">
      <ColNames>appId
uuid
dateStr</ColNames>
      <UnderlyingIndexName>ix_appId_uuid_dateStr</UnderlyingIndexName>
    </key>
    <column id="657" parent="258" name="uuidRechargeHourStatisticsId">
      <Position>1</Position>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <SequenceIdentity>1</SequenceIdentity>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="658" parent="258" name="appId">
      <Position>2</Position>
      <Comment>appId</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="659" parent="258" name="uuid">
      <Position>3</Position>
      <Comment>uuid</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="660" parent="258" name="hourStr">
      <Position>4</Position>
      <Comment>日期YYYY-MM-dd HH:00:00</Comment>
      <DataType>char(19)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="661" parent="258" name="totalRecharge">
      <Position>5</Position>
      <Comment>总充值金额 单位 元, 订单创建时间计算</Comment>
      <DataType>decimal(18,3 digit)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0.000</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="662" parent="258" name="totalRefund">
      <Position>6</Position>
      <Comment>总退款金额 单位 元, 订单创建时间计算</Comment>
      <DataType>decimal(18,3 digit)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0.000</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="663" parent="258" name="totalAmount">
      <Position>7</Position>
      <Comment>totalRecharge + totalRefund 单位 元</Comment>
      <DataType>decimal(18,3 digit)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0.000</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="664" parent="258" name="rechargeCount">
      <Position>8</Position>
      <Comment>充值次数</Comment>
      <DataType>int(4)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="665" parent="258" name="refundCount">
      <Position>9</Position>
      <Comment>退款次数</Comment>
      <DataType>int(4)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="666" parent="258" name="createdBy">
      <Position>10</Position>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="667" parent="258" name="createdOn">
      <Position>11</Position>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="668" parent="258" name="modifiedBy">
      <Position>12</Position>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="669" parent="258" name="modifiedOn">
      <Position>13</Position>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <index id="670" parent="258" name="ix_appId_openId_hourStr">
      <ColNames>appId
uuid
hourStr</ColNames>
      <Unique>1</Unique>
      <Type>btree</Type>
    </index>
    <key id="671" parent="258" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <ColNames>uuidRechargeHourStatisticsId</ColNames>
      <Primary>1</Primary>
    </key>
    <key id="672" parent="258" name="ix_appId_openId_hourStr">
      <ColNames>appId
uuid
hourStr</ColNames>
      <UnderlyingIndexName>ix_appId_openId_hourStr</UnderlyingIndexName>
    </key>
    <column id="673" parent="259" name="uuidRechargeStatisticsId">
      <Position>1</Position>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <SequenceIdentity>1</SequenceIdentity>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="674" parent="259" name="appId">
      <Position>2</Position>
      <Comment>appId</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="675" parent="259" name="uuid">
      <Position>3</Position>
      <Comment>uuid</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="676" parent="259" name="dateStr">
      <Position>4</Position>
      <Comment>日期YYYY-MM-dd</Comment>
      <DataType>char(10)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="677" parent="259" name="totalRecharge">
      <Position>5</Position>
      <Comment>总充值金额 单位 元, 订单创建时间计算</Comment>
      <DataType>decimal(18,3 digit)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0.000</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="678" parent="259" name="totalRefund">
      <Position>6</Position>
      <Comment>总退款金额 单位 元, 订单创建时间计算</Comment>
      <DataType>decimal(18,3 digit)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0.000</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="679" parent="259" name="totalAmount">
      <Position>7</Position>
      <Comment>totalRecharge + totalRefund 单位 元</Comment>
      <DataType>decimal(18,3 digit)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0.000</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="680" parent="259" name="rechargeCount">
      <Position>8</Position>
      <Comment>充值次数</Comment>
      <DataType>int(4)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="681" parent="259" name="refundCount">
      <Position>9</Position>
      <Comment>退款次数</Comment>
      <DataType>int(4)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="682" parent="259" name="createdBy">
      <Position>10</Position>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="683" parent="259" name="createdOn">
      <Position>11</Position>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="684" parent="259" name="modifiedBy">
      <Position>12</Position>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="685" parent="259" name="modifiedOn">
      <Position>13</Position>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <index id="686" parent="259" name="ix_appId_uuid_dateStr">
      <ColNames>appId
uuid
dateStr</ColNames>
      <Unique>1</Unique>
      <Type>btree</Type>
    </index>
    <key id="687" parent="259" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <ColNames>uuidRechargeStatisticsId</ColNames>
      <Primary>1</Primary>
    </key>
    <key id="688" parent="259" name="ix_appId_uuid_dateStr">
      <ColNames>appId
uuid
dateStr</ColNames>
      <UnderlyingIndexName>ix_appId_uuid_dateStr</UnderlyingIndexName>
    </key>
    <column id="689" parent="260" name="uuidStatisticsId">
      <Position>1</Position>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <SequenceIdentity>1</SequenceIdentity>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="690" parent="260" name="appId">
      <Position>2</Position>
      <Comment>appId</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="691" parent="260" name="uuid">
      <Position>3</Position>
      <Comment>设备uuid</Comment>
      <DataType>varchar(64)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="692" parent="260" name="channel">
      <Position>4</Position>
      <Comment>渠道 1快手 2抖音 2bilibili 3apple</Comment>
      <DataType>int(4)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="693" parent="260" name="bmcType">
      <Position>5</Position>
      <Comment>bmc类型 0:boom 1:oppo</Comment>
      <DataType>int(4)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="694" parent="260" name="phoneType">
      <Position>6</Position>
      <Comment>设备类型1安卓 2ios2 3pc</Comment>
      <DataType>tinyint(4)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="695" parent="260" name="sdkVersion">
      <Position>7</Position>
      <Comment>sdk版本</Comment>
      <DataType>varchar(20)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="696" parent="260" name="source">
      <Position>8</Position>
      <Comment>注册数据上报的渠道</Comment>
      <DataType>varchar(32)|0s</DataType>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="697" parent="260" name="model">
      <Position>9</Position>
      <Comment>注册数据上报的手机型号</Comment>
      <DataType>varchar(32)|0s</DataType>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="698" parent="260" name="version">
      <Position>10</Position>
      <Comment>注册数据上报的系统版本</Comment>
      <DataType>varchar(255)|0s</DataType>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="699" parent="260" name="registerTime">
      <Position>11</Position>
      <Comment>注册时间</Comment>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="700" parent="260" name="old">
      <Position>12</Position>
      <Comment>老数据标记,0新数据,1老数据</Comment>
      <DataType>tinyint(2)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="701" parent="260" name="status">
      <Position>13</Position>
      <Comment>状态 0默认</Comment>
      <DataType>int(4)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="702" parent="260" name="createdBy">
      <Position>14</Position>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="703" parent="260" name="createdOn">
      <Position>15</Position>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="704" parent="260" name="modifiedBy">
      <Position>16</Position>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="705" parent="260" name="modifiedOn">
      <Position>17</Position>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <index id="706" parent="260" name="ix_appId_uuid">
      <ColNames>appId
uuid</ColNames>
      <Unique>1</Unique>
      <Type>btree</Type>
    </index>
    <index id="707" parent="260" name="ix_channel">
      <ColNames>channel</ColNames>
      <Type>btree</Type>
    </index>
    <index id="708" parent="260" name="ix_bmcType">
      <ColNames>bmcType</ColNames>
      <Type>btree</Type>
    </index>
    <index id="709" parent="260" name="ix_phoneType">
      <ColNames>phoneType</ColNames>
      <Type>btree</Type>
    </index>
    <index id="710" parent="260" name="ix_registerTime">
      <ColNames>registerTime</ColNames>
      <Type>btree</Type>
    </index>
    <key id="711" parent="260" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <ColNames>uuidStatisticsId</ColNames>
      <Primary>1</Primary>
    </key>
    <key id="712" parent="260" name="ix_appId_uuid">
      <ColNames>appId
uuid</ColNames>
      <UnderlyingIndexName>ix_appId_uuid</UnderlyingIndexName>
    </key>
    <column id="713" parent="261" name="appId">
      <Position>1</Position>
      <Comment>appId</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="714" parent="261" name="uuid">
      <Position>2</Position>
      <Comment>uuid</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="715" parent="261" name="recordId">
      <Position>3</Position>
      <Comment>apple_promotion_event_record id</Comment>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="716" parent="261" name="aid">
      <Position>4</Position>
      <Comment>广告计划Id</Comment>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="717" parent="261" name="campaignId">
      <Position>5</Position>
      <Comment>广告组Id</Comment>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="718" parent="261" name="cid">
      <Position>6</Position>
      <Comment>创意组Id</Comment>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="719" parent="261" name="csite">
      <Position>7</Position>
      <Comment>广告投放位置1:今日头条 10001:西瓜视频 30001:火山小视频 40001:抖音</Comment>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="720" parent="261" name="convertId">
      <Position>8</Position>
      <Comment>转化id</Comment>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="721" parent="261" name="activationTime">
      <Position>9</Position>
      <Comment>激活时间</Comment>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="722" parent="261" name="createdOn">
      <Position>10</Position>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <index id="723" parent="261" name="ix_aid">
      <ColNames>aid</ColNames>
      <Type>btree</Type>
    </index>
    <index id="724" parent="261" name="ix_campaignId">
      <ColNames>campaignId</ColNames>
      <Type>btree</Type>
    </index>
    <index id="725" parent="261" name="ix_cid">
      <ColNames>cid</ColNames>
      <Type>btree</Type>
    </index>
    <index id="726" parent="261" name="ix_convertId">
      <ColNames>convertId</ColNames>
      <Type>btree</Type>
    </index>
    <key id="727" parent="261" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <ColNames>appId
uuid</ColNames>
      <Primary>1</Primary>
    </key>
    <column id="728" parent="262" name="appId">
      <Position>1</Position>
      <Comment>appId</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="729" parent="262" name="uuid">
      <Position>2</Position>
      <Comment>uuid</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="730" parent="262" name="transmissionRecordId">
      <Position>3</Position>
      <Comment>tiktok_transmission_callback_record id</Comment>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="731" parent="262" name="demandId">
      <Position>4</Position>
      <Comment>计划ID，一次营销活动生成一个计划ID</Comment>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="732" parent="262" name="orderId">
      <Position>5</Position>
      <Comment>任务ID，标识一次达人投稿活动</Comment>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="733" parent="262" name="itemId">
      <Position>6</Position>
      <Comment>视频ID，与星图平台前端video_url中展现的视频id一致</Comment>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="734" parent="262" name="activationTime">
      <Position>7</Position>
      <Comment>激活时间</Comment>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="735" parent="262" name="createdOn">
      <Position>8</Position>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <index id="736" parent="262" name="ix_demandId">
      <ColNames>demandId</ColNames>
      <Type>btree</Type>
    </index>
    <index id="737" parent="262" name="ix_orderId">
      <ColNames>orderId</ColNames>
      <Type>btree</Type>
    </index>
    <index id="738" parent="262" name="ix_citemId">
      <ColNames>itemId</ColNames>
      <Type>btree</Type>
    </index>
    <key id="739" parent="262" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <ColNames>appId
uuid</ColNames>
      <Primary>1</Primary>
    </key>
    <column id="740" parent="263" name="appId">
      <Position>1</Position>
      <Comment>appId</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="741" parent="263" name="uuid">
      <Position>2</Position>
      <Comment>uuid</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="742" parent="263" name="recordId">
      <Position>3</Position>
      <Comment>apple_promotion_event_record id</Comment>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="743" parent="263" name="campaignId">
      <Position>4</Position>
      <Comment>广告主自定义的监测ID</Comment>
      <DataType>varchar(255)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="744" parent="263" name="customerId">
      <Position>5</Position>
      <Comment>广告主账号</Comment>
      <DataType>varchar(255)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="745" parent="263" name="wbCampaignId">
      <Position>6</Position>
      <Comment>系列ID</Comment>
      <DataType>varchar(255)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="746" parent="263" name="adId">
      <Position>7</Position>
      <Comment>计划ID</Comment>
      <DataType>varchar(255)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="747" parent="263" name="creativeId">
      <Position>8</Position>
      <Comment>创意ID</Comment>
      <DataType>varchar(255)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="748" parent="263" name="activationTime">
      <Position>9</Position>
      <Comment>激活时间</Comment>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="749" parent="263" name="createdOn">
      <Position>10</Position>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <index id="750" parent="263" name="ix_campaignId">
      <ColNames>campaignId</ColNames>
      <Type>btree</Type>
    </index>
    <index id="751" parent="263" name="ix_customerId">
      <ColNames>customerId</ColNames>
      <Type>btree</Type>
    </index>
    <index id="752" parent="263" name="ix_adId">
      <ColNames>adId</ColNames>
      <Type>btree</Type>
    </index>
    <index id="753" parent="263" name="ix_creativeId">
      <ColNames>creativeId</ColNames>
      <Type>btree</Type>
    </index>
    <key id="754" parent="263" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <ColNames>appId
uuid</ColNames>
      <Primary>1</Primary>
    </key>
    <column id="755" parent="264" name="yidunDeviceStatisticsId">
      <Position>1</Position>
      <DataType>bigint(11)|0s</DataType>
      <NotNull>1</NotNull>
      <SequenceIdentity>1</SequenceIdentity>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="756" parent="264" name="businessType">
      <Position>2</Position>
      <Comment>1 登录 2注册 3支付</Comment>
      <DataType>tinyint(4)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="757" parent="264" name="appId">
      <Position>3</Position>
      <Comment>appId</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="758" parent="264" name="uuid">
      <Position>4</Position>
      <Comment>uuid</Comment>
      <DataType>varchar(64)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="759" parent="264" name="openId">
      <Position>5</Position>
      <Comment>openId</Comment>
      <DataType>varchar(32)|0s</DataType>
      <NotNull>1</NotNull>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="760" parent="264" name="deviceId">
      <Position>6</Position>
      <Comment>设备唯一指纹 (iOS、Android)</Comment>
      <DataType>varchar(255)|0s</DataType>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="761" parent="264" name="osv">
      <Position>7</Position>
      <Comment>系统版本 (iOS、Android)</Comment>
      <DataType>varchar(32)|0s</DataType>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="762" parent="264" name="model">
      <Position>8</Position>
      <Comment>手机厂商 (iOS、Android)</Comment>
      <DataType>varchar(32)|0s</DataType>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="763" parent="264" name="appVersion">
      <Position>9</Position>
      <Comment>APP版本号 (iOS、Android)</Comment>
      <DataType>varchar(32)|0s</DataType>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="764" parent="264" name="simulator">
      <Position>10</Position>
      <Comment>是否模拟器 (iOS、Android)</Comment>
      <DataType>varchar(32)|0s</DataType>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="765" parent="264" name="root">
      <Position>11</Position>
      <Comment>是否root (iOS、Android)</Comment>
      <DataType>varchar(32)|0s</DataType>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="766" parent="264" name="flag">
      <Position>12</Position>
      <Comment>是否被调试 (iOS、Android)</Comment>
      <DataType>varchar(32)|0s</DataType>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="767" parent="264" name="isInjection">
      <Position>13</Position>
      <Comment>是否注入 (iOS、Android)</Comment>
      <DataType>varchar(32)|0s</DataType>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="768" parent="264" name="mac">
      <Position>14</Position>
      <Comment>mac地址 (Android)</Comment>
      <DataType>varchar(32)|0s</DataType>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="769" parent="264" name="createdBy">
      <Position>15</Position>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="770" parent="264" name="createdOn">
      <Position>16</Position>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="771" parent="264" name="modifiedBy">
      <Position>17</Position>
      <DataType>bigint(20)|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>0</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <column id="772" parent="264" name="modifiedOn">
      <Position>18</Position>
      <DataType>datetime|0s</DataType>
      <NotNull>1</NotNull>
      <DefaultExpression>&apos;1970-01-01 00:00:00&apos;</DefaultExpression>
      <ColumnKind>normal</ColumnKind>
    </column>
    <index id="773" parent="264" name="ix_businessType_appId_uuid">
      <ColNames>businessType
appId
uuid</ColNames>
      <Type>btree</Type>
    </index>
    <key id="774" parent="264" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <ColNames>yidunDeviceStatisticsId</ColNames>
      <Primary>1</Primary>
    </key>
  </database-model>
</dataSource>