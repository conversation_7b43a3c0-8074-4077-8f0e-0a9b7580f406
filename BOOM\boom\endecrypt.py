import base64,django
from Crypto.Cipher import AES


class EncryptData:
    def __init__(self, key='Jiamian+Langren='):
        self.key = key.encode('utf-8')
        self.length = AES.block_size
        self.aes = AES.new(self.key, AES.MODE_ECB)
        self.unpad = lambda date: date[0:-ord(date[-1])]

    def pad(self, text):
        count = len(text.encode('utf-8'))
        add = self.length - (count % self.length)
        entext = text + (chr(add) * add)
        return entext

    def encrypt(self, encrData):
        encrData = str(encrData)
        res = self.aes.encrypt(self.pad(encrData).encode('utf-8'))
        msg = str(base64.b64encode(res), encoding='utf-8')
        return msg

    def decrypt(self, decrData):
        res = base64.decodebytes(decrData.encode('utf-8'))
        msg = self.aes.decrypt(res).decode('utf-8')
        return self.unpad(msg)


eg = EncryptData()
#用来加密
data = 'Zy9e5SqL8AiZOtduPnasOg=='
#用来解密
data2 = 'eE8b0UjwQ5V6W52f4F8zo/gSoYnObIloI5VnH2hCF8k='
# data2 = 'Bh9BCxg2rJHNs4iImTEhMQ=='
print(eg.encrypt(data))
print(eg.decrypt(data2))
