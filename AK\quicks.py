def quicksort(list, left, right):
    i = list[left]
    while left < right:
        while list[right] >= i and left < right:
            right -= 1
        list[left] = list[right]
        while list[left] <= i and left < right:
            left += 1
        list[right] = list[left]
    list[left]=i
    return left

def quicks(list, left, right):
    if left < right:
        p = quicksort(list, left, right)
        quicks(list, left, p - 1)
        quicks(list, p + 1, right)

import random
a=[i for i in range(1,100)]
random.shuffle(a)
print(a)
left=0
right=len(a)-1
quicks(a,left,right)
print(a)
