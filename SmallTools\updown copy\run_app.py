#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
启动文件上传应用的测试脚本
"""

import os
import sys
from app import app

def main():
    # 设置默认上传目录
    upload_path = os.path.join(os.getcwd(), 'uploads')
    if not os.path.exists(upload_path):
        os.makedirs(upload_path)
    
    # 配置Flask应用
    app.config['UPLOAD_FOLDER'] = upload_path
    
    print(f"上传目录: {upload_path}")
    print("启动服务器...")
    print("访问 http://localhost:7000 查看界面")
    
    # 启动应用
    app.run(host='0.0.0.0', port=7000, debug=True)

if __name__ == "__main__":
    main()
