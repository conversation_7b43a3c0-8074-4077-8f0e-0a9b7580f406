"""
<AUTHOR>
@date 2020/6/15 0015
"""
import random
import time
from typing import Dict, Optional, Tuple

from biz.gift.domain import Gift
from page.page import PageRoot
from role.role import Role
from util.log import get_logger, log


class RoomRole(Role):

    @log
    def infer_seat_num(self) -> Dict[str, int]:
        return self.page_root.game_room_page.seat_num

    @log
    def infer_game_role(self) -> str:
        label = self.page_root.game_room_page.game_role_identity_label
        label.wait_for_appearance(15)
        return label.get_text()

    @log
    def send_gift_in_gr(self, seat_num: int, gift_name: str, num: int = None):
        page = self.page_root.game_room_page
        page.seat_member_img(seat_num).click()
        page.send_gift_btn.click()
        super().send_gift(gift_name, num)

    def get_latest_gift_in_room(self) -> Optional[Tuple[bool, int, int, Gift]]:
        page = self.page_root.game_room_page
        while page.unread_msg_label.exists():
            page.swipe_down()

        return page.latest_gift


# 房主
class Owner(RoomRole):
    def __init__(self, page_root: PageRoot):
        super().__init__(page_root)
        self.logger = get_logger('Owner')

    @log
    def create_room(self) -> int:
        """
        创房
        :return:
        """
        create_room_btn = self.page_root.home_tab_page.create_room_btn
        create_room_btn.wait_for_appearance(10)
        create_room_btn.click()
        self.page_root.home_tab_page.dialog_confirm_btn.click()
        time.sleep(5)
        room_num = self.page_root.game_room_page.room_num_label.get_text()

        self.gift_panel_index = 0
        return int(room_num)

    @log
    def start_game(self):
        """
        开始游戏
        :return:
        """
        self.page_root.game_room_page.start_game_btn.click()


# 座位用户
class SeatUser(RoomRole):

    def __init__(self, page_root: PageRoot):
        super().__init__(page_root)
        self.logger = get_logger('SeatUser')

    @log
    def join_room(self, room_num: int):
        """
        进房
        :param room_num:
        :return:
        """
        self.page_root.home_tab_page.search_room_btn.click()
        self.page_root.home_tab_page.search_room_input.set_text(str(room_num))
        # 并发进房会出错，随机等待一会
        time.sleep(random.random() * 2)
        self.page_root.home_tab_page.dialog_confirm_btn.click()
        self.page_root.game_room_page.game_ready_btn.click()

        self.gift_panel_index = 0

    @log
    def follow_room(self):
        """
        跟房[未完成]
        :return:
        """
        self.page_root.home_tab_page.follow_room_btn.click()
        self.page_root.home_tab_page.enter_follow_room_btn.click()
        time.sleep(5)
        self.page_root.game_room_page.game_ready_btn.click()
