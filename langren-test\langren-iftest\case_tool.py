"""
<AUTHOR>
@date 2020/7/24 0024
"""
from typing import List, <PERSON><PERSON>, Dict

import time
import xlrd
from xlrd import Book
from xlrd.sheet import Sheet, Cell

from domain import UserConfig
from langren_apollo import ApolloCall
from langren_database import DatabaseCall


def read_gift_cases(path: str) -> \
        Tuple[Dict[int, UserConfig], List[Tuple[str, str, Tuple]], List[Tuple[str, str, str, str]],
              Dict[str, List[Tuple[int, int, int, int]]]]:
    workbook: Book = xlrd.open_workbook(path)

    user_cfg_dict: Dict[int, UserConfig] = {}
    prepare_database: List[Tuple[str, str, Tuple]] = []
    prepare_apollo: List[Tuple[str, str, str, str]] = []
    all_case: Dict[str, List[Tuple[int, int, int, int]]] = {}

    for sheet_name in workbook.sheet_names():
        st: Sheet = workbook.sheet_by_name(sheet_name)

        if sheet_name == 'USER':
            for i in range(1, st.nrows):
                row: List[Cell] = st.row(i)
                user_id = int(row[0].value)
                user_identity = row[1].value
                access_token = row[2].value

                user_cfg_dict[user_id] = UserConfig(user_id, user_identity, access_token)
        elif sheet_name == 'PREPARE_DATABASE':
            for i in range(1, st.nrows):
                row: List[Cell] = st.row(i)
                database = row[0].value
                t = row[1].value
                op_type = ['stored_procedure', 'sql']
                if t not in op_type:
                    raise AssertionError(f'type must be in {op_type}')

                args = []
                for ci in range(2, len(row)):
                    value = row[ci].value
                    if value == '':
                        break
                    args.append(value)

                prepare_database.append((database, t, tuple(args)))
        elif sheet_name == 'PREPARE_APOLLO':
            for i in range(1, st.nrows):
                row: List[Cell] = st.row(i)
                app_id = row[0].value
                namespace = row[1].value
                key = row[2].value
                value = row[3].value
                prepare_apollo.append((app_id, namespace, key, value))
        elif sheet_name.startswith('CASE_'):
            cases: List[Tuple[int, int, int, int]] = []
            for i in range(1, st.nrows):
                row: List[Cell] = st.row(i)
                from_user_id = int(row[0].value)
                to_user_id = int(row[1].value)
                gift_config_id = int(row[2].value)
                gift_count = int(row[3].value)
                cases.append((from_user_id, to_user_id, gift_config_id, gift_count))
            all_case[sheet_name[len('CASE_'):]] = cases
        else:
            print(f'ignore undefined sheet name: {sheet_name}')

    return user_cfg_dict, prepare_database, prepare_apollo, all_case


def do_prepare_db(operations: List[Tuple[str, str, Tuple]]):
    db_calls: Dict[str, DatabaseCall] = {}

    for op in operations:
        database = op[0]
        if database not in db_calls:
            db_calls[database] = DatabaseCall(database)

    for op in operations:
        database, t, args = op
        if t == 'sql':
            print(f'SQL: {args}')
            db_calls[database].execute_sql(args[0])
        elif t == 'stored_procedure':
            print(f'STORED_PROCEDURE: {args}')
            proc_args = tuple(args[1:])
            db_calls[database].call_stored_procedure(args[0], proc_args)

    for db in db_calls:
        db_calls[db].dispose()


def do_prepare_apollo(operations: List[Tuple[str, str, str, str]]):
    apollo_call = ApolloCall()

    release_namespaces = {}
    for op in operations:
        print(f'APOLLO: {op}')
        app_id, namespace, key, value = op
        # 缓存自动成值
        if namespace == 'langren.kgameCache' and key.endswith('.suffix') and int(value) == 0:
            value = str(int(time.time() * 1000))
        apollo_call.edit_config(app_id, namespace, key, value)
        release_namespaces[namespace + app_id] = namespace

    for app_id in release_namespaces:
        namespace = release_namespaces[app_id]
        app_id = app_id[len(namespace):]
        apollo_call.release_namespace(app_id, namespace)
