import hmac, time, base64, urllib.parse
from hashlib import sha1

boomAppSecretKey = {'btest': '123456', 'hunter': 'CaMWz3WKZ3E4QAG8A44V2zbQmB7AO4mn',
                    'prod131946':'UDqmCPueZvaJl3aMxkzUHrxxekF6ukO9',
                    'yg55992558':'5q42OlRJN3fXeyQsuJTZT4WKJXFnY9Y2',
                    'bestwish10':'ey5zGIAfLgQjjCMw1Cy15Siesur4AHyP'
                    }
# boomAppClientSecretKey = {'btest': '123456','hunter':'n6Xr0C7CftwovB4LglvYoxvaB5L4iLQ9'}
boomtimestamp = str(int(time.time()*1000))


def getsign(appid='btest'):
    headers = {}
    boominfo = [['boom-app-id', appid], ['boom-nonce', '123456'], ['boom-signature-method', 'HMAC-SHA1'],
                ['boom-timestamp', boomtimestamp], ['boom-version', '*******']]
    boominfo.sort()
    strsign = ''
    for key in boominfo:
        strsign += str(key[0] + '=' + key[1] + '&')
        headers[key[0]] = key[1]
    strsign = strsign.strip('&')
    boomsignature = hmac.new(boomAppSecretKey[appid].encode(), strsign.encode(),
                             sha1).digest()  # hmac.has1加密(二进制)
    boomsignature = base64.b64encode(boomsignature)  # base64加密
    boomsignature = urllib.parse.quote(boomsignature, safe="")  # urlencode
    headers['boom-signature'] = boomsignature
    return headers


