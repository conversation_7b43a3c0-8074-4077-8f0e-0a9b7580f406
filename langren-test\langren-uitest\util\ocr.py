"""
<AUTHOR>
@date 2020/7/9 0009
"""

from PIL import Image
from pytesseract import pytesseract

from util.log import get_logger

pytesseract.tesseract_cmd = 'C:\\Program Files\\Tesseract-OCR\\tesseract.exe'

logger = get_logger('Ocr')


def img_to_str(img: Image) -> str:
    result = pytesseract.image_to_string(img, lang='chi_sim')
    logger.info('[OCR] %s' % ' '.join(result.replace(' ', '').split('\n')))
    return result
