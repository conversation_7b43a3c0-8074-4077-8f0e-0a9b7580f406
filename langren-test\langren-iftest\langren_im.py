"""
<AUTHOR>
@date 2020/7/13 0013
"""
import uuid
from typing import Dict, Tuple

import time

import protobuf_pb2 as pb
from domain import GiftConfig
from im_client import TcpClientProtocol, connect_im
from util.log import log, get_logger

logger = get_logger('LangrenIM')


class ImCall(object):

    def __init__(self, host: str, port: int = 6656):
        self.host = host
        self.port = port
        self.client: TcpClientProtocol = connect_im(self.host, self.port)

    def __str__(self):
        return f'ImCall host: {self.host} port: {self.port}'

    def login(self, user_id: int, access_token: str, device: str):
        """
        登录im
        :param user_id:
        :param access_token:
        :param device:
        :return:
        """
        req = pb.LoginReq()
        req.accessToken = access_token
        req.userId = user_id
        req.device = device

        resp = self.client.send_sync(300, 100, req)
        self.__check_error(resp)

    @staticmethod
    def __check_error(resp):
        if resp.header.code != 0:
            error_resp = pb.ErrorResp()
            error_resp.ParseFromString(resp.body)
            raise AssertionError(str(error_resp.errorMsg))

    @log
    def send_gift(self, from_uid: int, to_uid: int, gift_config: GiftConfig, count: int) -> Tuple[int, Dict[str, int]]:
        """
        送礼
        :param from_uid:
        :param to_uid:
        :param gift_config:
        :param count:
        :return: popularity/effects
        """
        msg = pb.PrivateMessage()
        setattr(msg, 'from', str(from_uid))
        msg.to = str(to_uid)
        msg.groupType = 1
        msg_id = str(uuid.uuid4())
        msg.messageId = msg_id
        msg.messageOn = int(time.time() * 1000)
        msg.msgType = 5
        msg.msgSubType = 0
        msg.length = 0
        msg.content = '[礼物]'
        msg.chatBubbleId = 0
        msg.vipType = 0
        msg.headFrameId = 0

        present = msg.present
        present.presentConfigId = gift_config.config_id
        pst_key = str(uuid.uuid4())
        present.presentKey = pst_key
        present.presentName = gift_config.name
        present.effect = gift_config.effect
        present.presentThumb = gift_config.thumb
        present.amount = gift_config.amount
        present.fromUserId = from_uid
        present.toUserId = to_uid
        present.count = count
        present.popularity = gift_config.popularity
        present.privateGive = False

        try:
            resp = self.client.send_sync(800, 100, msg, 15)
        except Exception as e:
            logger.error(f'timeout waiting for send_gift result, msg_id:{msg_id}, pst_key:{pst_key}')
            raise e
        self.__check_error(resp)
        rec_msg = pb.PrivateMessage()
        rec_msg.ParseFromString(resp.body)
        effect_list: str = rec_msg.present.effect

        effect_detail_list: Dict[str, int] = {}
        for line in effect_list.split('<br>'):
            t = []

            in_tag = False
            for c in line:
                if c == '<':
                    in_tag = True
                elif c == '>':
                    in_tag = False
                else:
                    if not in_tag and c != ' ':
                        t.append(c)
            e = ''.join(t)
            if e != '':
                def find_count(s: str) -> int:
                    tokens = '1234567890'
                    for i in range(0, len(s)):
                        if s[i] not in tokens:
                            if i == 0:
                                raise RuntimeError

                            return int(s[:i])
                    return int(s)

                if e.startswith('对方获得了'):
                    e = e[5:]

                if '*' in e:
                    split = e.split("*")
                    effect_detail_list[split[0]] = find_count(split[1])
                elif e.endswith('金币'):
                    effect_detail_list['金币'] = find_count(e)
                elif e.endswith('VIP'):
                    effect_detail_list['VIP'] = find_count(e)
                elif e.endswith('永久'):
                    effect_detail_list[e[:len(e) - 2]] = -1

        return rec_msg.present.popularity, effect_detail_list

    def dispose(self):
        self.client.disconnect()
