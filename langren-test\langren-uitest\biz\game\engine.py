"""
<AUTHOR>
@date 2020/6/18 0018
"""
import functools
from abc import abstractmethod
from threading import Condition
from typing import Dict, Callable, TypeVar, Generic, Optional

from biz.game.domain import Context
from util.log import get_logger
from util.thread_tool import Signal

ON_STATE = '__on_state__'

logger = get_logger('Engine')


class Stateful(object):

    @abstractmethod
    def define_state(self, engine: 'Engine'):
        pass


T = TypeVar('T', bound=Context)

S = TypeVar('S', bound=Stateful)


class Engine(Generic[T]):
    class EngineEvent:
        ENGINE_START = 'engine_start'

    def __init__(self, initial_state: str, context: T):
        self.initial_state = initial_state
        self.__current_state = None
        self.state_id = 0
        self.context: T = context
        self.mapping: Dict[str, Dict[str, str]] = {}
        self.event_history = []
        self.state_handlers: Dict[str, Callable[[Engine[T], str, str, int], None]] = {}
        self.end_signal: Signal[Optional[Exception]] = Signal()
        self.__lock = Condition()

    def define(self, from_state: str, event: str, to_state: str):
        self.mapping.setdefault(from_state, {})
        self.mapping[from_state][event] = to_state

    def start(self):
        self.__current_state = self.initial_state
        self.context.thread_tool.run_async(
            lambda: self.__call_state_handler(self.initial_state))

    def trigger(self, state_id: int, event: str):
        with self.__lock:
            if state_id != self.state_id:
                return
            self.state_id += 1
        self.event_history.append(event)
        from_state = self.__current_state
        new_state = self.mapping[self.__current_state][event]
        logger.info(
            '[%s] [%s] %s -> %s [%s]' % (state_id, self.context.status(), self.__current_state, new_state, event))
        self.__current_state = new_state
        self.context.thread_tool.run_async(lambda: self.__call_state_handler(new_state))

    def __call_state_handler(self, new_state: str):
        if new_state in self.state_handlers:
            try:
                kargs = {'engine': self, 'state_id': self.state_id}
                self.state_handlers[new_state](**kargs)
            except Exception as e:
                self.end_signal.notify_all(e)
                self.state_id = -self.state_id
                raise e
        if new_state not in self.mapping:
            self.end_signal.notify_all(None)

    def bind(self, s: S):
        cv = vars(s.__class__)
        for v in cv:
            func = cv[v]
            if callable(func):
                state = getattr(func, ON_STATE, None)
                if state is not None:
                    # def closure_func(f, st) -> Callable[..., None]:
                    #     def wrap_func(*args, **kwargs):
                    #         set_thread_info('state: [%s] status: [%s]' % (st, self.context.status()))
                    #         f(*args, **kwargs)
                    #
                    #     return wrap_func
                    #
                    # self.state_handlers[state] = closure_func(getattr(s, v), state)
                    self.state_handlers[state] = getattr(s, v)
        s.define_state(self)

    def wait_end(self):
        exception = self.end_signal.wait()
        self.context.dispose()
        if exception is not None:
            raise exception


def on_state(state):
    def deco(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            return func(*args, **kwargs)

        setattr(wrapper, ON_STATE, state)
        return wrapper

    return deco
