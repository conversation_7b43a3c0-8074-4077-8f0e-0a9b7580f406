"""
<AUTHOR>
@date 2020/7/13 0013
"""
import threading
from concurrent.futures.thread import Thread<PERSON>oolExecutor
from threading import Thread, Event
from typing import Callable, Dict, Union

import time
from google.protobuf.message import Message
from twisted.internet import reactor
from twisted.internet.base import ReactorBase
from twisted.internet.endpoints import TCP4ClientEndpoint, connectProtocol
from twisted.internet.protocol import Protocol
from twisted.internet.protocol import connectionDone
from twisted.internet.tcp import Client

import protobuf_pb2 as pb
from util.thread_tool import Signal

RESP_CONSUMER = Callable[[pb.Response], None]


class ReactorThread(Thread):
    def __init__(self, rat: ReactorBase):
        super().__init__()
        self.reactor = rat

    def run(self):
        self.reactor.run(installSignalHandlers=False)


rt = ReactorThread(reactor)


class TcpClientProtocol(Protocol):

    def __init__(self):
        self.executor: ThreadPoolExecutor = ThreadPoolExecutor()
        self.waiters: Dict[int, RESP_CONSUMER] = {}
        self.connected_event = Event()

        self.client: Client = None
        self.shutdown: bool = False
        self.cseq_no: int = 0
        self.__lock = threading.Condition()
        self.m_len: int = -1
        self.m_ba: bytearray = bytearray()

    def connectionMade(self):
        self.client: Client = self.transport
        self.shutdown: bool = False
        self.executor.submit(self.__heartbeat)
        self.connected_event.set()

    def connectionLost(self, reason=connectionDone):
        self.shutdown = True
        self.connected_event.clear()

    def dataReceived(self, data: bytes):
        def check_end() -> bool:
            if len(self.m_ba) != self.m_len:
                return False

            self.executor.submit(self.receive, self.m_ba)
            self.m_len = 0
            self.m_ba = bytearray()
            return True

        i = 0
        while i < len(data):
            if self.m_len <= 0:
                self.m_len = int.from_bytes(data[i:i + 4], byteorder='big', signed=False)
                i += 4
                continue
            if check_end():
                continue
            self.m_ba.append(data[i])
            i += 1
            check_end()

    def send(self, sid: int, cid: int, msg: Union[bytes, Message], rc: RESP_CONSUMER = None):
        with self.__lock:
            self.cseq_no += 1
            cseq_no = self.cseq_no

        req = pb.Request()
        req.header.sid = sid
        req.header.cid = cid

        req.header.cseqNo = cseq_no
        req.header.reqTime = int(time.time() * 1000)

        if isinstance(msg, bytes):
            req.body = msg
        else:
            req.body = msg.SerializeToString()

        req_bytes = req.SerializeToString()

        msg_len = len(req_bytes)
        ba = bytearray()
        msg_len_bytes = msg_len.to_bytes(4, byteorder='big', signed=False)
        for b in msg_len_bytes:
            ba.append(b)
        for b in req_bytes:
            ba.append(b)

        if rc is not None:
            self.waiters[cseq_no] = rc
        self.client.write(bytes(ba))

    def send_sync(self, sid: int, cid: int, msg: Union[bytes, Message], timeout: int = None) -> pb.Response:
        e: Signal[pb.Response] = Signal()
        self.send(sid, cid, msg, lambda res: e.notify_all(res))
        return e.wait(timeout)

    def receive(self, data: bytearray):
        res = pb.Response()
        res.ParseFromString(bytes(data))
        # print(str(hash(self.client)) + ': ' + str(res))
        cn = res.header.cseqNo
        if cn in self.waiters:
            callback = self.waiters[cn]
            del self.waiters[cn]
            callback(res)

    def __heartbeat(self):
        i = 0
        while not self.shutdown:
            if i == 10:
                i = 0
                self.send(400, 4000, b'')
            time.sleep(1)
            i += 1

    def wait_connected(self, timeout=None):
        self.connected_event.wait(timeout)

    def disconnect(self):
        self.client.loseConnection()
        self.executor.shutdown()


def connect_im(host: str, port: int) -> TcpClientProtocol:
    point = TCP4ClientEndpoint(reactor, host, port)
    p = TcpClientProtocol()
    connectProtocol(point, p)
    p.wait_connected()
    return p


def start_reactor():
    rt.start()


def stop_reactor():
    rt.reactor.callFromThread(rt.reactor.stop)
    rt.join()
