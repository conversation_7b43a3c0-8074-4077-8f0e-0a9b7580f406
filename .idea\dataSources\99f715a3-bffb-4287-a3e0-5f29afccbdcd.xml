<?xml version="1.0" encoding="UTF-8"?>
<dataSource name="qa1 holostatic">
  <database-model serializer="dbm" dbms="POSTGRES" family-id="POSTGRES" format-version="4.17">
    <root id="1">
      <ServerVersion>11.3</ServerVersion>
      <StartupTime>1747133097</StartupTime>
    </root>
    <database id="2" parent="1" name="lr_recommend">
      <ObjectId>20803</ObjectId>
      <Owner>lr_recommend_admin</Owner>
    </database>
    <database id="3" parent="1" name="lr_recommend_qa3">
      <ObjectId>23295</ObjectId>
      <Owner>lr_recommend_qa3_admin</Owner>
    </database>
    <database id="4" parent="1" name="lr_statistic_hologres">
      <ObjectId>25645</ObjectId>
      <Owner>lr_statistic_hologres_admin</Owner>
      <IntrospectionStateNumber>113269</IntrospectionStateNumber>
      <Relations>sequence|table|27748|27731|1
sequence|table|27868|27802|1
sequence|table|574421|574423|1
sequence|table|27794|27778|1
sequence|table|27939|27870|1
</Relations>
    </database>
    <database id="5" parent="1" name="lr_statistic_hologres_qa3">
      <ObjectId>28012</ObjectId>
      <Owner>lr_statistic_hologres_qa3_admin</Owner>
    </database>
    <database id="6" parent="1" name="postgres">
      <ObjectId>30380</ObjectId>
      <Owner>holo_admin</Owner>
      <IntrospectionStateNumber>113255</IntrospectionStateNumber>
      <Current>1</Current>
    </database>
    <database id="7" parent="1" name="recreation_room">
      <ObjectId>32457</ObjectId>
      <Owner>recreation_room_admin</Owner>
    </database>
    <role id="8" parent="1" name="1732515765807132">
      <ObjectId>18692</ObjectId>
      <SuperRole>1</SuperRole>
      <CanLogin>1</CanLogin>
    </role>
    <role id="9" parent="1" name="holo_admin">
      <ObjectId>10</ObjectId>
      <SuperRole>1</SuperRole>
      <CreateRole>1</CreateRole>
      <CreateDb>1</CreateDb>
      <CanLogin>1</CanLogin>
      <Replication>1</Replication>
      <BypassRls>1</BypassRls>
    </role>
    <role id="10" parent="1" name="lr_recommend_admin">
      <ObjectId>18693</ObjectId>
    </role>
    <role id="11" parent="1" name="lr_recommend_developer">
      <ObjectId>18694</ObjectId>
    </role>
    <role id="12" parent="1" name="lr_recommend_qa3_admin">
      <ObjectId>18695</ObjectId>
    </role>
    <role id="13" parent="1" name="lr_recommend_qa3_developer">
      <ObjectId>18696</ObjectId>
    </role>
    <role id="14" parent="1" name="lr_recommend_qa3_viewer">
      <ObjectId>18697</ObjectId>
    </role>
    <role id="15" parent="1" name="lr_recommend_qa3_writer">
      <ObjectId>18698</ObjectId>
    </role>
    <role id="16" parent="1" name="lr_recommend_viewer">
      <ObjectId>18699</ObjectId>
    </role>
    <role id="17" parent="1" name="lr_recommend_writer">
      <ObjectId>18700</ObjectId>
    </role>
    <role id="18" parent="1" name="lr_statistic_hologres_admin">
      <ObjectId>18701</ObjectId>
    </role>
    <role id="19" parent="1" name="lr_statistic_hologres_developer">
      <ObjectId>18702</ObjectId>
    </role>
    <role id="20" parent="1" name="lr_statistic_hologres_qa3_admin">
      <ObjectId>18703</ObjectId>
    </role>
    <role id="21" parent="1" name="lr_statistic_hologres_qa3_developer">
      <ObjectId>18704</ObjectId>
    </role>
    <role id="22" parent="1" name="lr_statistic_hologres_qa3_viewer">
      <ObjectId>18705</ObjectId>
    </role>
    <role id="23" parent="1" name="lr_statistic_hologres_qa3_writer">
      <ObjectId>18706</ObjectId>
    </role>
    <role id="24" parent="1" name="lr_statistic_hologres_viewer">
      <ObjectId>18707</ObjectId>
    </role>
    <role id="25" parent="1" name="lr_statistic_hologres_writer">
      <ObjectId>18708</ObjectId>
    </role>
    <role id="26" parent="1" name="p4_203529501672912904">
      <ObjectId>18709</ObjectId>
      <SuperRole>1</SuperRole>
      <CanLogin>1</CanLogin>
    </role>
    <role id="27" parent="1" name="p4_211319987523635783">
      <ObjectId>18710</ObjectId>
      <CanLogin>1</CanLogin>
    </role>
    <role id="28" parent="1" name="p4_214651945113396097">
      <ObjectId>18711</ObjectId>
      <SuperRole>1</SuperRole>
      <CanLogin>1</CanLogin>
    </role>
    <role id="29" parent="1" name="p4_216268587228462299">
      <ObjectId>18712</ObjectId>
      <CanLogin>1</CanLogin>
    </role>
    <role id="30" parent="1" name="p4_226041568689962375">
      <ObjectId>18713</ObjectId>
      <CanLogin>1</CanLogin>
    </role>
    <role id="31" parent="1" name="p4_227147869295297065">
      <ObjectId>18714</ObjectId>
      <SuperRole>1</SuperRole>
      <CanLogin>1</CanLogin>
    </role>
    <role id="32" parent="1" name="p4_229485993187840343">
      <ObjectId>18715</ObjectId>
      <CanLogin>1</CanLogin>
    </role>
    <role id="33" parent="1" name="p4_231307791734554974">
      <ObjectId>18716</ObjectId>
      <CanLogin>1</CanLogin>
    </role>
    <role id="34" parent="1" name="p4_231496999550087358">
      <ObjectId>18717</ObjectId>
      <CanLogin>1</CanLogin>
    </role>
    <role id="35" parent="1" name="p4_248296224709597015">
      <ObjectId>18718</ObjectId>
      <CanLogin>1</CanLogin>
    </role>
    <role id="36" parent="1" name="p4_263679656647443852">
      <ObjectId>18719</ObjectId>
      <CanLogin>1</CanLogin>
    </role>
    <role id="37" parent="1" name="p4_282641929103383969">
      <ObjectId>18720</ObjectId>
      <SuperRole>1</SuperRole>
      <CanLogin>1</CanLogin>
    </role>
    <role id="38" parent="1" name="p4_289856899744564609">
      <ObjectId>18721</ObjectId>
      <CanLogin>1</CanLogin>
    </role>
    <role id="39" parent="1" name="pg_execute_server_program">
      <ObjectId>4571</ObjectId>
    </role>
    <role id="40" parent="1" name="pg_monitor">
      <ObjectId>3373</ObjectId>
    </role>
    <role id="41" parent="1" name="pg_read_all_settings">
      <ObjectId>3374</ObjectId>
    </role>
    <role id="42" parent="1" name="pg_read_all_stats">
      <ObjectId>3375</ObjectId>
    </role>
    <role id="43" parent="1" name="pg_read_server_files">
      <ObjectId>4569</ObjectId>
    </role>
    <role id="44" parent="1" name="pg_signal_backend">
      <ObjectId>4200</ObjectId>
    </role>
    <role id="45" parent="1" name="pg_stat_scan_tables">
      <ObjectId>3377</ObjectId>
    </role>
    <role id="46" parent="1" name="pg_write_server_files">
      <ObjectId>4570</ObjectId>
    </role>
    <role id="47" parent="1" name="recreation_room_admin">
      <ObjectId>18722</ObjectId>
    </role>
    <role id="48" parent="1" name="recreation_room_developer">
      <ObjectId>18723</ObjectId>
    </role>
    <role id="49" parent="1" name="recreation_room_viewer">
      <ObjectId>18724</ObjectId>
    </role>
    <role id="50" parent="1" name="recreation_room_writer">
      <ObjectId>18725</ObjectId>
    </role>
    <schema id="51" parent="4" name="hologres">
      <ObjectId>25646</ObjectId>
      <StateNumber>2069</StateNumber>
      <Owner>holo_admin</Owner>
    </schema>
    <schema id="52" parent="4" name="hologres_sample">
      <ObjectId>25647</ObjectId>
      <StateNumber>1846</StateNumber>
      <Owner>holo_admin</Owner>
    </schema>
    <schema id="53" parent="4" name="hologres_statistic">
      <ObjectId>25649</ObjectId>
      <StateNumber>1847</StateNumber>
      <Owner>holo_admin</Owner>
    </schema>
    <schema id="54" parent="4" name="hologres_streaming_mv">
      <ObjectId>27258</ObjectId>
      <StateNumber>1847</StateNumber>
      <Owner>holo_admin</Owner>
    </schema>
    <schema id="55" parent="4" name="information_schema">
      <ObjectId>13011</ObjectId>
      <StateNumber>370</StateNumber>
      <Owner>holo_admin</Owner>
    </schema>
    <schema id="56" parent="4" name="pg_catalog">
      <ObjectId>11</ObjectId>
      <Comment>system catalog schema</Comment>
      <StateNumber>363</StateNumber>
      <Owner>holo_admin</Owner>
    </schema>
    <schema id="57" parent="4" name="public">
      <ObjectId>2200</ObjectId>
      <Comment>standard public schema</Comment>
      <StateNumber>2074</StateNumber>
      <Owner>holo_admin</Owner>
      <IntrospectionStateNumber>113271</IntrospectionStateNumber>
      <Current>1</Current>
    </schema>
    <foreign-data-wrapper id="58" parent="4" name="dlf_fdw">
      <ObjectId>27320</ObjectId>
      <StateNumber>1849</StateNumber>
      <Owner>holo_admin</Owner>
      <Handler>dlf_fdw_handler</Handler>
      <HandlerSchema>pg_catalog</HandlerSchema>
      <Validator>dlf_fdw_validator</Validator>
      <ValidatorSchema>pg_catalog</ValidatorSchema>
    </foreign-data-wrapper>
    <foreign-data-wrapper id="59" parent="4" name="hg_admin_command_fdw">
      <ObjectId>27333</ObjectId>
      <StateNumber>1851</StateNumber>
      <Owner>holo_admin</Owner>
    </foreign-data-wrapper>
    <foreign-data-wrapper id="60" parent="4" name="holo_link_fdw">
      <ObjectId>27449</ObjectId>
      <StateNumber>1861</StateNumber>
      <Owner>holo_admin</Owner>
    </foreign-data-wrapper>
    <foreign-data-wrapper id="61" parent="4" name="odps_fdw">
      <ObjectId>27457</ObjectId>
      <StateNumber>1865</StateNumber>
      <Owner>holo_admin</Owner>
      <Handler>odps_fdw_handler</Handler>
      <HandlerSchema>pg_catalog</HandlerSchema>
      <Validator>odps_fdw_validator</Validator>
      <ValidatorSchema>pg_catalog</ValidatorSchema>
    </foreign-data-wrapper>
    <foreign-data-wrapper id="62" parent="4" name="postgres_fdw">
      <ObjectId>27461</ObjectId>
      <StateNumber>1867</StateNumber>
      <Owner>holo_admin</Owner>
      <Handler>postgres_fdw_handler</Handler>
      <HandlerSchema>pg_catalog</HandlerSchema>
      <Validator>postgres_fdw_validator</Validator>
      <ValidatorSchema>pg_catalog</ValidatorSchema>
    </foreign-data-wrapper>
    <access-method id="63" parent="4" name="btree">
      <ObjectId>403</ObjectId>
      <Comment>b-tree index access method</Comment>
      <StateNumber>1</StateNumber>
      <Handler>pg_catalog.bthandler</Handler>
      <HandlerId>330</HandlerId>
      <Type>index</Type>
    </access-method>
    <access-method id="64" parent="4" name="hash">
      <ObjectId>405</ObjectId>
      <Comment>hash index access method</Comment>
      <StateNumber>1</StateNumber>
      <Handler>pg_catalog.hashhandler</Handler>
      <HandlerId>331</HandlerId>
      <Type>index</Type>
    </access-method>
    <access-method id="65" parent="4" name="gist">
      <ObjectId>783</ObjectId>
      <Comment>GiST index access method</Comment>
      <StateNumber>1</StateNumber>
      <Handler>pg_catalog.gisthandler</Handler>
      <HandlerId>332</HandlerId>
      <Type>index</Type>
    </access-method>
    <access-method id="66" parent="4" name="gin">
      <ObjectId>2742</ObjectId>
      <Comment>GIN index access method</Comment>
      <StateNumber>1</StateNumber>
      <Handler>pg_catalog.ginhandler</Handler>
      <HandlerId>333</HandlerId>
      <Type>index</Type>
    </access-method>
    <access-method id="67" parent="4" name="spgist">
      <ObjectId>4000</ObjectId>
      <Comment>SP-GiST index access method</Comment>
      <StateNumber>1</StateNumber>
      <Handler>pg_catalog.spghandler</Handler>
      <HandlerId>334</HandlerId>
      <Type>index</Type>
    </access-method>
    <access-method id="68" parent="4" name="brin">
      <ObjectId>3580</ObjectId>
      <Comment>block range index (BRIN) access method</Comment>
      <StateNumber>1</StateNumber>
      <Handler>pg_catalog.brinhandler</Handler>
      <HandlerId>335</HandlerId>
      <Type>index</Type>
    </access-method>
    <extension id="69" parent="4" name="dlf_fdw">
      <ObjectId>27317</ObjectId>
      <Comment>foreign-data wrapper for Data Lake Formation (DLF) table access</Comment>
      <StateNumber>1849</StateNumber>
      <Version>1.0</Version>
      <SchemaName>pg_catalog</SchemaName>
      <SchemaId>11</SchemaId>
    </extension>
    <extension id="70" parent="4" name="external_database">
      <ObjectId>35127</ObjectId>
      <Comment>hologres external database functions</Comment>
      <StateNumber>2764</StateNumber>
      <Version>1.0</Version>
      <SchemaName>pg_catalog</SchemaName>
      <SchemaId>11</SchemaId>
    </extension>
    <extension id="71" parent="4" name="hg_admin_cmd">
      <ObjectId>27321</ObjectId>
      <Comment>connect to other data sorce, such as holo executor/seahawks executor</Comment>
      <StateNumber>1851</StateNumber>
      <Version>1.0</Version>
      <SchemaName>pg_catalog</SchemaName>
      <SchemaId>11</SchemaId>
    </extension>
    <extension id="72" parent="4" name="hg_binlog">
      <ObjectId>27338</ObjectId>
      <Comment>hologres binlog implementation</Comment>
      <StateNumber>1853</StateNumber>
      <Version>0.2</Version>
      <SchemaName>pg_catalog</SchemaName>
      <SchemaId>11</SchemaId>
    </extension>
    <extension id="73" parent="4" name="hg_cron_view">
      <ObjectId>35086</ObjectId>
      <Comment>Hologres Cron User View Extention</Comment>
      <StateNumber>2762</StateNumber>
      <Version>1.0</Version>
      <SchemaName>pg_catalog</SchemaName>
      <SchemaId>11</SchemaId>
    </extension>
    <extension id="74" parent="4" name="hg_procedures">
      <ObjectId>34753</ObjectId>
      <Comment>hologres procedures extensions</Comment>
      <StateNumber>2742</StateNumber>
      <Version>1.0</Version>
      <SchemaName>pg_catalog</SchemaName>
      <SchemaId>11</SchemaId>
    </extension>
    <extension id="75" parent="4" name="hg_toolkit">
      <ObjectId>27349</ObjectId>
      <Comment>hologres toolkit functions</Comment>
      <StateNumber>1855</StateNumber>
      <Version>0.7</Version>
      <SchemaName>pg_catalog</SchemaName>
      <SchemaId>11</SchemaId>
    </extension>
    <extension id="76" parent="4" name="holo_dump_stat">
      <ObjectId>27400</ObjectId>
      <Comment>move pg_statistic to new instance of PostgreSQL</Comment>
      <StateNumber>1857</StateNumber>
      <Version>1.0</Version>
      <SchemaName>pg_catalog</SchemaName>
      <SchemaId>11</SchemaId>
    </extension>
    <extension id="77" parent="4" name="holo_dynamic_table">
      <ObjectId>35108</ObjectId>
      <Comment>A extension for dynamic table funcs.</Comment>
      <StateNumber>2763</StateNumber>
      <Version>1.0</Version>
      <SchemaName>pg_catalog</SchemaName>
      <SchemaId>11</SchemaId>
    </extension>
    <extension id="78" parent="4" name="holo_funcs">
      <ObjectId>25648</ObjectId>
      <Comment>hologres function extensions</Comment>
      <StateNumber>1847</StateNumber>
      <Version>1.0</Version>
      <SchemaName>pg_catalog</SchemaName>
      <SchemaId>11</SchemaId>
    </extension>
    <extension id="79" parent="4" name="holo_jsonb">
      <ObjectId>27414</ObjectId>
      <Comment>A extension that extend jsonb udf and operator class</Comment>
      <StateNumber>1859</StateNumber>
      <Version>1.0</Version>
      <SchemaName>public</SchemaName>
      <SchemaId>2200</SchemaId>
    </extension>
    <extension id="80" parent="4" name="holo_link">
      <ObjectId>27445</ObjectId>
      <Comment>connect to other data sorce, such as holo executor/seahawks executor</Comment>
      <StateNumber>1861</StateNumber>
      <Version>1.0</Version>
      <SchemaName>hologres</SchemaName>
      <SchemaId>25646</SchemaId>
    </extension>
    <extension id="81" parent="4" name="holo_system_admin">
      <ObjectId>27450</ObjectId>
      <Comment>start meta warehouse report</Comment>
      <StateNumber>1863</StateNumber>
      <Version>1.0</Version>
      <SchemaName>public</SchemaName>
      <SchemaId>2200</SchemaId>
    </extension>
    <extension id="82" parent="4" name="odps_fdw">
      <ObjectId>27454</ObjectId>
      <Comment>foreign-data wrapper for odps table access</Comment>
      <StateNumber>1865</StateNumber>
      <Version>1.0</Version>
      <SchemaName>pg_catalog</SchemaName>
      <SchemaId>11</SchemaId>
    </extension>
    <extension id="83" parent="4" name="plpgsql">
      <ObjectId>13295</ObjectId>
      <Comment>PL/pgSQL procedural language</Comment>
      <StateNumber>523</StateNumber>
      <Version>1.0</Version>
      <SchemaName>pg_catalog</SchemaName>
      <SchemaId>11</SchemaId>
    </extension>
    <extension id="84" parent="4" name="postgres_fdw">
      <ObjectId>27458</ObjectId>
      <Comment>foreign-data wrapper for remote PostgreSQL servers</Comment>
      <StateNumber>1867</StateNumber>
      <Version>1.0</Version>
      <SchemaName>pg_catalog</SchemaName>
      <SchemaId>11</SchemaId>
    </extension>
    <extension id="85" parent="4" name="query_log">
      <ObjectId>27462</ObjectId>
      <Comment>Hologres Query Log Extention</Comment>
      <StateNumber>1869</StateNumber>
      <Version>6.0</Version>
      <SchemaName>pg_catalog</SchemaName>
      <SchemaId>11</SchemaId>
      <AvailableUpdates>2.0
3.0
4.0
5.0</AvailableUpdates>
    </extension>
    <extension id="86" parent="4" name="slpm">
      <ObjectId>27524</ObjectId>
      <Comment>schema-level permission model extensions</Comment>
      <StateNumber>1871</StateNumber>
      <Version>1.0</Version>
      <SchemaName>pg_catalog</SchemaName>
      <SchemaId>11</SchemaId>
    </extension>
    <extension id="87" parent="4" name="spm">
      <ObjectId>27558</ObjectId>
      <Comment>database-level simple privilege model extensions</Comment>
      <StateNumber>1873</StateNumber>
      <Version>1.0</Version>
      <SchemaName>pg_catalog</SchemaName>
      <SchemaId>11</SchemaId>
    </extension>
    <schema id="88" parent="6" name="hologres">
      <ObjectId>30381</ObjectId>
      <StateNumber>2494</StateNumber>
      <Owner>holo_admin</Owner>
    </schema>
    <schema id="89" parent="6" name="hologres_sample">
      <ObjectId>30382</ObjectId>
      <StateNumber>2406</StateNumber>
      <Owner>holo_admin</Owner>
    </schema>
    <schema id="90" parent="6" name="hologres_statistic">
      <ObjectId>30384</ObjectId>
      <StateNumber>2407</StateNumber>
      <Owner>holo_admin</Owner>
    </schema>
    <schema id="91" parent="6" name="hologres_streaming_mv">
      <ObjectId>31994</ObjectId>
      <StateNumber>2407</StateNumber>
      <Owner>holo_admin</Owner>
    </schema>
    <schema id="92" parent="6" name="information_schema">
      <ObjectId>13011</ObjectId>
      <StateNumber>370</StateNumber>
      <Owner>holo_admin</Owner>
    </schema>
    <schema id="93" parent="6" name="pg_catalog">
      <ObjectId>11</ObjectId>
      <Comment>system catalog schema</Comment>
      <StateNumber>363</StateNumber>
      <Owner>holo_admin</Owner>
    </schema>
    <schema id="94" parent="6" name="public">
      <ObjectId>2200</ObjectId>
      <Comment>standard public schema</Comment>
      <StateNumber>364</StateNumber>
      <Owner>holo_admin</Owner>
      <IntrospectionStateNumber>113259</IntrospectionStateNumber>
      <Current>1</Current>
    </schema>
    <foreign-data-wrapper id="95" parent="6" name="dlf_fdw">
      <ObjectId>32056</ObjectId>
      <StateNumber>2409</StateNumber>
      <Owner>holo_admin</Owner>
      <Handler>dlf_fdw_handler</Handler>
      <HandlerSchema>pg_catalog</HandlerSchema>
      <Validator>dlf_fdw_validator</Validator>
      <ValidatorSchema>pg_catalog</ValidatorSchema>
    </foreign-data-wrapper>
    <foreign-data-wrapper id="96" parent="6" name="hg_admin_command_fdw">
      <ObjectId>32069</ObjectId>
      <StateNumber>2411</StateNumber>
      <Owner>holo_admin</Owner>
    </foreign-data-wrapper>
    <foreign-data-wrapper id="97" parent="6" name="holo_link_fdw">
      <ObjectId>32185</ObjectId>
      <StateNumber>2421</StateNumber>
      <Owner>holo_admin</Owner>
    </foreign-data-wrapper>
    <foreign-data-wrapper id="98" parent="6" name="odps_fdw">
      <ObjectId>32193</ObjectId>
      <StateNumber>2425</StateNumber>
      <Owner>holo_admin</Owner>
      <Handler>odps_fdw_handler</Handler>
      <HandlerSchema>pg_catalog</HandlerSchema>
      <Validator>odps_fdw_validator</Validator>
      <ValidatorSchema>pg_catalog</ValidatorSchema>
    </foreign-data-wrapper>
    <foreign-data-wrapper id="99" parent="6" name="postgres_fdw">
      <ObjectId>32197</ObjectId>
      <StateNumber>2427</StateNumber>
      <Owner>holo_admin</Owner>
      <Handler>postgres_fdw_handler</Handler>
      <HandlerSchema>pg_catalog</HandlerSchema>
      <Validator>postgres_fdw_validator</Validator>
      <ValidatorSchema>pg_catalog</ValidatorSchema>
    </foreign-data-wrapper>
    <access-method id="100" parent="6" name="btree">
      <ObjectId>403</ObjectId>
      <Comment>b-tree index access method</Comment>
      <StateNumber>1</StateNumber>
      <Handler>pg_catalog.bthandler</Handler>
      <HandlerId>330</HandlerId>
      <Type>index</Type>
    </access-method>
    <access-method id="101" parent="6" name="hash">
      <ObjectId>405</ObjectId>
      <Comment>hash index access method</Comment>
      <StateNumber>1</StateNumber>
      <Handler>pg_catalog.hashhandler</Handler>
      <HandlerId>331</HandlerId>
      <Type>index</Type>
    </access-method>
    <access-method id="102" parent="6" name="gist">
      <ObjectId>783</ObjectId>
      <Comment>GiST index access method</Comment>
      <StateNumber>1</StateNumber>
      <Handler>pg_catalog.gisthandler</Handler>
      <HandlerId>332</HandlerId>
      <Type>index</Type>
    </access-method>
    <access-method id="103" parent="6" name="gin">
      <ObjectId>2742</ObjectId>
      <Comment>GIN index access method</Comment>
      <StateNumber>1</StateNumber>
      <Handler>pg_catalog.ginhandler</Handler>
      <HandlerId>333</HandlerId>
      <Type>index</Type>
    </access-method>
    <access-method id="104" parent="6" name="spgist">
      <ObjectId>4000</ObjectId>
      <Comment>SP-GiST index access method</Comment>
      <StateNumber>1</StateNumber>
      <Handler>pg_catalog.spghandler</Handler>
      <HandlerId>334</HandlerId>
      <Type>index</Type>
    </access-method>
    <access-method id="105" parent="6" name="brin">
      <ObjectId>3580</ObjectId>
      <Comment>block range index (BRIN) access method</Comment>
      <StateNumber>1</StateNumber>
      <Handler>pg_catalog.brinhandler</Handler>
      <HandlerId>335</HandlerId>
      <Type>index</Type>
    </access-method>
    <extension id="106" parent="6" name="dlf_fdw">
      <ObjectId>32053</ObjectId>
      <Comment>foreign-data wrapper for Data Lake Formation (DLF) table access</Comment>
      <StateNumber>2409</StateNumber>
      <Version>1.0</Version>
      <SchemaName>pg_catalog</SchemaName>
      <SchemaId>11</SchemaId>
    </extension>
    <extension id="107" parent="6" name="external_database">
      <ObjectId>35021</ObjectId>
      <Comment>hologres external database functions</Comment>
      <StateNumber>2758</StateNumber>
      <Version>1.0</Version>
      <SchemaName>pg_catalog</SchemaName>
      <SchemaId>11</SchemaId>
    </extension>
    <extension id="108" parent="6" name="hg_admin_cmd">
      <ObjectId>32057</ObjectId>
      <Comment>connect to other data sorce, such as holo executor/seahawks executor</Comment>
      <StateNumber>2411</StateNumber>
      <Version>1.0</Version>
      <SchemaName>pg_catalog</SchemaName>
      <SchemaId>11</SchemaId>
    </extension>
    <extension id="109" parent="6" name="hg_binlog">
      <ObjectId>32074</ObjectId>
      <Comment>hologres binlog implementation</Comment>
      <StateNumber>2413</StateNumber>
      <Version>0.2</Version>
      <SchemaName>pg_catalog</SchemaName>
      <SchemaId>11</SchemaId>
    </extension>
    <extension id="110" parent="6" name="hg_cron_view">
      <ObjectId>34980</ObjectId>
      <Comment>Hologres Cron User View Extention</Comment>
      <StateNumber>2756</StateNumber>
      <Version>1.0</Version>
      <SchemaName>pg_catalog</SchemaName>
      <SchemaId>11</SchemaId>
    </extension>
    <extension id="111" parent="6" name="hg_procedures">
      <ObjectId>34723</ObjectId>
      <Comment>hologres procedures extensions</Comment>
      <StateNumber>2740</StateNumber>
      <Version>1.0</Version>
      <SchemaName>pg_catalog</SchemaName>
      <SchemaId>11</SchemaId>
    </extension>
    <extension id="112" parent="6" name="hg_toolkit">
      <ObjectId>32085</ObjectId>
      <Comment>hologres toolkit functions</Comment>
      <StateNumber>2415</StateNumber>
      <Version>0.7</Version>
      <SchemaName>pg_catalog</SchemaName>
      <SchemaId>11</SchemaId>
    </extension>
    <extension id="113" parent="6" name="holo_dump_stat">
      <ObjectId>32136</ObjectId>
      <Comment>move pg_statistic to new instance of PostgreSQL</Comment>
      <StateNumber>2417</StateNumber>
      <Version>1.0</Version>
      <SchemaName>pg_catalog</SchemaName>
      <SchemaId>11</SchemaId>
    </extension>
    <extension id="114" parent="6" name="holo_dynamic_table">
      <ObjectId>35002</ObjectId>
      <Comment>A extension for dynamic table funcs.</Comment>
      <StateNumber>2757</StateNumber>
      <Version>1.0</Version>
      <SchemaName>pg_catalog</SchemaName>
      <SchemaId>11</SchemaId>
    </extension>
    <extension id="115" parent="6" name="holo_funcs">
      <ObjectId>30383</ObjectId>
      <Comment>hologres function extensions</Comment>
      <StateNumber>2407</StateNumber>
      <Version>1.0</Version>
      <SchemaName>pg_catalog</SchemaName>
      <SchemaId>11</SchemaId>
    </extension>
    <extension id="116" parent="6" name="holo_jsonb">
      <ObjectId>32150</ObjectId>
      <Comment>A extension that extend jsonb udf and operator class</Comment>
      <StateNumber>2419</StateNumber>
      <Version>1.0</Version>
      <SchemaName>public</SchemaName>
      <SchemaId>2200</SchemaId>
    </extension>
    <extension id="117" parent="6" name="holo_link">
      <ObjectId>32181</ObjectId>
      <Comment>connect to other data sorce, such as holo executor/seahawks executor</Comment>
      <StateNumber>2421</StateNumber>
      <Version>1.0</Version>
      <SchemaName>hologres</SchemaName>
      <SchemaId>30381</SchemaId>
    </extension>
    <extension id="118" parent="6" name="holo_system_admin">
      <ObjectId>32186</ObjectId>
      <Comment>start meta warehouse report</Comment>
      <StateNumber>2423</StateNumber>
      <Version>1.0</Version>
      <SchemaName>public</SchemaName>
      <SchemaId>2200</SchemaId>
    </extension>
    <extension id="119" parent="6" name="odps_fdw">
      <ObjectId>32190</ObjectId>
      <Comment>foreign-data wrapper for odps table access</Comment>
      <StateNumber>2425</StateNumber>
      <Version>1.0</Version>
      <SchemaName>pg_catalog</SchemaName>
      <SchemaId>11</SchemaId>
    </extension>
    <extension id="120" parent="6" name="plpgsql">
      <ObjectId>13295</ObjectId>
      <Comment>PL/pgSQL procedural language</Comment>
      <StateNumber>523</StateNumber>
      <Version>1.0</Version>
      <SchemaName>pg_catalog</SchemaName>
      <SchemaId>11</SchemaId>
    </extension>
    <extension id="121" parent="6" name="postgres_fdw">
      <ObjectId>32194</ObjectId>
      <Comment>foreign-data wrapper for remote PostgreSQL servers</Comment>
      <StateNumber>2427</StateNumber>
      <Version>1.0</Version>
      <SchemaName>pg_catalog</SchemaName>
      <SchemaId>11</SchemaId>
    </extension>
    <extension id="122" parent="6" name="query_log">
      <ObjectId>32198</ObjectId>
      <Comment>Hologres Query Log Extention</Comment>
      <StateNumber>2429</StateNumber>
      <Version>6.0</Version>
      <SchemaName>pg_catalog</SchemaName>
      <SchemaId>11</SchemaId>
      <AvailableUpdates>2.0
3.0
4.0
5.0</AvailableUpdates>
    </extension>
    <extension id="123" parent="6" name="slpm">
      <ObjectId>32260</ObjectId>
      <Comment>schema-level permission model extensions</Comment>
      <StateNumber>2431</StateNumber>
      <Version>1.0</Version>
      <SchemaName>pg_catalog</SchemaName>
      <SchemaId>11</SchemaId>
    </extension>
    <extension id="124" parent="6" name="spm">
      <ObjectId>32294</ObjectId>
      <Comment>database-level simple privilege model extensions</Comment>
      <StateNumber>2433</StateNumber>
      <Version>1.0</Version>
      <SchemaName>pg_catalog</SchemaName>
      <SchemaId>11</SchemaId>
    </extension>
    <sequence id="125" parent="57" name="active_game_room_user_statistic_info_record_id_seq">
      <ObjectId>27748</ObjectId>
      <StateNumber>2097</StateNumber>
      <Owner>lr_statistic_hologres_developer</Owner>
      <SequenceIdentity>1</SequenceIdentity>
      <CacheSize>1</CacheSize>
      <DataType>bigint</DataType>
    </sequence>
    <sequence id="126" parent="57" name="active_recreation_room_user_statistic_info_record_id_seq">
      <ObjectId>27794</ObjectId>
      <StateNumber>2103</StateNumber>
      <Owner>lr_statistic_hologres_developer</Owner>
      <SequenceIdentity>1</SequenceIdentity>
      <CacheSize>1</CacheSize>
      <DataType>bigint</DataType>
    </sequence>
    <sequence id="127" parent="57" name="active_room_user_detail_info_statistic_recordid_seq">
      <ObjectId>574421</ObjectId>
      <StateNumber>113267</StateNumber>
      <Owner>lr_statistic_hologres_developer</Owner>
      <SequenceIdentity>1</SequenceIdentity>
      <CacheSize>1</CacheSize>
      <DataType>bigint</DataType>
    </sequence>
    <sequence id="128" parent="57" name="dissolved_game_room_info_record_record_id_seq">
      <ObjectId>27868</ObjectId>
      <StateNumber>2109</StateNumber>
      <Owner>lr_statistic_hologres_developer</Owner>
      <SequenceIdentity>1</SequenceIdentity>
      <CacheSize>1</CacheSize>
      <DataType>bigint</DataType>
    </sequence>
    <sequence id="129" parent="57" name="dissolved_recreation_room_info_record_record_id_seq">
      <ObjectId>27939</ObjectId>
      <StateNumber>2113</StateNumber>
      <Owner>lr_statistic_hologres_developer</Owner>
      <SequenceIdentity>1</SequenceIdentity>
      <CacheSize>1</CacheSize>
      <DataType>bigint</DataType>
    </sequence>
    <table id="130" parent="57" name="active_game_room_info">
      <ObjectId>27707</ObjectId>
      <Owner>lr_statistic_hologres_developer</Owner>
      <StateNumber>2093</StateNumber>
    </table>
    <table id="131" parent="57" name="active_game_room_user_statistic_info">
      <ObjectId>27731</ObjectId>
      <Owner>lr_statistic_hologres_developer</Owner>
      <StateNumber>2095</StateNumber>
    </table>
    <table id="132" parent="57" name="active_recreation_room_info">
      <ObjectId>27750</ObjectId>
      <Owner>lr_statistic_hologres_developer</Owner>
      <StateNumber>2099</StateNumber>
    </table>
    <table id="133" parent="57" name="active_recreation_room_user_statistic_info">
      <ObjectId>27778</ObjectId>
      <Owner>lr_statistic_hologres_developer</Owner>
      <StateNumber>2101</StateNumber>
    </table>
    <table id="134" parent="57" name="active_room_user_detail_info_statistic">
      <ObjectId>574423</ObjectId>
      <Owner>lr_statistic_hologres_developer</Owner>
      <StateNumber>113267</StateNumber>
    </table>
    <table id="135" parent="57" name="console_js_detail_dayly_da">
      <ObjectId>27796</ObjectId>
      <Comment>Please write your comments</Comment>
      <Owner>lr_statistic_hologres_developer</Owner>
      <StateNumber>2105</StateNumber>
    </table>
    <table id="136" parent="57" name="dissolved_game_room_info_record">
      <ObjectId>27802</ObjectId>
      <Owner>lr_statistic_hologres_developer</Owner>
      <StateNumber>2107</StateNumber>
    </table>
    <table id="137" parent="57" name="dissolved_recreation_room_info_record">
      <ObjectId>27870</ObjectId>
      <Owner>lr_statistic_hologres_developer</Owner>
      <StateNumber>2111</StateNumber>
    </table>
    <table id="138" parent="57" name="game_room_consecutive_start_statistic">
      <ObjectId>27941</ObjectId>
      <Comment>Please write your comments</Comment>
      <Owner>lr_statistic_hologres_developer</Owner>
      <StateNumber>2115</StateNumber>
    </table>
    <table id="139" parent="57" name="game_room_speaking_time_statistic">
      <ObjectId>27947</ObjectId>
      <Comment>Please write your comments</Comment>
      <Owner>lr_statistic_hologres_developer</Owner>
      <StateNumber>2117</StateNumber>
    </table>
    <table id="140" parent="57" name="game_room_user_event_record">
      <ObjectId>27953</ObjectId>
      <Comment>Please write your comments</Comment>
      <Owner>lr_statistic_hologres_developer</Owner>
      <StateNumber>2119</StateNumber>
    </table>
    <table id="141" parent="57" name="game_room_user_event_statistic">
      <ObjectId>27959</ObjectId>
      <Comment>Please write your comments</Comment>
      <Owner>lr_statistic_hologres_developer</Owner>
      <StateNumber>2121</StateNumber>
    </table>
    <routine id="142" parent="57" name="consistent_inner_join_no_recheck">
      <ObjectId>27429</ObjectId>
      <Owner>holo_admin</Owner>
      <StateNumber>1859</StateNumber>
      <VolatilityKind>volatile</VolatilityKind>
      <LanguageName>c</LanguageName>
      <RoutineKind>function</RoutineKind>
      <Strict>1</Strict>
      <Cost>1.0</Cost>
      <ConcurrencyKind>safe</ConcurrencyKind>
    </routine>
    <routine id="143" parent="57" name="gin_consistent_jsonb_holo">
      <ObjectId>27417</ObjectId>
      <Owner>holo_admin</Owner>
      <StateNumber>1859</StateNumber>
      <VolatilityKind>volatile</VolatilityKind>
      <LanguageName>c</LanguageName>
      <RoutineKind>function</RoutineKind>
      <Strict>1</Strict>
      <Cost>1.0</Cost>
      <ConcurrencyKind>safe</ConcurrencyKind>
    </routine>
    <routine id="144" parent="57" name="gin_extract_jsonb_holo">
      <ObjectId>27415</ObjectId>
      <Owner>holo_admin</Owner>
      <StateNumber>1859</StateNumber>
      <VolatilityKind>volatile</VolatilityKind>
      <LanguageName>c</LanguageName>
      <RoutineKind>function</RoutineKind>
      <Strict>1</Strict>
      <Cost>1.0</Cost>
      <ConcurrencyKind>safe</ConcurrencyKind>
    </routine>
    <routine id="145" parent="57" name="gin_extract_jsonb_holo_path">
      <ObjectId>27427</ObjectId>
      <Owner>holo_admin</Owner>
      <StateNumber>1859</StateNumber>
      <VolatilityKind>volatile</VolatilityKind>
      <LanguageName>c</LanguageName>
      <RoutineKind>function</RoutineKind>
      <Strict>1</Strict>
      <Cost>1.0</Cost>
      <ConcurrencyKind>safe</ConcurrencyKind>
    </routine>
    <routine id="146" parent="57" name="gin_extract_jsonb_query_holo">
      <ObjectId>27416</ObjectId>
      <Owner>holo_admin</Owner>
      <StateNumber>1859</StateNumber>
      <VolatilityKind>volatile</VolatilityKind>
      <LanguageName>c</LanguageName>
      <RoutineKind>function</RoutineKind>
      <Strict>1</Strict>
      <Cost>1.0</Cost>
      <ConcurrencyKind>safe</ConcurrencyKind>
    </routine>
    <routine id="147" parent="57" name="gin_extract_jsonb_query_holo_path">
      <ObjectId>27428</ObjectId>
      <Owner>holo_admin</Owner>
      <StateNumber>1859</StateNumber>
      <VolatilityKind>volatile</VolatilityKind>
      <LanguageName>c</LanguageName>
      <RoutineKind>function</RoutineKind>
      <Strict>1</Strict>
      <Cost>1.0</Cost>
      <ConcurrencyKind>safe</ConcurrencyKind>
    </routine>
    <routine id="148" parent="57" name="hg_remove_duplicated_pk">
      <ObjectId>26952</ObjectId>
      <Owner>holo_admin</Owner>
      <SourceTextLength>2967</SourceTextLength>
      <StateNumber>1847</StateNumber>
      <VolatilityKind>volatile</VolatilityKind>
      <ArgumentsDefinition>input_table_name text, condition_sql text DEFAULT &apos;&apos;::text</ArgumentsDefinition>
      <LanguageName>plpgsql</LanguageName>
      <RoutineKind>procedure</RoutineKind>
      <Cost>100.0</Cost>
    </routine>
    <routine id="149" parent="57" name="jsonb_object_agg_with_timestamp_cfunc">
      <ObjectId>27442</ObjectId>
      <Owner>holo_admin</Owner>
      <SourceTextLength>101</SourceTextLength>
      <StateNumber>1859</StateNumber>
      <VolatilityKind>volatile</VolatilityKind>
      <ArgumentsDefinition>bytea, bytea</ArgumentsDefinition>
      <ResultsDefinition>bytea</ResultsDefinition>
      <LanguageName>plpgsql</LanguageName>
      <RoutineKind>function</RoutineKind>
      <Cost>100.0</Cost>
      <ConcurrencyKind>safe</ConcurrencyKind>
    </routine>
    <routine id="150" parent="57" name="jsonb_object_agg_with_timestamp_ffunc">
      <ObjectId>27443</ObjectId>
      <Owner>holo_admin</Owner>
      <SourceTextLength>101</SourceTextLength>
      <StateNumber>1859</StateNumber>
      <VolatilityKind>volatile</VolatilityKind>
      <ArgumentsDefinition>bytea</ArgumentsDefinition>
      <ResultsDefinition>jsonb</ResultsDefinition>
      <LanguageName>plpgsql</LanguageName>
      <RoutineKind>function</RoutineKind>
      <Cost>100.0</Cost>
      <ConcurrencyKind>safe</ConcurrencyKind>
    </routine>
    <routine id="151" parent="57" name="jsonb_object_agg_with_timestamp_sfunc">
      <ObjectId>27441</ObjectId>
      <Owner>holo_admin</Owner>
      <SourceTextLength>101</SourceTextLength>
      <StateNumber>1859</StateNumber>
      <VolatilityKind>volatile</VolatilityKind>
      <ArgumentsDefinition>bytea, jsonb, timestamp with time zone</ArgumentsDefinition>
      <ResultsDefinition>bytea</ResultsDefinition>
      <LanguageName>plpgsql</LanguageName>
      <RoutineKind>function</RoutineKind>
      <Cost>100.0</Cost>
      <ConcurrencyKind>safe</ConcurrencyKind>
    </routine>
    <routine id="152" parent="57" name="update_instance_settings">
      <ObjectId>26949</ObjectId>
      <Owner>holo_admin</Owner>
      <StateNumber>1847</StateNumber>
      <VolatilityKind>stable</VolatilityKind>
      <LanguageName>c</LanguageName>
      <RoutineKind>function</RoutineKind>
      <Strict>1</Strict>
      <Cost>1.0</Cost>
    </routine>
    <aggregate id="153" parent="57" name="jsonb_object_agg_with_timestamp">
      <ObjectId>27444</ObjectId>
      <Owner>holo_admin</Owner>
      <StateNumber>1859</StateNumber>
      <Transition>public.jsonb_object_agg_with_timestamp_sfunc</Transition>
      <TransitionId>27441</TransitionId>
      <Final>public.jsonb_object_agg_with_timestamp_ffunc</Final>
      <FinalId>27443</FinalId>
      <TransitionDataType>bytea|0s</TransitionDataType>
      <Combine>public.jsonb_object_agg_with_timestamp_cfunc</Combine>
      <CombineId>27442</CombineId>
    </aggregate>
    <server id="154" parent="61" name="odps_server">
      <ObjectId>27597</ObjectId>
      <StateNumber>2075</StateNumber>
      <Owner>holo_admin</Owner>
      <Options>endpoint=http://service.cn-hangzhou.maxcompute.aliyun-inc.com/api</Options>
    </server>
    <server id="155" parent="62" name="hg_cron_server">
      <ObjectId>35087</ObjectId>
      <StateNumber>2762</StateNumber>
      <Owner>lr_statistic_hologres_developer</Owner>
      <Options>server_type=hg_cron_server</Options>
    </server>
    <server id="156" parent="62" name="meta_warehouse_server">
      <ObjectId>27478</ObjectId>
      <StateNumber>1869</StateNumber>
      <Owner>holo_admin</Owner>
      <Options>server_type=meta_warehouse</Options>
    </server>
    <routine id="157" parent="94" name="consistent_inner_join_no_recheck">
      <ObjectId>32165</ObjectId>
      <Owner>holo_admin</Owner>
      <StateNumber>2419</StateNumber>
      <VolatilityKind>volatile</VolatilityKind>
      <LanguageName>c</LanguageName>
      <RoutineKind>function</RoutineKind>
      <Strict>1</Strict>
      <Cost>1.0</Cost>
      <ConcurrencyKind>safe</ConcurrencyKind>
    </routine>
    <routine id="158" parent="94" name="gin_consistent_jsonb_holo">
      <ObjectId>32153</ObjectId>
      <Owner>holo_admin</Owner>
      <StateNumber>2419</StateNumber>
      <VolatilityKind>volatile</VolatilityKind>
      <LanguageName>c</LanguageName>
      <RoutineKind>function</RoutineKind>
      <Strict>1</Strict>
      <Cost>1.0</Cost>
      <ConcurrencyKind>safe</ConcurrencyKind>
    </routine>
    <routine id="159" parent="94" name="gin_extract_jsonb_holo">
      <ObjectId>32151</ObjectId>
      <Owner>holo_admin</Owner>
      <StateNumber>2419</StateNumber>
      <VolatilityKind>volatile</VolatilityKind>
      <LanguageName>c</LanguageName>
      <RoutineKind>function</RoutineKind>
      <Strict>1</Strict>
      <Cost>1.0</Cost>
      <ConcurrencyKind>safe</ConcurrencyKind>
    </routine>
    <routine id="160" parent="94" name="gin_extract_jsonb_holo_path">
      <ObjectId>32163</ObjectId>
      <Owner>holo_admin</Owner>
      <StateNumber>2419</StateNumber>
      <VolatilityKind>volatile</VolatilityKind>
      <LanguageName>c</LanguageName>
      <RoutineKind>function</RoutineKind>
      <Strict>1</Strict>
      <Cost>1.0</Cost>
      <ConcurrencyKind>safe</ConcurrencyKind>
    </routine>
    <routine id="161" parent="94" name="gin_extract_jsonb_query_holo">
      <ObjectId>32152</ObjectId>
      <Owner>holo_admin</Owner>
      <StateNumber>2419</StateNumber>
      <VolatilityKind>volatile</VolatilityKind>
      <LanguageName>c</LanguageName>
      <RoutineKind>function</RoutineKind>
      <Strict>1</Strict>
      <Cost>1.0</Cost>
      <ConcurrencyKind>safe</ConcurrencyKind>
    </routine>
    <routine id="162" parent="94" name="gin_extract_jsonb_query_holo_path">
      <ObjectId>32164</ObjectId>
      <Owner>holo_admin</Owner>
      <StateNumber>2419</StateNumber>
      <VolatilityKind>volatile</VolatilityKind>
      <LanguageName>c</LanguageName>
      <RoutineKind>function</RoutineKind>
      <Strict>1</Strict>
      <Cost>1.0</Cost>
      <ConcurrencyKind>safe</ConcurrencyKind>
    </routine>
    <routine id="163" parent="94" name="hg_remove_duplicated_pk">
      <ObjectId>31687</ObjectId>
      <Owner>holo_admin</Owner>
      <SourceTextLength>2967</SourceTextLength>
      <StateNumber>2407</StateNumber>
      <VolatilityKind>volatile</VolatilityKind>
      <ArgumentsDefinition>input_table_name text, condition_sql text DEFAULT &apos;&apos;::text</ArgumentsDefinition>
      <LanguageName>plpgsql</LanguageName>
      <RoutineKind>procedure</RoutineKind>
      <Cost>100.0</Cost>
    </routine>
    <routine id="164" parent="94" name="jsonb_object_agg_with_timestamp_cfunc">
      <ObjectId>32178</ObjectId>
      <Owner>holo_admin</Owner>
      <SourceTextLength>101</SourceTextLength>
      <StateNumber>2419</StateNumber>
      <VolatilityKind>volatile</VolatilityKind>
      <ArgumentsDefinition>bytea, bytea</ArgumentsDefinition>
      <ResultsDefinition>bytea</ResultsDefinition>
      <LanguageName>plpgsql</LanguageName>
      <RoutineKind>function</RoutineKind>
      <Cost>100.0</Cost>
      <ConcurrencyKind>safe</ConcurrencyKind>
    </routine>
    <routine id="165" parent="94" name="jsonb_object_agg_with_timestamp_ffunc">
      <ObjectId>32179</ObjectId>
      <Owner>holo_admin</Owner>
      <SourceTextLength>101</SourceTextLength>
      <StateNumber>2419</StateNumber>
      <VolatilityKind>volatile</VolatilityKind>
      <ArgumentsDefinition>bytea</ArgumentsDefinition>
      <ResultsDefinition>jsonb</ResultsDefinition>
      <LanguageName>plpgsql</LanguageName>
      <RoutineKind>function</RoutineKind>
      <Cost>100.0</Cost>
      <ConcurrencyKind>safe</ConcurrencyKind>
    </routine>
    <routine id="166" parent="94" name="jsonb_object_agg_with_timestamp_sfunc">
      <ObjectId>32177</ObjectId>
      <Owner>holo_admin</Owner>
      <SourceTextLength>101</SourceTextLength>
      <StateNumber>2419</StateNumber>
      <VolatilityKind>volatile</VolatilityKind>
      <ArgumentsDefinition>bytea, jsonb, timestamp with time zone</ArgumentsDefinition>
      <ResultsDefinition>bytea</ResultsDefinition>
      <LanguageName>plpgsql</LanguageName>
      <RoutineKind>function</RoutineKind>
      <Cost>100.0</Cost>
      <ConcurrencyKind>safe</ConcurrencyKind>
    </routine>
    <routine id="167" parent="94" name="update_instance_settings">
      <ObjectId>31684</ObjectId>
      <Owner>holo_admin</Owner>
      <StateNumber>2407</StateNumber>
      <VolatilityKind>stable</VolatilityKind>
      <LanguageName>c</LanguageName>
      <RoutineKind>function</RoutineKind>
      <Strict>1</Strict>
      <Cost>1.0</Cost>
    </routine>
    <aggregate id="168" parent="94" name="jsonb_object_agg_with_timestamp">
      <ObjectId>32180</ObjectId>
      <Owner>holo_admin</Owner>
      <StateNumber>2419</StateNumber>
      <Transition>public.jsonb_object_agg_with_timestamp_sfunc</Transition>
      <TransitionId>32177</TransitionId>
      <Final>public.jsonb_object_agg_with_timestamp_ffunc</Final>
      <FinalId>32179</FinalId>
      <TransitionDataType>bytea|0s</TransitionDataType>
      <Combine>public.jsonb_object_agg_with_timestamp_cfunc</Combine>
      <CombineId>32178</CombineId>
    </aggregate>
    <server id="169" parent="98" name="odps_server">
      <ObjectId>32333</ObjectId>
      <StateNumber>2495</StateNumber>
      <Owner>holo_admin</Owner>
      <Options>endpoint=http://service.cn-hangzhou.maxcompute.aliyun-inc.com/api</Options>
    </server>
    <server id="170" parent="99" name="hg_cron_server">
      <ObjectId>34981</ObjectId>
      <StateNumber>2756</StateNumber>
      <Owner>holo_admin</Owner>
      <Options>server_type=hg_cron_server</Options>
    </server>
    <server id="171" parent="99" name="meta_warehouse_server">
      <ObjectId>32214</ObjectId>
      <StateNumber>2429</StateNumber>
      <Owner>holo_admin</Owner>
      <Options>server_type=meta_warehouse</Options>
    </server>
    <column id="172" parent="130" name="room_num">
      <Position>1</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1916</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="173" parent="130" name="unique_room_key">
      <Position>2</Position>
      <DataType>varchar(255)|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1916</StateNumber>
      <TypeId>1043</TypeId>
    </column>
    <column id="174" parent="130" name="pass_word_type">
      <Position>3</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1916</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="175" parent="130" name="game_mode_type">
      <Position>4</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1916</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="176" parent="130" name="game_room_type">
      <Position>5</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1916</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="177" parent="130" name="member_count">
      <Position>6</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1916</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="178" parent="130" name="game_status">
      <Position>7</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1916</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="179" parent="130" name="has_big_rmb_member">
      <Position>8</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1916</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="180" parent="130" name="has_daily_new_member">
      <Position>9</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1916</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="181" parent="130" name="has_weekly_new_member">
      <Position>10</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1916</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="182" parent="130" name="big_rmb_member_user_id_list">
      <Position>11</Position>
      <DataType>text|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1916</StateNumber>
      <DefaultExpression>&apos;&apos;::text</DefaultExpression>
      <TypeId>25</TypeId>
    </column>
    <column id="183" parent="130" name="daily_new_member_user_id_list">
      <Position>12</Position>
      <DataType>text|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1916</StateNumber>
      <DefaultExpression>&apos;&apos;::text</DefaultExpression>
      <TypeId>25</TypeId>
    </column>
    <column id="184" parent="130" name="weekly_new_member_user_id_list">
      <Position>13</Position>
      <DataType>text|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1916</StateNumber>
      <DefaultExpression>&apos;&apos;::text</DefaultExpression>
      <TypeId>25</TypeId>
    </column>
    <column id="185" parent="130" name="level_limit">
      <Position>14</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1916</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="186" parent="130" name="average_level">
      <Position>15</Position>
      <DataType>varchar(255)|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1916</StateNumber>
      <DefaultExpression>&apos;0&apos;::character varying</DefaultExpression>
      <TypeId>1043</TypeId>
    </column>
    <column id="187" parent="130" name="average_game_count">
      <Position>16</Position>
      <DataType>varchar(255)|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1916</StateNumber>
      <DefaultExpression>&apos;0&apos;::character varying</DefaultExpression>
      <TypeId>1043</TypeId>
    </column>
    <column id="188" parent="130" name="average_level_map">
      <Position>17</Position>
      <DataType>text|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1916</StateNumber>
      <DefaultExpression>&apos;&apos;::text</DefaultExpression>
      <TypeId>25</TypeId>
    </column>
    <column id="189" parent="130" name="average_game_count_map">
      <Position>18</Position>
      <DataType>text|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1916</StateNumber>
      <DefaultExpression>&apos;&apos;::text</DefaultExpression>
      <TypeId>25</TypeId>
    </column>
    <column id="190" parent="130" name="created_on">
      <Position>19</Position>
      <DataType>timestamp|0s</DataType>
      <StateNumber>1916</StateNumber>
      <TypeId>1114</TypeId>
    </column>
    <column id="191" parent="130" name="created_by">
      <Position>20</Position>
      <DataType>bigint|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1916</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>20</TypeId>
    </column>
    <index id="192" parent="130" name="active_game_room_info_pkey">
      <ObjectId>27981</ObjectId>
      <StateNumber>2041</StateNumber>
      <ColNames>room_num</ColNames>
      <Unique>1</Unique>
      <Primary>1</Primary>
    </index>
    <key id="193" parent="130" name="active_game_room_info_pkey">
      <ObjectId>27982</ObjectId>
      <StateNumber>2041</StateNumber>
      <ColNames>room_num</ColNames>
      <Primary>1</Primary>
      <UnderlyingIndexName>active_game_room_info_pkey</UnderlyingIndexName>
    </key>
    <column id="194" parent="131" name="record_id">
      <Position>1</Position>
      <DataType>bigint|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>2024</StateNumber>
      <DefaultExpression>nextval(&apos;public.active_game_room_user_statistic_info_record_id_seq&apos;::regclass)</DefaultExpression>
      <TypeId>20</TypeId>
    </column>
    <column id="195" parent="131" name="user_id">
      <Position>2</Position>
      <DataType>bigint|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1918</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>20</TypeId>
    </column>
    <column id="196" parent="131" name="unique_room_key">
      <Position>3</Position>
      <DataType>varchar(255)|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1918</StateNumber>
      <TypeId>1043</TypeId>
    </column>
    <column id="197" parent="131" name="user_feature">
      <Position>4</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1918</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="198" parent="131" name="user_level">
      <Position>5</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1918</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="199" parent="131" name="user_game_num">
      <Position>6</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1918</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="200" parent="131" name="join_times">
      <Position>7</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1918</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="201" parent="131" name="instantly_quit_times">
      <Position>8</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1918</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="202" parent="131" name="be_kicked_times">
      <Position>9</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1918</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="203" parent="131" name="effective_stay_times">
      <Position>10</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1918</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="204" parent="131" name="total_stay_time">
      <Position>11</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1918</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="205" parent="131" name="start_game_times">
      <Position>12</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1918</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="206" parent="131" name="last_join_room_time">
      <Position>13</Position>
      <DataType>bigint|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1918</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>20</TypeId>
    </column>
    <column id="207" parent="131" name="total_voice_time">
      <Position>14</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1918</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="208" parent="131" name="now_in_room">
      <Position>15</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1918</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="209" parent="131" name="created_on">
      <Position>16</Position>
      <DataType>timestamp|0s</DataType>
      <StateNumber>1918</StateNumber>
      <TypeId>1114</TypeId>
    </column>
    <column id="210" parent="131" name="created_by">
      <Position>17</Position>
      <DataType>bigint|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1918</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>20</TypeId>
    </column>
    <index id="211" parent="131" name="active_game_room_user_statistic_info_pkey">
      <ObjectId>27983</ObjectId>
      <StateNumber>2042</StateNumber>
      <ColNames>record_id</ColNames>
      <Unique>1</Unique>
      <Primary>1</Primary>
    </index>
    <key id="212" parent="131" name="active_game_room_user_statistic_info_pkey">
      <ObjectId>27984</ObjectId>
      <StateNumber>2042</StateNumber>
      <ColNames>record_id</ColNames>
      <Primary>1</Primary>
      <UnderlyingIndexName>active_game_room_user_statistic_info_pkey</UnderlyingIndexName>
    </key>
    <column id="213" parent="132" name="room_num">
      <Position>1</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1923</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="214" parent="132" name="unique_room_key">
      <Position>2</Position>
      <DataType>varchar(255)|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1923</StateNumber>
      <TypeId>1043</TypeId>
    </column>
    <column id="215" parent="132" name="pass_word_type">
      <Position>3</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1923</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="216" parent="132" name="room_theme">
      <Position>4</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1923</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="217" parent="132" name="room_tag">
      <Position>5</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1923</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="218" parent="132" name="member_count">
      <Position>6</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1923</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="219" parent="132" name="tiny_game_type">
      <Position>7</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1923</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="220" parent="132" name="red_bag_switch">
      <Position>8</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1923</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="221" parent="132" name="travel_world_switch">
      <Position>9</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1923</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="222" parent="132" name="music_switch">
      <Position>10</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1923</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="223" parent="132" name="lucy_draw_switch">
      <Position>11</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1923</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="224" parent="132" name="palpitate_switch">
      <Position>12</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1923</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="225" parent="132" name="has_big_rmb_member">
      <Position>13</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1923</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="226" parent="132" name="has_daily_new_member">
      <Position>14</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1923</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="227" parent="132" name="has_weekly_new_member">
      <Position>15</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1923</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="228" parent="132" name="big_rmb_member_user_id_list">
      <Position>16</Position>
      <DataType>text|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1923</StateNumber>
      <DefaultExpression>&apos;&apos;::text</DefaultExpression>
      <TypeId>25</TypeId>
    </column>
    <column id="229" parent="132" name="daily_new_member_user_id_list">
      <Position>17</Position>
      <DataType>text|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1923</StateNumber>
      <DefaultExpression>&apos;&apos;::text</DefaultExpression>
      <TypeId>25</TypeId>
    </column>
    <column id="230" parent="132" name="weekly_new_member_user_id_list">
      <Position>18</Position>
      <DataType>text|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1923</StateNumber>
      <DefaultExpression>&apos;&apos;::text</DefaultExpression>
      <TypeId>25</TypeId>
    </column>
    <column id="231" parent="132" name="room_layout">
      <Position>19</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1923</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="232" parent="132" name="average_popularity">
      <Position>20</Position>
      <DataType>varchar(255)|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1923</StateNumber>
      <DefaultExpression>&apos;0&apos;::character varying</DefaultExpression>
      <TypeId>1043</TypeId>
    </column>
    <column id="233" parent="132" name="average_popularity_map">
      <Position>21</Position>
      <DataType>text|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1923</StateNumber>
      <DefaultExpression>&apos;&apos;::text</DefaultExpression>
      <TypeId>25</TypeId>
    </column>
    <column id="234" parent="132" name="cost_golden_coin_in_time">
      <Position>22</Position>
      <DataType>numeric(18,3 digit)|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1923</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>1700</TypeId>
    </column>
    <column id="235" parent="132" name="created_on">
      <Position>23</Position>
      <DataType>timestamp|0s</DataType>
      <StateNumber>1923</StateNumber>
      <TypeId>1114</TypeId>
    </column>
    <column id="236" parent="132" name="created_by">
      <Position>24</Position>
      <DataType>bigint|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1923</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>20</TypeId>
    </column>
    <index id="237" parent="132" name="active_recreation_room_info_pkey">
      <ObjectId>27985</ObjectId>
      <StateNumber>2043</StateNumber>
      <ColNames>room_num</ColNames>
      <Unique>1</Unique>
      <Primary>1</Primary>
    </index>
    <key id="238" parent="132" name="active_recreation_room_info_pkey">
      <ObjectId>27986</ObjectId>
      <StateNumber>2043</StateNumber>
      <ColNames>room_num</ColNames>
      <Primary>1</Primary>
      <UnderlyingIndexName>active_recreation_room_info_pkey</UnderlyingIndexName>
    </key>
    <column id="239" parent="133" name="record_id">
      <Position>1</Position>
      <DataType>bigint|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>2025</StateNumber>
      <DefaultExpression>nextval(&apos;public.active_recreation_room_user_statistic_info_record_id_seq&apos;::regclass)</DefaultExpression>
      <TypeId>20</TypeId>
    </column>
    <column id="240" parent="133" name="user_id">
      <Position>2</Position>
      <DataType>bigint|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1925</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>20</TypeId>
    </column>
    <column id="241" parent="133" name="unique_room_key">
      <Position>3</Position>
      <DataType>varchar(255)|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1925</StateNumber>
      <TypeId>1043</TypeId>
    </column>
    <column id="242" parent="133" name="user_feature">
      <Position>4</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1925</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="243" parent="133" name="user_popularity">
      <Position>5</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1925</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="244" parent="133" name="join_times">
      <Position>6</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1925</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="245" parent="133" name="instantly_quit_times">
      <Position>7</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1925</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="246" parent="133" name="be_kicked_times">
      <Position>8</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1925</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="247" parent="133" name="effective_stay_times">
      <Position>9</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1925</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="248" parent="133" name="total_stay_time">
      <Position>10</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1925</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="249" parent="133" name="last_join_room_time">
      <Position>11</Position>
      <DataType>bigint|0s</DataType>
      <StateNumber>1925</StateNumber>
      <TypeId>20</TypeId>
    </column>
    <column id="250" parent="133" name="total_voice_time">
      <Position>12</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1925</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="251" parent="133" name="total_text_count">
      <Position>13</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1925</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="252" parent="133" name="total_cost_golden_coin_count">
      <Position>14</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1925</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="253" parent="133" name="now_in_room">
      <Position>15</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1925</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="254" parent="133" name="created_on">
      <Position>16</Position>
      <DataType>timestamp|0s</DataType>
      <StateNumber>1925</StateNumber>
      <TypeId>1114</TypeId>
    </column>
    <column id="255" parent="133" name="created_by">
      <Position>17</Position>
      <DataType>bigint|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1925</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>20</TypeId>
    </column>
    <index id="256" parent="133" name="active_recreation_room_user_statistic_info_pkey">
      <ObjectId>27987</ObjectId>
      <StateNumber>2044</StateNumber>
      <ColNames>record_id</ColNames>
      <Unique>1</Unique>
      <Primary>1</Primary>
    </index>
    <key id="257" parent="133" name="active_recreation_room_user_statistic_info_pkey">
      <ObjectId>27988</ObjectId>
      <StateNumber>2044</StateNumber>
      <ColNames>record_id</ColNames>
      <Primary>1</Primary>
      <UnderlyingIndexName>active_recreation_room_user_statistic_info_pkey</UnderlyingIndexName>
    </key>
    <column id="258" parent="134" name="recordid">
      <Position>1</Position>
      <DataType>bigint|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>113267</StateNumber>
      <DefaultExpression>nextval(&apos;active_room_user_detail_info_statistic_recordid_seq&apos;::regclass)</DefaultExpression>
      <TypeId>20</TypeId>
    </column>
    <column id="259" parent="134" name="uniqueroomkey">
      <Position>2</Position>
      <DataType>varchar(255)|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>113267</StateNumber>
      <TypeId>1043</TypeId>
    </column>
    <column id="260" parent="134" name="userid">
      <Position>3</Position>
      <DataType>bigint|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>113267</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>20</TypeId>
    </column>
    <column id="261" parent="134" name="roomkey">
      <Position>4</Position>
      <DataType>varchar(255)|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>113267</StateNumber>
      <DefaultExpression>&apos;&apos;::character varying</DefaultExpression>
      <TypeId>1043</TypeId>
    </column>
    <column id="262" parent="134" name="stayduration">
      <Position>5</Position>
      <DataType>bigint|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>113267</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>20</TypeId>
    </column>
    <column id="263" parent="134" name="joinroomtime">
      <Position>6</Position>
      <DataType>timestamp|0s</DataType>
      <StateNumber>113267</StateNumber>
      <TypeId>1114</TypeId>
    </column>
    <column id="264" parent="134" name="quitroomtime">
      <Position>7</Position>
      <DataType>timestamp|0s</DataType>
      <StateNumber>113267</StateNumber>
      <TypeId>1114</TypeId>
    </column>
    <column id="265" parent="134" name="changestatus">
      <Position>8</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>113267</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="266" parent="134" name="createdby">
      <Position>9</Position>
      <DataType>bigint|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>113267</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>20</TypeId>
    </column>
    <column id="267" parent="134" name="createdon">
      <Position>10</Position>
      <DataType>timestamp|0s</DataType>
      <StateNumber>113267</StateNumber>
      <TypeId>1114</TypeId>
    </column>
    <column id="268" parent="134" name="modifiedby">
      <Position>11</Position>
      <DataType>bigint|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>113267</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>20</TypeId>
    </column>
    <column id="269" parent="134" name="modifiedon">
      <Position>12</Position>
      <DataType>timestamp|0s</DataType>
      <StateNumber>113267</StateNumber>
      <TypeId>1114</TypeId>
    </column>
    <index id="270" parent="134" name="active_room_user_detail_info_statistic_pkey">
      <ObjectId>574436</ObjectId>
      <StateNumber>113267</StateNumber>
      <ColNames>recordid</ColNames>
      <Unique>1</Unique>
      <Primary>1</Primary>
    </index>
    <key id="271" parent="134" name="active_room_user_detail_info_statistic_pkey">
      <ObjectId>574437</ObjectId>
      <StateNumber>113267</StateNumber>
      <ColNames>recordid</ColNames>
      <Primary>1</Primary>
      <UnderlyingIndexName>active_room_user_detail_info_statistic_pkey</UnderlyingIndexName>
    </key>
    <column id="272" parent="135" name="user_id">
      <Position>1</Position>
      <Comment>用户ID</Comment>
      <DataType>bigint|0s</DataType>
      <StateNumber>1930</StateNumber>
      <TypeId>20</TypeId>
    </column>
    <column id="273" parent="135" name="event_name">
      <Position>2</Position>
      <Comment>事件名称：join_room-进房 werewolf_game_start-开局 werewolf_game_over 完局 quit_room-退房</Comment>
      <DataType>text|0s</DataType>
      <StateNumber>1930</StateNumber>
      <TypeId>25</TypeId>
    </column>
    <column id="274" parent="135" name="event_time">
      <Position>3</Position>
      <Comment>事件时间</Comment>
      <DataType>timestamp with time zone|0s</DataType>
      <StateNumber>1930</StateNumber>
      <TypeId>1184</TypeId>
    </column>
    <column id="275" parent="135" name="room_key">
      <Position>4</Position>
      <Comment>房号</Comment>
      <DataType>text|0s</DataType>
      <StateNumber>1930</StateNumber>
      <TypeId>25</TypeId>
    </column>
    <column id="276" parent="135" name="room_value">
      <Position>5</Position>
      <DataType>text|0s</DataType>
      <StateNumber>1930</StateNumber>
      <TypeId>25</TypeId>
    </column>
    <column id="277" parent="135" name="event_value">
      <Position>6</Position>
      <DataType>text|0s</DataType>
      <StateNumber>1930</StateNumber>
      <TypeId>25</TypeId>
    </column>
    <column id="278" parent="135" name="gameroomtype">
      <Position>7</Position>
      <Comment>房间类型</Comment>
      <DataType>text|0s</DataType>
      <StateNumber>1930</StateNumber>
      <TypeId>25</TypeId>
    </column>
    <column id="279" parent="135" name="traceid">
      <Position>8</Position>
      <DataType>text|0s</DataType>
      <StateNumber>1930</StateNumber>
      <TypeId>25</TypeId>
    </column>
    <column id="280" parent="135" name="quittype">
      <Position>9</Position>
      <Comment>退房类型：0-主动；1-被踢</Comment>
      <DataType>text|0s</DataType>
      <StateNumber>1930</StateNumber>
      <TypeId>25</TypeId>
    </column>
    <column id="281" parent="135" name="kicktype">
      <Position>10</Position>
      <Comment>被踢类型：0-被房主踢</Comment>
      <DataType>text|0s</DataType>
      <StateNumber>1930</StateNumber>
      <TypeId>25</TypeId>
    </column>
    <column id="282" parent="135" name="gameuuid">
      <Position>11</Position>
      <DataType>text|0s</DataType>
      <StateNumber>1930</StateNumber>
      <TypeId>25</TypeId>
    </column>
    <column id="283" parent="135" name="gamelevel">
      <Position>12</Position>
      <Comment>用户等级</Comment>
      <DataType>text|0s</DataType>
      <StateNumber>1930</StateNumber>
      <TypeId>25</TypeId>
    </column>
    <column id="284" parent="135" name="gameroomlevel">
      <Position>13</Position>
      <Comment>房间等级限制</Comment>
      <DataType>text|0s</DataType>
      <StateNumber>1930</StateNumber>
      <TypeId>25</TypeId>
    </column>
    <column id="285" parent="135" name="passwordroom">
      <Position>14</Position>
      <Comment>是否密码房：True/False</Comment>
      <DataType>text|0s</DataType>
      <StateNumber>1930</StateNumber>
      <TypeId>25</TypeId>
    </column>
    <column id="286" parent="135" name="createdon">
      <Position>15</Position>
      <Comment>数据生成时间</Comment>
      <DataType>timestamp with time zone|0s</DataType>
      <StateNumber>1930</StateNumber>
      <TypeId>1184</TypeId>
    </column>
    <column id="287" parent="136" name="record_id">
      <Position>1</Position>
      <DataType>bigint|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>2026</StateNumber>
      <DefaultExpression>nextval(&apos;public.dissolved_game_room_info_record_record_id_seq&apos;::regclass)</DefaultExpression>
      <TypeId>20</TypeId>
    </column>
    <column id="288" parent="136" name="unique_room_key">
      <Position>2</Position>
      <DataType>varchar(255)|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <TypeId>1043</TypeId>
    </column>
    <column id="289" parent="136" name="room_num">
      <Position>3</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="290" parent="136" name="game_mode_type">
      <Position>4</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="291" parent="136" name="game_room_type">
      <Position>5</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="292" parent="136" name="initial_owner_id">
      <Position>6</Position>
      <DataType>bigint|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>20</TypeId>
    </column>
    <column id="293" parent="136" name="owner_changed_people_count">
      <Position>7</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="294" parent="136" name="owner_changed_times">
      <Position>8</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="295" parent="136" name="join_people_count_total">
      <Position>9</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="296" parent="136" name="join_times_total">
      <Position>10</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="297" parent="136" name="join_people_count_big_rmb">
      <Position>11</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="298" parent="136" name="join_times_big_rmb">
      <Position>12</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="299" parent="136" name="join_people_count_daily_new">
      <Position>13</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="300" parent="136" name="join_times_daily_new">
      <Position>14</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="301" parent="136" name="join_people_count_weekly_new">
      <Position>15</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="302" parent="136" name="join_times_weekly_new">
      <Position>16</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="303" parent="136" name="instantly_quit_people_count_total">
      <Position>17</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="304" parent="136" name="instantly_quit_times_total">
      <Position>18</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="305" parent="136" name="instantly_quit_people_count_big_rmb">
      <Position>19</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="306" parent="136" name="instantly_quit_times_big_rmb">
      <Position>20</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="307" parent="136" name="instantly_quit_people_count_daily_new">
      <Position>21</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="308" parent="136" name="instantly_quit_times_daily_new">
      <Position>22</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="309" parent="136" name="instantly_quit_people_count_weekly_new">
      <Position>23</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="310" parent="136" name="instantly_quit_times_weekly_new">
      <Position>24</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="311" parent="136" name="effective_stay_people_count_total">
      <Position>25</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="312" parent="136" name="effective_stay_times_total">
      <Position>26</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="313" parent="136" name="effective_stay_people_count_big_rmb">
      <Position>27</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="314" parent="136" name="effective_stay_times_big_rmb">
      <Position>28</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="315" parent="136" name="effective_stay_people_count_daily_new">
      <Position>29</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="316" parent="136" name="effective_stay_times_daily_new">
      <Position>30</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="317" parent="136" name="effective_stay_people_count_weekly_new">
      <Position>31</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="318" parent="136" name="effective_stay_times_weekly_new">
      <Position>32</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="319" parent="136" name="total_stay_time_total">
      <Position>33</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="320" parent="136" name="total_stay_time_big_rmb">
      <Position>34</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="321" parent="136" name="total_stay_time_daily_new">
      <Position>35</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="322" parent="136" name="total_stay_time_weekly_new">
      <Position>36</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="323" parent="136" name="be_kicked_people_count_total">
      <Position>37</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="324" parent="136" name="be_kicked_times_total">
      <Position>38</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="325" parent="136" name="be_kicked_people_count_big_rmb">
      <Position>39</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="326" parent="136" name="be_kicked_times_big_rmb">
      <Position>40</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="327" parent="136" name="be_kicked_people_count_daily_new">
      <Position>41</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="328" parent="136" name="be_kicked_times_daily_new">
      <Position>42</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="329" parent="136" name="be_kicked_people_count_weekly_new">
      <Position>43</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="330" parent="136" name="be_kicked_times_weekly_new">
      <Position>44</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="331" parent="136" name="start_game_people_count_total">
      <Position>45</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="332" parent="136" name="start_game_times_total">
      <Position>46</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="333" parent="136" name="start_game_people_count_big_rmb">
      <Position>47</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="334" parent="136" name="start_game_times_big_rmb">
      <Position>48</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="335" parent="136" name="start_game_people_count_daily_new">
      <Position>49</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="336" parent="136" name="start_game_times_daily_new">
      <Position>50</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="337" parent="136" name="start_game_people_count_weekly_new">
      <Position>51</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="338" parent="136" name="start_game_times_weekly_new">
      <Position>52</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="339" parent="136" name="multiple_times_game_people_count_total">
      <Position>53</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="340" parent="136" name="multiple_times_game_people_count_big_rmb">
      <Position>54</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="341" parent="136" name="multiple_times_game_people_count_daily_new">
      <Position>55</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="342" parent="136" name="multiple_times_game_people_count_weekly_new">
      <Position>56</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="343" parent="136" name="has_spoken_people_count_total">
      <Position>57</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="344" parent="136" name="has_spoken_people_count_big_rmb">
      <Position>58</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="345" parent="136" name="has_spoken_people_count_daily_new">
      <Position>59</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="346" parent="136" name="has_spoken_people_count_weekly_new">
      <Position>60</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="347" parent="136" name="total_voice_time_total">
      <Position>61</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="348" parent="136" name="total_voice_time_big_rmb">
      <Position>62</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="349" parent="136" name="total_voice_time_daily_new">
      <Position>63</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="350" parent="136" name="total_voice_time_weekly_new">
      <Position>64</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="351" parent="136" name="room_created_on">
      <Position>65</Position>
      <DataType>timestamp|0s</DataType>
      <StateNumber>1944</StateNumber>
      <TypeId>1114</TypeId>
    </column>
    <column id="352" parent="136" name="created_on">
      <Position>66</Position>
      <DataType>timestamp|0s</DataType>
      <StateNumber>1944</StateNumber>
      <TypeId>1114</TypeId>
    </column>
    <column id="353" parent="136" name="created_by">
      <Position>67</Position>
      <DataType>bigint|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1944</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>20</TypeId>
    </column>
    <index id="354" parent="136" name="dissolved_game_room_info_record_pkey">
      <ObjectId>27989</ObjectId>
      <StateNumber>2045</StateNumber>
      <ColNames>unique_room_key</ColNames>
      <Unique>1</Unique>
      <Primary>1</Primary>
    </index>
    <key id="355" parent="136" name="dissolved_game_room_info_record_pkey">
      <ObjectId>27990</ObjectId>
      <StateNumber>2045</StateNumber>
      <ColNames>unique_room_key</ColNames>
      <Primary>1</Primary>
      <UnderlyingIndexName>dissolved_game_room_info_record_pkey</UnderlyingIndexName>
    </key>
    <column id="356" parent="137" name="record_id">
      <Position>1</Position>
      <DataType>bigint|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>2027</StateNumber>
      <DefaultExpression>nextval(&apos;public.dissolved_recreation_room_info_record_record_id_seq&apos;::regclass)</DefaultExpression>
      <TypeId>20</TypeId>
    </column>
    <column id="357" parent="137" name="unique_room_key">
      <Position>2</Position>
      <DataType>varchar(255)|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <TypeId>1043</TypeId>
    </column>
    <column id="358" parent="137" name="room_num">
      <Position>3</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="359" parent="137" name="room_theme">
      <Position>4</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="360" parent="137" name="initial_owner_id">
      <Position>5</Position>
      <DataType>bigint|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>20</TypeId>
    </column>
    <column id="361" parent="137" name="owner_changed_people_count">
      <Position>6</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="362" parent="137" name="owner_changed_times">
      <Position>7</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="363" parent="137" name="join_people_count_total">
      <Position>8</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="364" parent="137" name="join_times_total">
      <Position>9</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="365" parent="137" name="join_people_count_big_rmb">
      <Position>10</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="366" parent="137" name="join_times_big_rmb">
      <Position>11</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="367" parent="137" name="join_people_count_daily_new">
      <Position>12</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="368" parent="137" name="join_times_daily_new">
      <Position>13</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="369" parent="137" name="join_people_count_weekly_new">
      <Position>14</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="370" parent="137" name="join_times_weekly_new">
      <Position>15</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="371" parent="137" name="instantly_quit_people_count_total">
      <Position>16</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="372" parent="137" name="instantly_quit_times_total">
      <Position>17</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="373" parent="137" name="instantly_quit_people_count_big_rmb">
      <Position>18</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="374" parent="137" name="instantly_quit_times_big_rmb">
      <Position>19</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="375" parent="137" name="instantly_quit_people_count_daily_new">
      <Position>20</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="376" parent="137" name="instantly_quit_times_daily_new">
      <Position>21</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="377" parent="137" name="instantly_quit_people_count_weekly_new">
      <Position>22</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="378" parent="137" name="instantly_quit_times_weekly_new">
      <Position>23</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="379" parent="137" name="effective_stay_people_count_total">
      <Position>24</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="380" parent="137" name="effective_stay_times_total">
      <Position>25</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="381" parent="137" name="effective_stay_people_count_big_rmb">
      <Position>26</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="382" parent="137" name="effective_stay_times_big_rmb">
      <Position>27</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="383" parent="137" name="effective_stay_people_count_daily_new">
      <Position>28</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="384" parent="137" name="effective_stay_times_daily_new">
      <Position>29</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="385" parent="137" name="effective_stay_people_count_weekly_new">
      <Position>30</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="386" parent="137" name="effective_stay_times_weekly_new">
      <Position>31</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="387" parent="137" name="total_stay_time_total">
      <Position>32</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="388" parent="137" name="total_stay_time_big_rmb">
      <Position>33</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="389" parent="137" name="total_stay_time_daily_new">
      <Position>34</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="390" parent="137" name="total_stay_time_weekly_new">
      <Position>35</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="391" parent="137" name="be_kicked_people_count_total">
      <Position>36</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="392" parent="137" name="be_kicked_times_total">
      <Position>37</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="393" parent="137" name="be_kicked_people_count_big_rmb">
      <Position>38</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="394" parent="137" name="be_kicked_times_big_rmb">
      <Position>39</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="395" parent="137" name="be_kicked_people_count_daily_new">
      <Position>40</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="396" parent="137" name="be_kicked_times_daily_new">
      <Position>41</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="397" parent="137" name="be_kicked_people_count_weekly_new">
      <Position>42</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="398" parent="137" name="be_kicked_times_weekly_new">
      <Position>43</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="399" parent="137" name="cost_golden_coin_people_count_total">
      <Position>44</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="400" parent="137" name="cost_golden_coin_count_total">
      <Position>45</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="401" parent="137" name="cost_golden_coin_people_count_big_rmb">
      <Position>46</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="402" parent="137" name="cost_golden_coin_count_big_rmb">
      <Position>47</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="403" parent="137" name="cost_golden_coin_people_count_daily_new">
      <Position>48</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="404" parent="137" name="cost_golden_coin_count_daily_new">
      <Position>49</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="405" parent="137" name="cost_golden_coin_people_count_weekly_new">
      <Position>50</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="406" parent="137" name="cost_golden_coin_count_weekly_new">
      <Position>51</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="407" parent="137" name="has_spoken_people_count_total">
      <Position>52</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="408" parent="137" name="has_spoken_people_count_big_rmb">
      <Position>53</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="409" parent="137" name="has_spoken_people_count_daily_new">
      <Position>54</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="410" parent="137" name="has_spoken_people_count_weekly_new">
      <Position>55</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="411" parent="137" name="total_voice_time_total">
      <Position>56</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="412" parent="137" name="total_voice_time_big_rmb">
      <Position>57</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="413" parent="137" name="total_voice_time_daily_new">
      <Position>58</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="414" parent="137" name="total_voice_time_weekly_new">
      <Position>59</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="415" parent="137" name="has_text_people_count_total">
      <Position>60</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="416" parent="137" name="has_text_people_count_big_rmb">
      <Position>61</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="417" parent="137" name="has_text_people_count_daily_new">
      <Position>62</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="418" parent="137" name="has_text_people_count_weekly_new">
      <Position>63</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="419" parent="137" name="total_text_count_total">
      <Position>64</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="420" parent="137" name="total_text_count_big_rmb">
      <Position>65</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="421" parent="137" name="total_text_count_daily_new">
      <Position>66</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="422" parent="137" name="total_text_count_weekly_new">
      <Position>67</Position>
      <DataType>integer|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>23</TypeId>
    </column>
    <column id="423" parent="137" name="room_created_on">
      <Position>68</Position>
      <DataType>timestamp|0s</DataType>
      <StateNumber>1949</StateNumber>
      <TypeId>1114</TypeId>
    </column>
    <column id="424" parent="137" name="created_on">
      <Position>69</Position>
      <DataType>timestamp|0s</DataType>
      <StateNumber>1949</StateNumber>
      <TypeId>1114</TypeId>
    </column>
    <column id="425" parent="137" name="created_by">
      <Position>70</Position>
      <DataType>bigint|0s</DataType>
      <NotNull>1</NotNull>
      <StateNumber>1949</StateNumber>
      <DefaultExpression>0</DefaultExpression>
      <TypeId>20</TypeId>
    </column>
    <index id="426" parent="137" name="dissolved_recreation_room_info_record_pkey">
      <ObjectId>27991</ObjectId>
      <StateNumber>2046</StateNumber>
      <ColNames>unique_room_key</ColNames>
      <Unique>1</Unique>
      <Primary>1</Primary>
    </index>
    <key id="427" parent="137" name="dissolved_recreation_room_info_record_pkey">
      <ObjectId>27992</ObjectId>
      <StateNumber>2046</StateNumber>
      <ColNames>unique_room_key</ColNames>
      <Primary>1</Primary>
      <UnderlyingIndexName>dissolved_recreation_room_info_record_pkey</UnderlyingIndexName>
    </key>
    <column id="428" parent="138" name="dt">
      <Position>1</Position>
      <Comment>日期</Comment>
      <DataType>date|0s</DataType>
      <StateNumber>1954</StateNumber>
      <TypeId>1082</TypeId>
    </column>
    <column id="429" parent="138" name="gameroomtype">
      <Position>2</Position>
      <Comment>房间类型</Comment>
      <DataType>integer|0s</DataType>
      <StateNumber>1954</StateNumber>
      <TypeId>23</TypeId>
    </column>
    <column id="430" parent="138" name="gameroomlevel">
      <Position>3</Position>
      <Comment>房间等级限制：0,5,10,30</Comment>
      <DataType>integer|0s</DataType>
      <StateNumber>1954</StateNumber>
      <TypeId>23</TypeId>
    </column>
    <column id="431" parent="138" name="gamelevel">
      <Position>4</Position>
      <Comment>用户等级</Comment>
      <DataType>integer|0s</DataType>
      <StateNumber>1954</StateNumber>
      <TypeId>23</TypeId>
    </column>
    <column id="432" parent="138" name="gamelevelgroup">
      <Position>5</Position>
      <Comment>用户等级分组：1-5、6-10、11-15、16-20、21-25、26-29、无分组</Comment>
      <DataType>text|0s</DataType>
      <StateNumber>1954</StateNumber>
      <TypeId>25</TypeId>
    </column>
    <column id="433" parent="138" name="passwordroom">
      <Position>6</Position>
      <Comment>是否密码房：True、False</Comment>
      <DataType>text|0s</DataType>
      <StateNumber>1954</StateNumber>
      <TypeId>25</TypeId>
    </column>
    <column id="434" parent="138" name="gameroomtypegroup">
      <Position>7</Position>
      <Comment>房间模式分组</Comment>
      <DataType>integer|0s</DataType>
      <StateNumber>1954</StateNumber>
      <TypeId>23</TypeId>
    </column>
    <column id="435" parent="138" name="count_join_withstart">
      <Position>8</Position>
      <Comment>有开局的进房次数</Comment>
      <DataType>bigint|0s</DataType>
      <StateNumber>1954</StateNumber>
      <TypeId>20</TypeId>
    </column>
    <column id="436" parent="138" name="count_start_continuousstart">
      <Position>9</Position>
      <Comment>连续开局次数</Comment>
      <DataType>bigint|0s</DataType>
      <StateNumber>1954</StateNumber>
      <TypeId>20</TypeId>
    </column>
    <column id="437" parent="138" name="count_join_continuousstart">
      <Position>10</Position>
      <Comment>连续开局的进房次数</Comment>
      <DataType>bigint|0s</DataType>
      <StateNumber>1954</StateNumber>
      <TypeId>20</TypeId>
    </column>
    <column id="438" parent="138" name="rate_join_withstart">
      <Position>11</Position>
      <Comment>有效开局率</Comment>
      <DataType>double precision|0s</DataType>
      <StateNumber>1954</StateNumber>
      <TypeId>701</TypeId>
    </column>
    <column id="439" parent="139" name="dt">
      <Position>1</Position>
      <Comment>日期</Comment>
      <DataType>date|0s</DataType>
      <StateNumber>1968</StateNumber>
      <TypeId>1082</TypeId>
    </column>
    <column id="440" parent="139" name="gameroomtype">
      <Position>2</Position>
      <Comment>房间类型</Comment>
      <DataType>integer|0s</DataType>
      <StateNumber>1968</StateNumber>
      <TypeId>23</TypeId>
    </column>
    <column id="441" parent="139" name="gameroomlevel">
      <Position>3</Position>
      <Comment>房间等级限制：0,5,10,30</Comment>
      <DataType>integer|0s</DataType>
      <StateNumber>1968</StateNumber>
      <TypeId>23</TypeId>
    </column>
    <column id="442" parent="139" name="gamelevel">
      <Position>4</Position>
      <Comment>用户等级</Comment>
      <DataType>integer|0s</DataType>
      <StateNumber>1968</StateNumber>
      <TypeId>23</TypeId>
    </column>
    <column id="443" parent="139" name="gamelevelgroup">
      <Position>5</Position>
      <Comment>用户等级分组：1-5、6-10、11-15、16-20、21-25、26-29、无分组</Comment>
      <DataType>text|0s</DataType>
      <StateNumber>1968</StateNumber>
      <TypeId>25</TypeId>
    </column>
    <column id="444" parent="139" name="passwordroom">
      <Position>6</Position>
      <Comment>是否密码房：True、False</Comment>
      <DataType>text|0s</DataType>
      <StateNumber>1968</StateNumber>
      <TypeId>25</TypeId>
    </column>
    <column id="445" parent="139" name="gameroomtypegroup">
      <Position>7</Position>
      <Comment>房间模式分组</Comment>
      <DataType>integer|0s</DataType>
      <StateNumber>1968</StateNumber>
      <TypeId>23</TypeId>
    </column>
    <column id="446" parent="139" name="gamecount_start">
      <Position>8</Position>
      <Comment>开局数(对局角度)</Comment>
      <DataType>bigint|0s</DataType>
      <StateNumber>1968</StateNumber>
      <TypeId>20</TypeId>
    </column>
    <column id="447" parent="139" name="voicesum_ingame">
      <Position>9</Position>
      <Comment>发言总时长</Comment>
      <DataType>bigint|0s</DataType>
      <StateNumber>1968</StateNumber>
      <TypeId>20</TypeId>
    </column>
    <column id="448" parent="139" name="voicesum_pergame">
      <Position>10</Position>
      <Comment>平均每局发言时长</Comment>
      <DataType>double precision|0s</DataType>
      <StateNumber>1968</StateNumber>
      <TypeId>701</TypeId>
    </column>
    <column id="449" parent="140" name="user_id">
      <Position>1</Position>
      <Comment>用户ID</Comment>
      <DataType>bigint|0s</DataType>
      <StateNumber>1981</StateNumber>
      <TypeId>20</TypeId>
    </column>
    <column id="450" parent="140" name="event_name">
      <Position>2</Position>
      <Comment>事件名称：join_room-进房 werewolf_game_start-开局 werewolf_game_over 完局 quit_room-退房</Comment>
      <DataType>text|0s</DataType>
      <StateNumber>1981</StateNumber>
      <TypeId>25</TypeId>
    </column>
    <column id="451" parent="140" name="event_time">
      <Position>3</Position>
      <Comment>事件时间</Comment>
      <DataType>timestamp with time zone|0s</DataType>
      <StateNumber>1981</StateNumber>
      <TypeId>1184</TypeId>
    </column>
    <column id="452" parent="140" name="room_key">
      <Position>4</Position>
      <Comment>房号</Comment>
      <DataType>text|0s</DataType>
      <StateNumber>1981</StateNumber>
      <TypeId>25</TypeId>
    </column>
    <column id="453" parent="140" name="room_value">
      <Position>5</Position>
      <DataType>text|0s</DataType>
      <StateNumber>1981</StateNumber>
      <TypeId>25</TypeId>
    </column>
    <column id="454" parent="140" name="event_value">
      <Position>6</Position>
      <DataType>text|0s</DataType>
      <StateNumber>1981</StateNumber>
      <TypeId>25</TypeId>
    </column>
    <column id="455" parent="140" name="gameroomtype">
      <Position>7</Position>
      <Comment>房间类型</Comment>
      <DataType>integer|0s</DataType>
      <StateNumber>1981</StateNumber>
      <TypeId>23</TypeId>
    </column>
    <column id="456" parent="140" name="traceid">
      <Position>8</Position>
      <DataType>text|0s</DataType>
      <StateNumber>1981</StateNumber>
      <TypeId>25</TypeId>
    </column>
    <column id="457" parent="140" name="quittype">
      <Position>9</Position>
      <Comment>退房类型：0-主动；1-被踢</Comment>
      <DataType>integer|0s</DataType>
      <StateNumber>1981</StateNumber>
      <TypeId>23</TypeId>
    </column>
    <column id="458" parent="140" name="kicktype">
      <Position>10</Position>
      <Comment>被踢类型：0-被房主踢</Comment>
      <DataType>integer|0s</DataType>
      <StateNumber>1981</StateNumber>
      <TypeId>23</TypeId>
    </column>
    <column id="459" parent="140" name="gameuuid">
      <Position>11</Position>
      <DataType>text|0s</DataType>
      <StateNumber>1981</StateNumber>
      <TypeId>25</TypeId>
    </column>
    <column id="460" parent="140" name="gamelevel">
      <Position>12</Position>
      <Comment>用户等级</Comment>
      <DataType>integer|0s</DataType>
      <StateNumber>1981</StateNumber>
      <TypeId>23</TypeId>
    </column>
    <column id="461" parent="140" name="gameroomlevel">
      <Position>13</Position>
      <Comment>房间等级限制</Comment>
      <DataType>integer|0s</DataType>
      <StateNumber>1981</StateNumber>
      <TypeId>23</TypeId>
    </column>
    <column id="462" parent="140" name="passwordroom">
      <Position>14</Position>
      <Comment>是否密码房：True/False</Comment>
      <DataType>text|0s</DataType>
      <StateNumber>1981</StateNumber>
      <TypeId>25</TypeId>
    </column>
    <column id="463" parent="140" name="createdon">
      <Position>15</Position>
      <Comment>数据生成时间</Comment>
      <DataType>timestamp with time zone|0s</DataType>
      <StateNumber>1981</StateNumber>
      <TypeId>1184</TypeId>
    </column>
    <column id="464" parent="141" name="dt">
      <Position>1</Position>
      <Comment>日期</Comment>
      <DataType>date|0s</DataType>
      <StateNumber>1995</StateNumber>
      <TypeId>1082</TypeId>
    </column>
    <column id="465" parent="141" name="gameroomtype">
      <Position>2</Position>
      <Comment>房间类型</Comment>
      <DataType>integer|0s</DataType>
      <StateNumber>1995</StateNumber>
      <TypeId>23</TypeId>
    </column>
    <column id="466" parent="141" name="gameroomlevel">
      <Position>3</Position>
      <Comment>房间等级限制：0,5,10,30</Comment>
      <DataType>integer|0s</DataType>
      <StateNumber>1995</StateNumber>
      <TypeId>23</TypeId>
    </column>
    <column id="467" parent="141" name="gamelevel">
      <Position>4</Position>
      <Comment>用户等级</Comment>
      <DataType>integer|0s</DataType>
      <StateNumber>1995</StateNumber>
      <TypeId>23</TypeId>
    </column>
    <column id="468" parent="141" name="gamelevelgroup">
      <Position>5</Position>
      <Comment>用户等级分组：1-5、6-10、11-15、16-20、21-25、26-29、无分组</Comment>
      <DataType>text|0s</DataType>
      <StateNumber>1995</StateNumber>
      <TypeId>25</TypeId>
    </column>
    <column id="469" parent="141" name="passwordroom">
      <Position>6</Position>
      <Comment>是否密码房：True、False</Comment>
      <DataType>text|0s</DataType>
      <StateNumber>1995</StateNumber>
      <TypeId>25</TypeId>
    </column>
    <column id="470" parent="141" name="gameroomtypegroup">
      <Position>7</Position>
      <Comment>房间模式分组</Comment>
      <DataType>integer|0s</DataType>
      <StateNumber>1995</StateNumber>
      <TypeId>23</TypeId>
    </column>
    <column id="471" parent="141" name="count_join_room">
      <Position>8</Position>
      <Comment>进房次数</Comment>
      <DataType>bigint|0s</DataType>
      <StateNumber>1995</StateNumber>
      <TypeId>20</TypeId>
    </column>
    <column id="472" parent="141" name="usercount_join_room">
      <Position>9</Position>
      <Comment>进房人数</Comment>
      <DataType>bigint|0s</DataType>
      <StateNumber>1995</StateNumber>
      <TypeId>20</TypeId>
    </column>
    <column id="473" parent="141" name="count_game_start">
      <Position>10</Position>
      <Comment>开局次数</Comment>
      <DataType>bigint|0s</DataType>
      <StateNumber>1995</StateNumber>
      <TypeId>20</TypeId>
    </column>
    <column id="474" parent="141" name="usercount_game_start">
      <Position>11</Position>
      <Comment>开局人数</Comment>
      <DataType>bigint|0s</DataType>
      <StateNumber>1995</StateNumber>
      <TypeId>20</TypeId>
    </column>
    <column id="475" parent="141" name="count_owner_kick_quit_room">
      <Position>12</Position>
      <Comment>被踢次数</Comment>
      <DataType>bigint|0s</DataType>
      <StateNumber>1995</StateNumber>
      <TypeId>20</TypeId>
    </column>
    <column id="476" parent="141" name="usercount_owner_kick_quit_room">
      <Position>13</Position>
      <Comment>被踢人数</Comment>
      <DataType>bigint|0s</DataType>
      <StateNumber>1995</StateNumber>
      <TypeId>20</TypeId>
    </column>
    <column id="477" parent="141" name="count_active_quit_room">
      <Position>14</Position>
      <Comment>主动退房次数</Comment>
      <DataType>bigint|0s</DataType>
      <StateNumber>1995</StateNumber>
      <TypeId>20</TypeId>
    </column>
    <column id="478" parent="141" name="usercount_active_quit_room">
      <Position>15</Position>
      <Comment>主动退房人数</Comment>
      <DataType>bigint|0s</DataType>
      <StateNumber>1995</StateNumber>
      <TypeId>20</TypeId>
    </column>
    <column id="479" parent="141" name="count_game_over">
      <Position>16</Position>
      <Comment>完局次数</Comment>
      <DataType>bigint|0s</DataType>
      <StateNumber>1995</StateNumber>
      <TypeId>20</TypeId>
    </column>
    <column id="480" parent="141" name="usercount_game_over">
      <Position>17</Position>
      <Comment>完局人数</Comment>
      <DataType>bigint|0s</DataType>
      <StateNumber>1995</StateNumber>
      <TypeId>20</TypeId>
    </column>
    <column id="481" parent="141" name="rate_game_start_pv">
      <Position>18</Position>
      <Comment>开局率PV</Comment>
      <DataType>double precision|0s</DataType>
      <StateNumber>1995</StateNumber>
      <TypeId>701</TypeId>
    </column>
    <column id="482" parent="141" name="rate_game_start_uv">
      <Position>19</Position>
      <Comment>开局率uv</Comment>
      <DataType>double precision|0s</DataType>
      <StateNumber>1995</StateNumber>
      <TypeId>701</TypeId>
    </column>
    <column id="483" parent="141" name="avg_game_start">
      <Position>20</Position>
      <Comment>人均开局次数</Comment>
      <DataType>double precision|0s</DataType>
      <StateNumber>1995</StateNumber>
      <TypeId>701</TypeId>
    </column>
    <column id="484" parent="141" name="rate_owner_kick_quit_room_pv">
      <Position>21</Position>
      <Comment>被踢率pv</Comment>
      <DataType>double precision|0s</DataType>
      <StateNumber>1995</StateNumber>
      <TypeId>701</TypeId>
    </column>
    <column id="485" parent="141" name="rate_owner_kick_quit_room_uv">
      <Position>22</Position>
      <Comment>被踢率uv</Comment>
      <DataType>double precision|0s</DataType>
      <StateNumber>1995</StateNumber>
      <TypeId>701</TypeId>
    </column>
    <column id="486" parent="141" name="rate_active_quit_room_pv">
      <Position>23</Position>
      <Comment>主动退房率pv</Comment>
      <DataType>double precision|0s</DataType>
      <StateNumber>1995</StateNumber>
      <TypeId>701</TypeId>
    </column>
    <column id="487" parent="141" name="rate_active_quit_room_uv">
      <Position>24</Position>
      <Comment>主动退房率uv</Comment>
      <DataType>double precision|0s</DataType>
      <StateNumber>1995</StateNumber>
      <TypeId>701</TypeId>
    </column>
    <column id="488" parent="141" name="rate_game_over_pv">
      <Position>25</Position>
      <Comment>完局率pv</Comment>
      <DataType>double precision|0s</DataType>
      <StateNumber>1995</StateNumber>
      <TypeId>701</TypeId>
    </column>
    <column id="489" parent="141" name="rate_game_over_uv">
      <Position>26</Position>
      <Comment>完局率uv</Comment>
      <DataType>double precision|0s</DataType>
      <StateNumber>1995</StateNumber>
      <TypeId>701</TypeId>
    </column>
    <argument id="490" parent="142">
      <Position>1</Position>
      <DataType>internal|0s</DataType>
    </argument>
    <argument id="491" parent="142">
      <Position>2</Position>
      <DataType>smallint|0s</DataType>
    </argument>
    <argument id="492" parent="142">
      <Position>3</Position>
      <DataType>jsonb|0s</DataType>
    </argument>
    <argument id="493" parent="142">
      <Position>4</Position>
      <DataType>integer|0s</DataType>
    </argument>
    <argument id="494" parent="142">
      <Position>5</Position>
      <DataType>internal|0s</DataType>
    </argument>
    <argument id="495" parent="142" name="internal">
      <Position>6</Position>
      <DataType>internal|0s</DataType>
    </argument>
    <argument id="496" parent="142">
      <Position>7</Position>
      <DataType>internal|0s</DataType>
    </argument>
    <argument id="497" parent="142">
      <ArgumentDirection>R</ArgumentDirection>
      <DataType>boolean|0s</DataType>
    </argument>
    <argument id="498" parent="143">
      <Position>1</Position>
      <DataType>internal|0s</DataType>
    </argument>
    <argument id="499" parent="143">
      <Position>2</Position>
      <DataType>smallint|0s</DataType>
    </argument>
    <argument id="500" parent="143">
      <Position>3</Position>
      <DataType>jsonb|0s</DataType>
    </argument>
    <argument id="501" parent="143">
      <Position>4</Position>
      <DataType>integer|0s</DataType>
    </argument>
    <argument id="502" parent="143">
      <Position>5</Position>
      <DataType>internal|0s</DataType>
    </argument>
    <argument id="503" parent="143" name="internal">
      <Position>6</Position>
      <DataType>internal|0s</DataType>
    </argument>
    <argument id="504" parent="143">
      <Position>7</Position>
      <DataType>internal|0s</DataType>
    </argument>
    <argument id="505" parent="143">
      <ArgumentDirection>R</ArgumentDirection>
      <DataType>boolean|0s</DataType>
    </argument>
    <argument id="506" parent="144">
      <Position>1</Position>
      <DataType>jsonb|0s</DataType>
    </argument>
    <argument id="507" parent="144">
      <Position>2</Position>
      <DataType>internal|0s</DataType>
    </argument>
    <argument id="508" parent="144">
      <Position>3</Position>
      <DataType>internal|0s</DataType>
    </argument>
    <argument id="509" parent="144">
      <ArgumentDirection>R</ArgumentDirection>
      <DataType>internal|0s</DataType>
    </argument>
    <argument id="510" parent="145">
      <Position>1</Position>
      <DataType>jsonb|0s</DataType>
    </argument>
    <argument id="511" parent="145">
      <Position>2</Position>
      <DataType>internal|0s</DataType>
    </argument>
    <argument id="512" parent="145">
      <Position>3</Position>
      <DataType>internal|0s</DataType>
    </argument>
    <argument id="513" parent="145">
      <ArgumentDirection>R</ArgumentDirection>
      <DataType>internal|0s</DataType>
    </argument>
    <argument id="514" parent="146">
      <Position>1</Position>
      <DataType>jsonb|0s</DataType>
    </argument>
    <argument id="515" parent="146">
      <Position>2</Position>
      <DataType>internal|0s</DataType>
    </argument>
    <argument id="516" parent="146">
      <Position>3</Position>
      <DataType>smallint|0s</DataType>
    </argument>
    <argument id="517" parent="146">
      <Position>4</Position>
      <DataType>internal|0s</DataType>
    </argument>
    <argument id="518" parent="146">
      <Position>5</Position>
      <DataType>internal|0s</DataType>
    </argument>
    <argument id="519" parent="146">
      <Position>6</Position>
      <DataType>internal|0s</DataType>
    </argument>
    <argument id="520" parent="146">
      <Position>7</Position>
      <DataType>internal|0s</DataType>
    </argument>
    <argument id="521" parent="146">
      <ArgumentDirection>R</ArgumentDirection>
      <DataType>internal|0s</DataType>
    </argument>
    <argument id="522" parent="147">
      <Position>1</Position>
      <DataType>jsonb|0s</DataType>
    </argument>
    <argument id="523" parent="147">
      <Position>2</Position>
      <DataType>internal|0s</DataType>
    </argument>
    <argument id="524" parent="147">
      <Position>3</Position>
      <DataType>smallint|0s</DataType>
    </argument>
    <argument id="525" parent="147">
      <Position>4</Position>
      <DataType>internal|0s</DataType>
    </argument>
    <argument id="526" parent="147">
      <Position>5</Position>
      <DataType>internal|0s</DataType>
    </argument>
    <argument id="527" parent="147">
      <Position>6</Position>
      <DataType>internal|0s</DataType>
    </argument>
    <argument id="528" parent="147">
      <Position>7</Position>
      <DataType>internal|0s</DataType>
    </argument>
    <argument id="529" parent="147">
      <ArgumentDirection>R</ArgumentDirection>
      <DataType>internal|0s</DataType>
    </argument>
    <argument id="530" parent="148" name="input_table_name">
      <Position>1</Position>
      <DataType>text|0s</DataType>
    </argument>
    <argument id="531" parent="148" name="condition_sql">
      <Position>2</Position>
      <DataType>text|0s</DataType>
      <DefaultExpression>&apos;&apos;::text</DefaultExpression>
    </argument>
    <argument id="532" parent="148">
      <ArgumentDirection>R</ArgumentDirection>
      <DataType>void|0s</DataType>
    </argument>
    <argument id="533" parent="149">
      <Position>1</Position>
      <DataType>bytea|0s</DataType>
    </argument>
    <argument id="534" parent="149">
      <Position>2</Position>
      <DataType>bytea|0s</DataType>
    </argument>
    <argument id="535" parent="149">
      <ArgumentDirection>R</ArgumentDirection>
      <DataType>bytea|0s</DataType>
    </argument>
    <argument id="536" parent="150">
      <Position>1</Position>
      <DataType>bytea|0s</DataType>
    </argument>
    <argument id="537" parent="150">
      <ArgumentDirection>R</ArgumentDirection>
      <DataType>jsonb|0s</DataType>
    </argument>
    <argument id="538" parent="151">
      <Position>1</Position>
      <DataType>bytea|0s</DataType>
    </argument>
    <argument id="539" parent="151">
      <Position>2</Position>
      <DataType>jsonb|0s</DataType>
    </argument>
    <argument id="540" parent="151">
      <Position>3</Position>
      <DataType>timestamp with time zone|0s</DataType>
    </argument>
    <argument id="541" parent="151">
      <ArgumentDirection>R</ArgumentDirection>
      <DataType>bytea|0s</DataType>
    </argument>
    <argument id="542" parent="152">
      <Position>1</Position>
      <DataType>text|0s</DataType>
    </argument>
    <argument id="543" parent="152">
      <Position>2</Position>
      <DataType>text|0s</DataType>
    </argument>
    <argument id="544" parent="152">
      <ArgumentDirection>R</ArgumentDirection>
      <DataType>void|0s</DataType>
    </argument>
    <argument id="545" parent="153">
      <Position>1</Position>
      <DataType>jsonb|0s</DataType>
    </argument>
    <argument id="546" parent="153">
      <Position>2</Position>
      <DataType>timestamp with time zone|0s</DataType>
    </argument>
    <argument id="547" parent="153">
      <ArgumentDirection>R</ArgumentDirection>
      <DataType>jsonb|0s</DataType>
    </argument>
    <user-mapping id="548" parent="155" name="public">
      <ObjectId>35088</ObjectId>
      <User>public</User>
      <Options>user_type=hg_cron_user</Options>
    </user-mapping>
    <user-mapping id="549" parent="156" name="public">
      <ObjectId>27479</ObjectId>
      <User>public</User>
    </user-mapping>
    <argument id="550" parent="157">
      <Position>1</Position>
      <DataType>internal|0s</DataType>
    </argument>
    <argument id="551" parent="157">
      <Position>2</Position>
      <DataType>smallint|0s</DataType>
    </argument>
    <argument id="552" parent="157">
      <Position>3</Position>
      <DataType>jsonb|0s</DataType>
    </argument>
    <argument id="553" parent="157">
      <Position>4</Position>
      <DataType>integer|0s</DataType>
    </argument>
    <argument id="554" parent="157">
      <Position>5</Position>
      <DataType>internal|0s</DataType>
    </argument>
    <argument id="555" parent="157" name="internal">
      <Position>6</Position>
      <DataType>internal|0s</DataType>
    </argument>
    <argument id="556" parent="157">
      <Position>7</Position>
      <DataType>internal|0s</DataType>
    </argument>
    <argument id="557" parent="157">
      <ArgumentDirection>R</ArgumentDirection>
      <DataType>boolean|0s</DataType>
    </argument>
    <argument id="558" parent="158">
      <Position>1</Position>
      <DataType>internal|0s</DataType>
    </argument>
    <argument id="559" parent="158">
      <Position>2</Position>
      <DataType>smallint|0s</DataType>
    </argument>
    <argument id="560" parent="158">
      <Position>3</Position>
      <DataType>jsonb|0s</DataType>
    </argument>
    <argument id="561" parent="158">
      <Position>4</Position>
      <DataType>integer|0s</DataType>
    </argument>
    <argument id="562" parent="158">
      <Position>5</Position>
      <DataType>internal|0s</DataType>
    </argument>
    <argument id="563" parent="158" name="internal">
      <Position>6</Position>
      <DataType>internal|0s</DataType>
    </argument>
    <argument id="564" parent="158">
      <Position>7</Position>
      <DataType>internal|0s</DataType>
    </argument>
    <argument id="565" parent="158">
      <ArgumentDirection>R</ArgumentDirection>
      <DataType>boolean|0s</DataType>
    </argument>
    <argument id="566" parent="159">
      <Position>1</Position>
      <DataType>jsonb|0s</DataType>
    </argument>
    <argument id="567" parent="159">
      <Position>2</Position>
      <DataType>internal|0s</DataType>
    </argument>
    <argument id="568" parent="159">
      <Position>3</Position>
      <DataType>internal|0s</DataType>
    </argument>
    <argument id="569" parent="159">
      <ArgumentDirection>R</ArgumentDirection>
      <DataType>internal|0s</DataType>
    </argument>
    <argument id="570" parent="160">
      <Position>1</Position>
      <DataType>jsonb|0s</DataType>
    </argument>
    <argument id="571" parent="160">
      <Position>2</Position>
      <DataType>internal|0s</DataType>
    </argument>
    <argument id="572" parent="160">
      <Position>3</Position>
      <DataType>internal|0s</DataType>
    </argument>
    <argument id="573" parent="160">
      <ArgumentDirection>R</ArgumentDirection>
      <DataType>internal|0s</DataType>
    </argument>
    <argument id="574" parent="161">
      <Position>1</Position>
      <DataType>jsonb|0s</DataType>
    </argument>
    <argument id="575" parent="161">
      <Position>2</Position>
      <DataType>internal|0s</DataType>
    </argument>
    <argument id="576" parent="161">
      <Position>3</Position>
      <DataType>smallint|0s</DataType>
    </argument>
    <argument id="577" parent="161">
      <Position>4</Position>
      <DataType>internal|0s</DataType>
    </argument>
    <argument id="578" parent="161">
      <Position>5</Position>
      <DataType>internal|0s</DataType>
    </argument>
    <argument id="579" parent="161">
      <Position>6</Position>
      <DataType>internal|0s</DataType>
    </argument>
    <argument id="580" parent="161">
      <Position>7</Position>
      <DataType>internal|0s</DataType>
    </argument>
    <argument id="581" parent="161">
      <ArgumentDirection>R</ArgumentDirection>
      <DataType>internal|0s</DataType>
    </argument>
    <argument id="582" parent="162">
      <Position>1</Position>
      <DataType>jsonb|0s</DataType>
    </argument>
    <argument id="583" parent="162">
      <Position>2</Position>
      <DataType>internal|0s</DataType>
    </argument>
    <argument id="584" parent="162">
      <Position>3</Position>
      <DataType>smallint|0s</DataType>
    </argument>
    <argument id="585" parent="162">
      <Position>4</Position>
      <DataType>internal|0s</DataType>
    </argument>
    <argument id="586" parent="162">
      <Position>5</Position>
      <DataType>internal|0s</DataType>
    </argument>
    <argument id="587" parent="162">
      <Position>6</Position>
      <DataType>internal|0s</DataType>
    </argument>
    <argument id="588" parent="162">
      <Position>7</Position>
      <DataType>internal|0s</DataType>
    </argument>
    <argument id="589" parent="162">
      <ArgumentDirection>R</ArgumentDirection>
      <DataType>internal|0s</DataType>
    </argument>
    <argument id="590" parent="163" name="input_table_name">
      <Position>1</Position>
      <DataType>text|0s</DataType>
    </argument>
    <argument id="591" parent="163" name="condition_sql">
      <Position>2</Position>
      <DataType>text|0s</DataType>
      <DefaultExpression>&apos;&apos;::text</DefaultExpression>
    </argument>
    <argument id="592" parent="163">
      <ArgumentDirection>R</ArgumentDirection>
      <DataType>void|0s</DataType>
    </argument>
    <argument id="593" parent="164">
      <Position>1</Position>
      <DataType>bytea|0s</DataType>
    </argument>
    <argument id="594" parent="164">
      <Position>2</Position>
      <DataType>bytea|0s</DataType>
    </argument>
    <argument id="595" parent="164">
      <ArgumentDirection>R</ArgumentDirection>
      <DataType>bytea|0s</DataType>
    </argument>
    <argument id="596" parent="165">
      <Position>1</Position>
      <DataType>bytea|0s</DataType>
    </argument>
    <argument id="597" parent="165">
      <ArgumentDirection>R</ArgumentDirection>
      <DataType>jsonb|0s</DataType>
    </argument>
    <argument id="598" parent="166">
      <Position>1</Position>
      <DataType>bytea|0s</DataType>
    </argument>
    <argument id="599" parent="166">
      <Position>2</Position>
      <DataType>jsonb|0s</DataType>
    </argument>
    <argument id="600" parent="166">
      <Position>3</Position>
      <DataType>timestamp with time zone|0s</DataType>
    </argument>
    <argument id="601" parent="166">
      <ArgumentDirection>R</ArgumentDirection>
      <DataType>bytea|0s</DataType>
    </argument>
    <argument id="602" parent="167">
      <Position>1</Position>
      <DataType>text|0s</DataType>
    </argument>
    <argument id="603" parent="167">
      <Position>2</Position>
      <DataType>text|0s</DataType>
    </argument>
    <argument id="604" parent="167">
      <ArgumentDirection>R</ArgumentDirection>
      <DataType>void|0s</DataType>
    </argument>
    <argument id="605" parent="168">
      <Position>1</Position>
      <DataType>jsonb|0s</DataType>
    </argument>
    <argument id="606" parent="168">
      <Position>2</Position>
      <DataType>timestamp with time zone|0s</DataType>
    </argument>
    <argument id="607" parent="168">
      <ArgumentDirection>R</ArgumentDirection>
      <DataType>jsonb|0s</DataType>
    </argument>
    <user-mapping id="608" parent="170" name="public">
      <ObjectId>34982</ObjectId>
      <User>public</User>
    </user-mapping>
    <user-mapping id="609" parent="171" name="public">
      <ObjectId>32215</ObjectId>
      <User>public</User>
    </user-mapping>
  </database-model>
</dataSource>