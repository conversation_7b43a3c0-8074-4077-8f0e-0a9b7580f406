# -*- coding: utf-8 -*-
import hmac, time, base64, urllib.parse, yaml, os
from hashlib import sha1

current_dir = os.path.dirname(os.path.realpath(__file__))
file_path = os.path.join(current_dir, r'config\appkey.yaml')
with open(file_path, 'r')as f:
    appkeys = yaml.safe_load(f)
timestamp = str(int(time.time() * 1000))


# ct True：客户端请求 False：服务端请求
def getsign(ct=True, appid='btest'):
    headers = {'boom-app-id': appid,
               # f'{app}-c-imei': '351564353542301',
               # f'{app}-c-token': '9ca16ae2e6eed7d661959aafb5c55c909e8ba6c55a939a9a83c560ad8e818cf252f7b0b6b4f72af0febec3b940f0feacc3b92aa884e1aad64b93b7e5a4cc70828f8aa6d85a93908bb8b74ba68683b1e84d92eaa5c300',
               'boom-nonce': '1611754359',
               'boom-signature-method': 'HMAC-SHA1',
               'boom-timestamp': timestamp,
               'boom-version': '2.3.1.0',
               }
    if ct:
        headers['boom-client-ptype'] = '1'
        headers['boom-client-source'] = 'jungle'
        headers['boom-client-uuid'] = 'e9f9df2a0a1565a235da982df903317d'
        userkey = appkeys[appid][0]
    else:
        userkey = appkeys[appid][1]
    strsign = '&'.join([f'{x}={headers[x]}' for x in sorted(headers.keys())])
    appsignature = hmac.new(str(userkey).encode(), strsign.encode(), sha1).digest()
    appsignature = base64.b64encode(appsignature)
    appsignature = urllib.parse.quote(appsignature, safe="")
    headers['boom-signature'] = appsignature
    return headers
