"""
<AUTHOR>
@date 2020/7/10 0010
"""
import time
import unittest
import warnings

from role.role import Devi<PERSON><PERSON>ana<PERSON>
from role.user_role import User
from util.device import DeviceContext, start_devices
from util.method_tool import parametrized_test_with

device_name = 'W4XUT19B01001844'
success_toast = '昵称修改成功'
failed_empty_toast = '昵称不能为空'
failed_illegal_toast = '内容存在敏感词'
params = {'change_nickname': [('今天天气不错哦%s' % time.strftime('%S'), success_toast), ('', failed_empty_toast),
                              ('微信', failed_illegal_toast)]}


@parametrized_test_with(params)
class EditNicknameTestCase(unittest.TestCase):
    device_ctx: DeviceContext = None
    user: User = None
    device_manager: DeviceManager = None

    @classmethod
    def setUpClass(cls) -> None:
        warnings.simplefilter("ignore", ResourceWarning)
        cls.device_ctx = start_devices([device_name])
        rpc_client = cls.device_ctx.rpc_clients[device_name]

        cls.device_manager = rpc_client.get_proxy(DeviceManager)
        cls.user = rpc_client.get_proxy(User)

    @classmethod
    def tearDownClass(cls) -> None:
        cls.device_manager.stop_langren_app()
        cls.device_ctx.dispose()

    def setUp(self) -> None:
        self.device_manager.restart_langren_app()
        self.user.wait_app_started()

    def parametrized_test_change_nickname(self, nickname: str, expect_toast: str):
        """编辑昵称"""
        print("nickname: %s expect_toast: %s" % (nickname, expect_toast))
        self.assertTrue(self.user.edit_nickname_with(nickname, expect_toast))


if __name__ == '__main__':
    unittest.main()
