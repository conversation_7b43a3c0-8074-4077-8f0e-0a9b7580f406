import pandas as pd
from openpyxl import load_workbook
from openpyxl.styles import PatternFill


def highlight_missing_data(a_file_path, b_file_path):
    """
    在a.xlsx文件中，将第二列里在b.xlsx中不存在的数据对应的单元格标黄。

    参数:
    a_file_path (str): a.xlsx文件的路径
    b_file_path (str): b.xlsx文件的路径
    """
    # 使用pandas读取a.xlsx第二列的数据（索引为1，Python索引从0开始，第二列对应索引1，跳过表头行）
    df_a = pd.read_excel(a_file_path, usecols=[1], skiprows=1, engine='openpyxl')
    # 使用pandas读取b.xlsx第二列的数据（索引为1，跳过表头行）
    df_b = pd.read_excel(b_file_path, usecols=[1], skiprows=1, engine='openpyxl')

    # 获取a.xlsx第二列的数据列表，并转换为字符串，去除可能存在的空值
    data_a = df_a.iloc[:, 0].dropna().astype(str).tolist()
    # 获取b.xlsx第二列的数据列表，并转换为集合，去除可能存在的空值，使用集合便于后续快速判断元素是否存在
    data_b = set(df_b.iloc[:, 0].dropna().astype(str).tolist())

    # 找出在a.xlsx第二列中存在但在b.xlsx第二列中不存在的数据
    missing_data = [d for d in data_a if d not in data_b]

    print(f"a.xlsx文件第二列中总共有 {len(data_a)} 个数据")
    print(f"b.xlsx文件第二列中总共有 {len(data_b)} 个数据")
    if len(missing_data) == 0:
        print("没有缺失的URL")
    else:
        print(f"a.xlsx文件第二列中在b.xlsx第二列里缺失的数据个数为: {len(missing_data)}")

        # 加载a.xlsx工作簿
        workbook = load_workbook(a_file_path)
        # 获取第一个工作表（这里假设你要操作的工作表就是第一个工作表，如果不是，可以指定具体名称来获取）
        worksheet = workbook.active

        # 定义黄色填充样式
        yellow_fill = PatternFill(start_color='FFFF00',
                                  end_color='FFFF00',
                                  fill_type='solid')

        # 遍历a.xlsx中第二列的每一行（从第二行开始，因为第一行是表头）
        for row_index in range(2, len(df_a) + 2):
            cell_value = worksheet.cell(row=row_index, column=2).value
            if cell_value and str(cell_value) in missing_data:
                cell = worksheet.cell(row=row_index, column=2)
                cell.fill = yellow_fill

        # 保存修改后的a.xlsx文件
        workbook.save(a_file_path)
        print("已成功将a.xlsx中缺失的数据对应的单元格标黄并保存文件。")


if __name__ == "__main__":
    a_file_path = r"D:\pystorage\langren_admin71\qa1_admin_test_202505201140.xlsx"
    b_file_path = r'D:\pystorage\langren_admin71\test_data.xlsx'
    highlight_missing_data(a_file_path, b_file_path)