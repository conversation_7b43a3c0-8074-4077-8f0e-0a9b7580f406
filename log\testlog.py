import requests

import hmac, time, base64, urllib.parse, random
from hashlib import sha1

# appid:[clientkey,serverkey]
appkeys = {'yg28295931': ['sCGZSxhCes0EEvsnYnxkKNj6cXPENW3L', 'XoFbvglmyhMVoEMrLBce6Qk635AnvOr3'],
           'btest': ['123456', '123456'],
           'hunter': ['n6Xr0C7CftwovB4LglvYoxvaB5L4iLQ9', 'CaMWz3WKZ3E4QAG8A44V2zbQmB7AO4mn'],
           'prod055361': ['U7UJUoUVvJK6GGyXHktGDFSu2ozpxRAY', 'vfQWZlkHFNaFhyqSaIs6MAYCnLwwicbN'],
           'audit25427': ['IG2WDNCKZjJMikfJkbRTOlua7oL14PBB', 'l107LX1yvDQLadXcmMDYtDUkNoPKUqdP'],
           'prod320749': ['tE1eO6xaAtp9xICIUBfnQNgYmfRqCnaE', 'YwR81nSiIlYr1fRGKjgDUhHWeJLBkPoS'],
           'prod520438': ['G0CnqVizsobUdYloFSP9mW0EPotSWfUe', 'RJd6VrpygWIRZZKLNQs5TLNaEpRoT1Fu'],
           'yg65vir892638': ['xADni4Txo6rEPXqygKzcAK1npDfh22Rb', 'wdvoJCIMNVhTzBTK7395ZS9Zt9HW0EOZ'],
           'pyth': ['ijRCar7OLmaYYsCtwjV5eXhq5Qh26ymy', 'nw6KeiwQbZ3LeuVSke8j3VUrWpJkGe8i'],
           'audit97688': ['ijRCar7OLmaYYsCtwjV5eXhq5Qh26ymy', 'nw6KeiwQbZ3LeuVSke8j3VUrWpJkGe8i'],
           'yg55992558': ['1', '5q42OlRJN3fXeyQsuJTZT4WKJXFnY9Y2'],
           'prod856774':['7eRw1dTEj0OpYkn3nMJT7BPxicLltpaH','mFE4ev6sweo8KM4KxTMPloUNWcHxHOlQ']
           }
timestamp = str(int(time.time() * 1000))

# ct True：客户端请求 False：服务端请求
def getsign(ct=True, app='voyage', appid='yg28295931'):
    headers = {f'{app}-app-id': appid,
               # f'{app}-c-imei': '351564353542301',
               # f'{app}-c-token': '9ca16ae2e6eed7d661959aafb5c55c909e8ba6c55a939a9a83c560ad8e818cf252f7b0b6b4f72af0febec3b940f0feacc3b92aa884e1aad64b93b7e5a4cc70828f8aa6d85a93908bb8b74ba68683b1e84d92eaa5c300',
               f'{app}-client-ptype': '1',
               f'{app}-client-source': 'xx00',
               f'{app}-client-uuid': 'e9f9df2a0a1565a235da982df903317e',
               f'{app}-nonce': '1611754359',
               f'{app}-signature-method': 'HMAC-SHA1',
               f'{app}-timestamp': timestamp,
               f'{app}-version': '2.3.1.0',
               }
    if ct:
        strsign = '&'.join([f'{x}={headers[x]}' for x in sorted(headers.keys())])
        userkey = appkeys[appid][0]
    else:
        strsign = '&'.join(
            [f'{x}={headers[x]}' for x in ([key for key in sorted(headers.keys()) if 'client' not in key])])
        userkey = appkeys[appid][1]
    appsignature = hmac.new(userkey.encode(), strsign.encode(), sha1).digest()  # hmac.has1加密(二进制)\
    appsignature = base64.b64encode(appsignature)  # base64加密
    appsignature = urllib.parse.quote(appsignature, safe="")  # urlencode
    headers[f'{app}-signature'] = appsignature
    return headers

# headers=getsign(True,'logdv','prod856774')
url = 'http://log-api.jmconch.com:80/logdv/client/log/network/upload'

data = {
    "serverAddress": "https://www.google.com",
    "status": 0,
    "responseTime": 1276,
    "monitorType": 2,
    "dns": ["*************", "*******", "***************"],
    "details": {
        "breakpoint": 0,
        "code": 0,
        "connect_time": 995,
        "dns_time": 3,
        "host": "www.google.com",
        "http_code": "200",
        "http_ver": "HTTP/1.1",
        "ip": "***************",
        "method": "HTTP",
        "port": 443,
        "recv_size": 23690,
        "send_size": 72,
        "timestamp": 1727578518111,
        "total_time": 1276,
        "trace_id": "1727578516832",
        "url": "https://www.google.com"
    }
}
# print(headers)
# headers.update('')
headers = {
    'logdv-deviceId': '213a8e664bad447a9efe256811d4554c',
    'logdv-appVersion': '5.0.0',
    'logdv-sign': 'Q44OmkHhOAuIkT2Fl2Nq0DkicrM%3D',
    'User-Agent': 'demo/5.0.0 (iPhone; iOS 14.2; Scale/3.00)',
    'logdv-appId': 'prod856774',
    'logdv-uid': '',
    'Connection': 'keep-alive',
    'logdv-netType': '2',
    'logdv-idfa': 'DC8DDC90-40D1-46A0-B48B-76E29C384D60',
    'logdv-timestamp': '1727577980081',
    'Content-Length': '545',
    'logdv-version': '*******',
    'logdv-sysVersion': '14.2',
    'logdv-phoneType': '2',
    'Accept-Language': 'zh-Hans-CN;q=1, zh-Hant-HK;q=0.9, bo-CN;q=0.8, ug-CN;q=0.7, ur-CN;q=0.6',
    # 'logdv-logTaskId': '11740',
    'logdv-model': 'iPhone10,3',
    'logdv-resolution': '812x375',
    'logdv-nonce': '3428103102',
    'Content-Type': 'application/json'  # 添加 Content-Type 头
}

response = requests.post(url=url, headers=headers, json=data)  # 使用 json 参数
print(response.json())