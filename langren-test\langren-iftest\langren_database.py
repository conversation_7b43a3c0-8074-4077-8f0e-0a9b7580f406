"""
<AUTHOR>
@date 2020/7/24 0024
"""
from typing import Tuple

import pymysql

DB_HOST = 'rm-bp18znv3cj9nj2n36.mysql.rds.aliyuncs.com'
DB_USER = 'langren'
DB_PWD = 'zs3PI2nl'


class DatabaseCall(object):

    def __init__(self, database: str):
        self.database = database
        self.connection = pymysql.connect(host=DB_HOST, port=3306, user=DB_USER,
                                          password=DB_PWD, database=self.database)

    def execute_sql(self, sql: str):
        """
        执行sql
        :param sql:
        :return:
        """
        cursor = self.connection.cursor()
        cursor.execute(sql)
        cursor.close()

    def call_stored_procedure(self, proc_name: str, args: Tuple = ()):
        """
        调用存储过程
        :param proc_name:
        :param args:
        :return:
        """
        cursor = self.connection.cursor()
        cursor.callproc(proc_name, args)
        cursor.close()

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.dispose()

    def dispose(self):
        self.connection.commit()
        self.connection.close()
