<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="9286c836-d581-479f-a380-76fd29cacc5d" name="Default Changelist" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="ProjectId" id="2CWJeisD8x8cgyqM6DiqKVE9MRi" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showExcludedFiles" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">
    <property name="RunOnceActivity.ShowReadmeOnStart" value="true" />
    <property name="WebServerToolWindowFactoryState" value="false" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$" />
  </component>
  <component name="RunManager" selected="Python.caltax">
    <configuration name="1" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="BOOM" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/boom" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/boom/1.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="BoomClientFunc" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="BOOM" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/boom" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/boom/BoomClientFunc.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="caltax" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="AK" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/../AK" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="D:\pystorage\AK\caltax.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="caogao" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="AK" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/../AK" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/../AK/caogao.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="clientsign" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="BOOM" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/boom" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/boom/clientsign.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Python.caltax" />
        <item itemvalue="Python.caogao" />
        <item itemvalue="Python.BoomClientFunc" />
        <item itemvalue="Python.clientsign" />
        <item itemvalue="Python.1" />
      </list>
    </recent_temporary>
  </component>
  <component name="SvnConfiguration">
    <configuration />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="9286c836-d581-479f-a380-76fd29cacc5d" name="Default Changelist" comment="" />
      <created>1658912504898</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1658912504898</updated>
      <workItem from="1658912506224" duration="29051000" />
      <workItem from="1660877314535" duration="672000" />
      <workItem from="1661154844879" duration="1227000" />
      <workItem from="1661763680960" duration="599000" />
      <workItem from="1661925591998" duration="1212000" />
      <workItem from="1661998868019" duration="351000" />
      <workItem from="1662362309314" duration="4509000" />
      <workItem from="1663224223818" duration="27678000" />
      <workItem from="1667374184591" duration="6838000" />
      <workItem from="1667813663945" duration="6514000" />
      <workItem from="1667960536396" duration="380000" />
      <workItem from="1667976858245" duration="594000" />
      <workItem from="1668481567432" duration="4058000" />
      <workItem from="1669023970055" duration="599000" />
      <workItem from="1669108681883" duration="4704000" />
      <workItem from="1669359552551" duration="1892000" />
      <workItem from="1670207835177" duration="21645000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="1" />
  </component>
  <component name="WindowStateProjectService">
    <state x="862" y="432" key="FileChooserDialogImpl" timestamp="1667382469025">
      <screen x="0" y="0" width="1920" height="1040" />
    </state>
    <state x="862" y="432" key="FileChooserDialogImpl/0.0.1920.1040@0.0.1920.1040" timestamp="1667382469025" />
    <state width="1381" height="510" key="GridCell.Tab.0.bottom" timestamp="1670838345209">
      <screen x="0" y="0" width="1920" height="1040" />
    </state>
    <state width="1381" height="510" key="GridCell.Tab.0.bottom/0.0.1920.1040@0.0.1920.1040" timestamp="1670838345209" />
    <state width="1381" height="510" key="GridCell.Tab.0.center" timestamp="1670838345208">
      <screen x="0" y="0" width="1920" height="1040" />
    </state>
    <state width="1381" height="510" key="GridCell.Tab.0.center/0.0.1920.1040@0.0.1920.1040" timestamp="1670838345208" />
    <state width="1381" height="510" key="GridCell.Tab.0.left" timestamp="1670838345208">
      <screen x="0" y="0" width="1920" height="1040" />
    </state>
    <state width="1381" height="510" key="GridCell.Tab.0.left/0.0.1920.1040@0.0.1920.1040" timestamp="1670838345208" />
    <state width="1381" height="510" key="GridCell.Tab.0.right" timestamp="1670838345209">
      <screen x="0" y="0" width="1920" height="1040" />
    </state>
    <state width="1381" height="510" key="GridCell.Tab.0.right/0.0.1920.1040@0.0.1920.1040" timestamp="1670838345209" />
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/BOOM$BoomClientFunc.coverage" NAME="BoomClientFunc Coverage Results" MODIFIED="1670210121635" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/boom" />
    <SUITE FILE_PATH="coverage/BOOM$BoomServerFunc__1_.coverage" NAME="BoomServerFunc (1) Coverage Results" MODIFIED="1659602085599" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/server" />
    <SUITE FILE_PATH="coverage/BOOM$bfun__1_.coverage" NAME="bfun (1) Coverage Results" MODIFIED="1659089357369" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/server" />
    <SUITE FILE_PATH="coverage/BOOM$__init__.coverage" NAME="__init__ Coverage Results" MODIFIED="1667470057783" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="E:/python3913/Lib/unittest" />
    <SUITE FILE_PATH="coverage/BOOM$clientsign.coverage" NAME="clientsign Coverage Results" MODIFIED="1669174214395" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/boom" />
    <SUITE FILE_PATH="coverage/BOOM$1.coverage" NAME="1 Coverage Results" MODIFIED="1668501847075" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/boom" />
    <SUITE FILE_PATH="coverage/BOOM$ip.coverage" NAME="查ip Coverage Results" MODIFIED="1658917952445" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/server" />
    <SUITE FILE_PATH="coverage/BOOM$sign.coverage" NAME="sign Coverage Results" MODIFIED="1658917335759" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/server" />
    <SUITE FILE_PATH="coverage/BOOM$salary.coverage" NAME="caltax Coverage Results" MODIFIED="1670491847084" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/../AK" />
    <SUITE FILE_PATH="coverage/BOOM$jiajian.coverage" NAME="endecrypt Coverage Results" MODIFIED="1659604631284" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/server" />
    <SUITE FILE_PATH="coverage/BOOM$clientsign__1_.coverage" NAME="clientsign (1) Coverage Results" MODIFIED="1659607460316" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/boom" />
    <SUITE FILE_PATH="coverage/BOOM$endecrypt__1_.coverage" NAME="endecrypt (1) Coverage Results" MODIFIED="1662375147200" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/boom" />
    <SUITE FILE_PATH="coverage/BOOM$BoomServerFunc.coverage" NAME="BoomServerFunc Coverage Results" MODIFIED="1668501839298" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/boom" />
    <SUITE FILE_PATH="coverage/BOOM$ss.coverage" NAME="ss Coverage Results" MODIFIED="1658913945212" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/../AK" />
    <SUITE FILE_PATH="coverage/BOOM$caogao.coverage" NAME="caogao Coverage Results" MODIFIED="1670405588989" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/../AK" />
  </component>
</project>