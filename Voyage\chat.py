import requests, multiprocessing
from Appsign import getsign
import json, os, socket, threading

baseurl = 'http://k8s-imqa-ingressi-5c3f8dc70f-2010225555.ap-southeast-1.elb.amazonaws.com/im/'
headers = getsign(False, 'im', 'yg28295931')


def gettoken(cpuserid):
    url = baseurl + 'sdk/security/token'
    data = {'cpUserId': cpuserid,
            'validTime': 7 * 60 * 60 * 24}
    response = requests.get(url, params=data, headers=headers)
    print(gettoken.__name__)
    print(response.text)
    return response.text


# gettoken('pgt00001')


# 创建群组
def createg(cpuserid, chatRoomid):
    data = {'cpUserId': cpuserid,
            'chatRoomId': chatRoomid}
    url = baseurl + 'sdk/chatroom/create'
    response = requests.post(url, data=data, headers=headers)
    print(createg.__name__)
    print(response.text)


# createg('pgt00001', 'chat001')


# 解散群组
def dissloveg(cpuserid, chatRoomid):
    data = {'cpUserId': cpuserid,
            'chatRoomId': chatRoomid}
    url = baseurl + 'sdk/chatroom/dissolve'
    response = requests.post(url, data=data, headers=headers)
    print(dissloveg.__name__)
    print(response.text)



# 加入群组
def joing(cpuserid, chatRoomid):
    data = {'cpUserId': cpuserid,
            'chatRoomId': chatRoomid,
            'messageCount': 10}
    url = baseurl + 'sdk/chatroom/join'
    response = requests.post(url, data=data, headers=headers)
    print(joing.__name__)
    print(response.text)


joing('pgt00003','chat001')

# 退出群组
def quitg(cpuserid, chatRoomid):
    data = {'cpUserId': cpuserid,
            'chatRoomId': chatRoomid,
            }
    url = baseurl + 'sdk/chatroom/quit'
    response = requests.post(url, data=data, headers=headers)
    print(quitg.__name__)
    print(response.text)


# quitg('pgt00003','small001')
# 群组禁言
def muteg(cpuserid, chatRoomid, banids):
    headers['Content-Type'] = 'application/json'
    data = {"chatRoomBanParam": {"cpUserId": cpuserid,
                              "chatRoomId": chatRoomid,
                              "muteTime": 60,
                              "targetIds": banids}
            }
    url = baseurl + 'sdk/chatroom/mute'
    response = requests.post(url, data=json.dumps(data), headers=headers)
    print(muteg.__name__)
    print(response.text)
    print(response.request.body)
    print(response.request.headers)
    # print(headers)


# muteg('pgt00001', 'small001', ['pgt00003', ])


# 群组解禁
def ummuteg(cpuserid, chatRoomid, unbanids):
    headers['Content-Type'] = 'application/json'
    data = {'chatRoomUnBanParam': {'cpUserId': cpuserid,
                                'chatRoomId': chatRoomid,
                                'targetIds': unbanids}
            }
    url = baseurl + 'sdk/chatroom/unmute'
    response = requests.post(url, data=json.dumps(data), headers=headers)
    print(muteg.__name__)
    print(response.text)


def getmen(cpuserids, chatRoomid):
    data = {"cpUserIds": cpuserids,
            'chatRoomId': chatRoomid}
    url = baseurl + 'sdk/chatroom/member/online'
    response = requests.get(url, data, headers=headers)
    print(getmen.__name__)
    print(response.text)


# getmen('pgt00001', 'small001')


def postmen(cpuserids, chatRoomid):
    headers['Content-Type'] = 'application/json'
    data = {"chatRoomMemberParam": {"cpUserIds": cpuserids,
                                 "chatRoomId": chatRoomid}}
    url = baseurl + 'sdk/chatroom/member/online'
    response = requests.post(url=url, data=json.dumps(data), headers=headers)
    print(postmen.__name__)
    print(response.text)


# postmen(['pgt00001'], 'small001')


