"""
<AUTHOR>
@date 2020/6/30 0030
"""
import json


class Gift(object):

    def __init__(self, name: str, count: int, popularity: int, effect: str):
        self.name = name
        self.count = count
        self.popularity = popularity
        self.effect = effect

    def __eq__(self, other):
        if type(other) != Gift:
            return False
        return self.name == other.name and self.count == other.count and self.popularity == other.popularity and self.effect == other.effect

    def __str__(self):
        return 'Gift: %s' % json.dumps(self.__dict__, ensure_ascii=False)
