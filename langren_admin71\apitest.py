from flask import Flask, render_template, json
import pandas as pd
import requests
import time

app = Flask(__name__)


@app.route('/', methods=['GET'])
def run_tests():
    start_time = time.time()
    headers = {'cookie': 'SESSION=87e01d52-c6ee-4818-ad98-35b1880f95bf'}
    # 读取Excel表格数据
    # df = pd.read_excel('test_data.xlsx')
    df = pd.read_excel('ada.xlsx')
    test_results = []
    total_tests = len(df)
    success_count = 0
    fail_count = 0

    for index, row in df.iterrows():

        url = row['接口URL']
        method = row['接口方法']
        params_str = row['接口参数']
        expected_result_str = row['预期结果']
        if pd.isnull(expected_result_str): expected_result_str = ''

        try:
            start_request_time = time.time()
            if pd.isnull(params_str):
                params = {}
            else:
                params = eval(params_str)
            # 根据接口方法处理请求参数
            if method == 'GET':
                response = requests.get(url, params=params, headers=headers)
            elif method == 'POST':
                if params_str:
                    try:
                        params = eval(params_str)
                    except Exception as e:
                        raise ValueError(f"POST请求参数解析错误: {e}")
                else:
                    params = {}
                response = requests.post(url, json=params, headers=headers)
                if response.status_code == 400:
                    response = requests.post(url, data=params, headers=headers)
            else:
                raise ValueError(f"不支持的接口方法: {method}")

            response_time = (time.time() - start_request_time) * 1000  # 转换为毫秒

            if response.status_code == 200 and expected_result_str in response.text:
                success_count += 1
                test_result = {
                    '接口概述': row['接口概述'],
                    '接口url': url,
                    '接口参数': params,
                    '接口返回结果': response.text,
                    '接口成功与否': True,
                    '接口响应时间': f"{response_time:.2f}毫秒"
                }
            else:
                fail_count += 1
                test_result = {
                    '接口概述': row['接口概述'],
                    '接口url': url,
                    '接口参数': params,
                    '接口返回结果': response.text,
                    '接口成功与否': False,
                    '接口响应时间': f"{response_time:.2f}毫秒"
                }
        except requests.exceptions.Timeout:
            fail_count += 1
            test_result = {
                '接口概述': row['接口概述'],
                '接口url': url,
                '接口参数': params,
                '接口返回结果': '请求超时',
                '接口成功与否': False,
                '接口响应时间': '超时无响应'
            }
        except Exception as e:
            fail_count += 1
            test_result = {
                '接口概述': row['接口概述'],
                '接口url': url,
                '接口参数': params,
                '接口返回结果': str(e),
                '接口成功与否': False,
                '接口响应时间': '出错无响应'
            }

        test_results.append(test_result)

    end_time = time.time()
    execution_time = end_time - start_time

    success_rate = (success_count / total_tests) * 100 if total_tests > 0 else 0

    return render_template('test_results.html',
                           total_tests=total_tests,
                           success_count=success_count,
                           fail_count=fail_count,
                           success_rate=f"{success_rate:.2f}%",
                           test_results=test_results,
                           execution_time=f"{execution_time:.2f}秒")


if __name__ == '__main__':
    app.run(debug=True)
