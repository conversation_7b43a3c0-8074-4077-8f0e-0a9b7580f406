<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>接口测试结果</title>
    <style>
        table {
            border-collapse: collapse;
            width: 100%;
        }

        th,
        td {
            border: 1px solid black;
            padding: 8px;
            text-align: left;
        }

        /* 设置鼠标移上去显示全部值的样式 */
        td[data-full-text]:hover {
            cursor: pointer;
            position: relative;
        }

        td[data-full-text]:hover::after {
            content: attr(data-full-text);
            position: absolute;
            top: 100%;
            left: 0;
            background-color: white;
            border: 1px solid black;
            padding: 5px;
            z-index: 1;
            max-width: 300px; /* 设置提示框最大宽度 */
            word-break: break-all; /* 允许单词内换行 */
        }
    </style>
    <script>
        // 定义复制文本到剪贴板的函数
        function copyTextToClipboard(text) {
            const textarea = document.createElement('textarea');
            textarea.value = text;
            document.body.appendChild(textarea);
            textarea.select();
            document.execCommand('copy');
            document.body.removeChild(textarea);
            alert('已复制到剪贴板！');
        }

        // 为每个可复制的单元格添加点击事件监听器
        window.onload = function () {
            const cellsWithFullText = document.querySelectorAll('td[data-full-text]');
            cellsWithFullText.forEach(function (cell) {
                cell.addEventListener('click', function () {
                    const fullText = this.getAttribute('data-full-text');
                    copyTextToClipboard(fullText);
                });
            });

            const cellsWithFullParam = document.querySelectorAll('td[data-full-param]');
            cellsWithFullParam.forEach(function (cell) {
                cell.addEventListener('click', function () {
                    const fullParam = this.getAttribute('data-full-param');
                    copyTextToClipboard(fullParam);
                });
            });
        }
    </script>
</head>

<body>
    <h1>接口测试结果</h1>
    <p>测试接口总数: {{ total_tests }}</p>
    <p>成功数: {{ success_count }}</p>
    <p>失败数: {{ fail_count }}</p>
    <p>成功率: {{ success_rate }}</p>
    <p>执行时间: {{ execution_time }}</p>

    <table>
        <tr>
            <th>接口概述</th>
            <th>接口url</th>
            <th>接口参数</th>
            <th>接口返回结果</th>
            <th>接口成功与否</th>
            <th>接口响应时间</th>
        </tr>
        {% for result in test_results %}
        <tr>
            <td>{{ result['接口概述'] }}</td>
            <td>{{ result['接口url'] }}</td>
            <td data-full-param="{{ result['接口参数'] }}" >
                {% if result['接口参数']|length > 20 %}
                    {{ result['接口参数'][:20] }}...
                {% else %}
                    {{ result['接口参数'] }}
                {% endif %}
            </td>
            <td data-full-text="{{ result['接口返回结果'] }}" >
                {% if result['接口返回结果']|length > 20 %}
                    {{ result['接口返回结果'][:20] }}...
                {% else %}
                    {{ result['接口返回结果'] }}
                {% endif %}
            </td>
            <td>{{ result['接口成功与否'] }}</td>
            <td>{{ result['接口响应时间'] }}</td>
        </tr>
        {% endfor %}
    </table>
</body>

</html>