"""
<AUTHOR>
@date 2020/7/24 0024
"""
import requests
import time

APOLLO_ENV = 'UAT'
APOLLO_CLUSTER = 'qa3'
APOLLO_PORTAL_URL = '121.41.47.30:8070'
APOLLO_PORTAL_TOKEN = '93282e8a772a70d88038bf67d285dacda6c3a9d9'


class ApolloCall(object):

    def __init__(self):
        self.session = requests.Session()
        self.session.headers['Authorization'] = APOLLO_PORTAL_TOKEN
        self.session.headers['Content-Type'] = 'application/json;charset=UTF-8'

    def edit_config(self, app_id: str, namespace: str, key: str, value: str):
        """
        编辑配置
        :param app_id:
        :param namespace:
        :param key:
        :param value:
        :return:
        """
        url = f'http://{APOLLO_PORTAL_URL}/openapi/v1/envs/{APOLLO_ENV}/apps/{app_id}/clusters/{APOLLO_CLUSTER}/namespaces/{namespace}/items/{key}'

        data = {
            "key": key,
            "value": value,
            "comment": "nothing",
            "dataChangeLastModifiedBy": "apollo"
        }

        params = {"createIfNotExists": True}
        resp = self.session.put(url, params=params, json=data)
        if resp.status_code != 200:
            raise AssertionError

    def release_namespace(self, app_id: str, namespace: str):
        """
        发布配置
        :param app_id:
        :param namespace:
        :return:
        """
        url = f'http://{APOLLO_PORTAL_URL}/openapi/v1/envs/{APOLLO_ENV}/apps/{app_id}/clusters/{APOLLO_CLUSTER}/namespaces/{namespace}/releases'
        data = {
            "releaseTitle": 'langren_test_' + str(time.time()),
            "releaseComment": "nothing",
            "releasedBy": "apollo"
        }
        resp = self.session.post(url, json=data)
        if resp.status_code != 200:
            raise AssertionError
