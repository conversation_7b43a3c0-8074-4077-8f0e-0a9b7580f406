"""
<AUTHOR>
@date 2020/7/14 0014
"""
import base64
import json
import uuid
from typing import List, Any, Dict


class GiftConfig(object):

    def __init__(self, config_id: int, name: str, thumb: str, popularity: int, amount: int, effect: str):
        self.config_id = config_id
        self.name = name
        self.thumb = thumb
        self.popularity = popularity
        self.amount = amount
        self.effect = effect

    def __str__(self):
        return json.dumps(self.__dict__, ensure_ascii=False)


class UserConfig(object):

    def __init__(self, user_id: int, user_identity: str, access_token: str, device_id: str = None):
        if device_id is None:
            device_id = str(uuid.uuid4())

        self.user_id = user_id
        self.user_identity = user_identity
        self.access_token = access_token
        self.device_id = device_id


class ExportCase(object):

    @staticmethod
    def read_from_json_file(filepath: str):
        with open(filepath, encoding='UTF-8') as file:
            j = json.loads(file.read())
            return ExportCase(j)

    @staticmethod
    def read_from_dict(d: dict):
        return ExportCase(d)

    def __init__(self, d: dict):
        self.name: str = d['name']
        self.version: int = d['version']
        self.profiles: List[Profile] = list(map(Profile, d['profiles']))
        self.groups: List[Group] = list(map(Group, d['groups']))


class Group(object):

    def __init__(self, d: dict):
        self.name = d['name']
        self.requests: List[Request] = list(map(Request, d['requests']))


class Profile(object):

    def __init__(self, d: dict):
        self.name: str = d['name']
        self.script: str = d['script']


class Request(object):
    def __init__(self, d: dict):
        self.enabled: str = d['enabled']
        self.uuid: str = d['uuid']
        self.name: str = d['name']
        self.method: str = d['method']
        self.url: str = d['url']
        self.params: Dict[str, Any] = {kv['key']: str(kv['value']) for kv in d['params']}
        self.headers: Dict[str, Any] = {kv['key']: str(kv['value']) for kv in d['headers']}
        self.body: RequestBody = RequestBody(d['body'])
        self.script: str = d['script']

    def __repr__(self):
        return self.name


class RequestBody(object):
    def __init__(self, d: dict):
        self.type: str = d['type']

        if self.type == 'formdata':
            self.formdata: Dict[str, Any] = {
                fd['key']: (str(fd['value']) if fd['type'] == 'text' else base64.b64decode(fd['value'])) for
                fd in d['formdata']}
        elif self.type == 'raw':
            self.raw: str = d['raw']
        elif self.type == 'raw':
            self.bin: str = d['bin']
