"""
<AUTHOR>
@date 2020/6/30 0030
"""
import time
from typing import Optional, Tuple

from biz.gift.domain import Gift
from page.page import PageRoot
from role.role import Role
from util.log import get_logger, log


class User(Role):

    def __init__(self, page_root: PageRoot):
        super().__init__(page_root)
        self.gift_panel_index = 0
        self.logger = get_logger('User')

    @log
    def infer_user_nickname(self) -> str:
        """
        在hometab获取用户昵称
        :return:
        """
        label = self.page_root.home_tab_page.nickname_label
        label.wait_for_appearance(30)
        return label.get_text()

    @log
    def infer_vip_days(self) -> int:
        """
        vip剩余天数
        :return:
        """
        vip_duration_text = self.page_root.home_tab_page.vip_duration_label.get_text()
        if vip_duration_text == '暂未开通':
            return -1
        return int(vip_duration_text[1:-1])

    def enter_private_message(self, uid: int):
        """
        进入私信界面
        :param uid:
        :return:
        """
        message_tab_page = self.page_root.message_tab_page
        message_tab_page.msg_tab_btn.click()
        message_tab_page.add_friend_btn.click()
        message_tab_page.add_friend_id_input.set_text(str(uid))
        time.sleep(0.5)
        message_tab_page.add_friend_id_confirm_btn.click()
        self.page_root.user_home_page.private_msg_btn.click()
        self.gift_panel_index = 0

    def send_gift_in_pm(self, gift_name: str, num: int = None):
        """
        私信界面发礼物
        :param gift_name:
        :param num:
        :return:
        """
        self.page_root.private_message_page.send_gift_btn.click()
        super().send_gift(gift_name, num)

    def get_latest_gift_in_pm(self) -> Optional[Tuple[bool, Gift]]:
        """
        获取私信界面最新一条礼物消息
        :return:
        """
        page = self.page_root.private_message_page
        while page.unread_msg_label.exists():
            page.swipe_down()

        return page.latest_gift

    def edit_nickname(self, nickname: str) -> Tuple[str, str]:
        """
        编辑昵称
        :param nickname:
        :return:
        """
        pr = self.page_root

        pr.home_tab_page.user_info_back.click()
        original_nickname = pr.user_home_page.nickname_label.get_text()
        pr.user_home_page.edit_profile_btn.click()
        pr.user_profile_edit_page.nickname.click()
        pr.user_profile_edit_page.nickname_input.set_text(nickname)
        pr.back_btn.click()
        pr.user_profile_edit_page.confirm_btn.click()
        self.wait_toast('昵称修改成功')
        pr.back_btn.click()
        current_nickname = pr.user_home_page.nickname_label.get_text()
        pr.back_btn.click()
        return original_nickname, current_nickname

    def edit_signature(self, signature: str) -> Tuple[str, str]:
        """
        编辑个性签名
        :param signature:
        :return:
        """
        pr = self.page_root

        pr.home_tab_page.user_info_back.click()
        original_signature = pr.user_home_page.signature_label.get_text()
        pr.user_home_page.edit_profile_btn.click()
        pr.user_profile_edit_page.signature.click()
        pr.user_profile_edit_page.signature_input.set_text(signature)
        pr.back_btn.click()
        pr.user_profile_edit_page.confirm_btn.click()
        pr.back_btn.click()
        current_signature = pr.user_home_page.signature_label.get_text()
        pr.back_btn.click()

        return original_signature, current_signature

    def edit_head_img_with_photo(self, photo_name: str):
        """
        编辑头像
        :param photo_name:
        :return:
        """
        pr = self.page_root

        pr.home_tab_page.user_info_back.click()
        pr.user_home_page.edit_profile_btn.click()
        pr.user_profile_edit_page.head_img.click()
        pr.user_profile_edit_page.select_photo_btn.click()
        pr.user_profile_edit_page.choose_photo(photo_name).click()
        pr.user_profile_edit_page.confirm_crop_btn.click()
        self.wait_toast('头像修改成功')
        pr.back_btn.click()
        pr.back_btn.click()

    def edit_background_img_with_photo(self, photo_name: str) -> bool:
        """
        编辑背景图
        :param photo_name:
        :return:
        """
        pr = self.page_root

        pr.home_tab_page.user_info_back.click()
        pr.user_home_page.edit_profile_btn.click()
        pr.user_profile_edit_page.background_img.click()
        pr.user_profile_edit_page.select_photo_btn.click()

        can_edit = True
        if pr.user_profile_edit_page.buy_vip_dialog.exists():
            pr.user_profile_edit_page.cancel_buy_vip_btn.click()
            can_edit = False
        else:
            pr.user_profile_edit_page.choose_photo(photo_name).click()
            pr.user_profile_edit_page.confirm_crop_btn.click()
            self.wait_toast('背景修改成功')
        pr.back_btn.click()
        pr.back_btn.click()
        return can_edit

    def change_gender(self) -> Tuple[int, int, bool]:
        """
        修改性别
        :return:
        """
        pr = self.page_root

        pr.home_tab_page.user_info_back.click()
        pr.user_home_page.edit_profile_btn.click()
        pr.user_profile_edit_page.gender.click()
        original_gender = pr.user_gender_edit_page.selected_gender
        if original_gender == 0:
            pr.user_gender_edit_page.male_btn.click()
        else:
            pr.user_gender_edit_page.female_btn.click()

        free_edit = pr.user_gender_edit_page.edit_btn.get_text() == '本月首次免费修改'
        gender_card_num = pr.user_gender_edit_page.gender_card_num
        if free_edit or gender_card_num > 0:
            pr.user_gender_edit_page.edit_btn.click()
            pr.user_gender_edit_page.confirm_edit_btn.click()
        else:
            pr.user_gender_edit_page.buy_gender_card_btn.click()
            pr.user_gender_edit_page.confirm_buy_btn.click()
            pr.user_gender_edit_page.buy_accept_btn.click()
            pr.user_gender_edit_page.edit_btn.click()
            pr.user_gender_edit_page.confirm_edit_btn.click()

        pr.user_profile_edit_page.gender.click()
        current_gender = pr.user_gender_edit_page.selected_gender
        pr.back_btn.click()
        pr.back_btn.click()
        pr.back_btn.click()

        return original_gender, current_gender, free_edit

    def edit_nickname_with(self, nickname: str, expect_toast: str) -> bool:
        """
        编辑昵称
        :param nickname:
        :param expect_toast:
        :return:
        """
        pr = self.page_root

        pr.home_tab_page.user_info_back.click()
        pr.user_home_page.edit_profile_btn.click()
        pr.user_profile_edit_page.nickname.click()
        pr.user_profile_edit_page.nickname_input.set_text(nickname)
        pr.back_btn.click()
        confirm_btn = pr.user_profile_edit_page.confirm_btn
        if confirm_btn.exists():
            confirm_btn.click()
        return self.wait_toast(expect_toast, error_on_failed=False)
