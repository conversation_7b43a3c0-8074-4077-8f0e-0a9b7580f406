"""
<AUTHOR>
@date 2020/6/17 0017
"""
from concurrent.futures.thread import ThreadPoolExecutor
from multiprocessing.connection import Connection
from typing import Dict, TypeVar, Type, List, Generic, Callable

from util.log import get_logger
from util.thread_tool import Signal

T = TypeVar('T')

logger = get_logger('Rpc')


class Request(Generic[T]):
    def __init__(self, seq_no: int, service_type: Type[T], method_name: str, *args):
        self.seq_no = seq_no
        self.service_type = service_type
        self.method_name = method_name
        self.args = args

    def __str__(self):
        return 'Request:%s %s.%s%s' % (self.seq_no, self.service_type.__name__, self.method_name, self.args)


class Response(Generic[T]):
    def __init__(self, seq_no: int, ret: T = None):
        self.seq_no = seq_no
        self.ret = ret

    def __str__(self):
        return 'Response:%s %s' % (self.seq_no, self.ret)


class RpcError(Exception):
    def __init__(self, cause: str):
        self.cause = cause


class RpcContext(object):

    def __init__(self, conn: Connection):
        self.__conn = conn
        self.sub_dict: Dict[Type[T], List[Callable[[T], None]]] = {}
        self.executor = ThreadPoolExecutor(max_workers=50)
        self.poll = True

        self.executor.submit(self.__dispatch_remote)
        self.logger = get_logger('RpcContext')

    def __dispatch_remote(self):
        while self.poll:
            try:
                recv = self.__conn.recv()
                logger.debug('[RECEIVE] %s' % recv)
                self._call_consumer(recv)
            except EOFError:
                break

    def dispatch(self, event: T):
        self._call_consumer(event)
        logger.debug('[SEND] %s' % event)
        self.__conn.send(event)

    def _call_consumer(self, event):
        event_type = type(event)
        if event_type in self.sub_dict:
            self.executor.map(lambda c: c(event), self.sub_dict[event_type])

    def subscribe(self, event_type: Type[T], consumer: Callable[[T], None]):
        self.sub_dict.setdefault(event_type, [])
        self.sub_dict[event_type].append(consumer)

    def wait(self, event_type: Type[T], consumer: Callable[[T], None] = lambda x: x):
        event_arrival_signal: Signal[bool] = Signal()

        def wrap_consumer(*args, **kwargs):
            try:
                consumer(*args, **kwargs)
            finally:
                event_arrival_signal.notify_all(True)

        self.sub_dict.setdefault(event_type, [])
        self.sub_dict[event_type].append(wrap_consumer)
        logger.info('[WAIT] [BEGIN] %s' % event_type.__name__)
        event_arrival_signal.wait()
        logger.info('[WAIT] [END] %s' % event_type.__name__)
        self.sub_dict[event_type].remove(wrap_consumer)

    def dispose(self):
        self.poll = False
        self.__conn.close()
        self.executor.shutdown()


class RpcServer(RpcContext):
    def __init__(self, conn: Connection):
        super().__init__(conn)
        self.__conn = conn
        self.handlers: Dict[Type[T], T] = {}
        self.subscribe(Request, self.handle_req)

    def handle_req(self, req: Request):
        resp: Response = Response(req.seq_no)
        try:
            found = False
            if req.service_type in self.handlers:
                result = getattr(self.handlers[req.service_type], req.method_name)(*req.args)
                resp.ret = result
                found = True
            else:
                for st in self.handlers:
                    if not found and issubclass(st, req.service_type):
                        result = getattr(self.handlers[st], req.method_name)(*req.args)
                        resp.ret = result
                        found = True
            if not found:
                raise RuntimeError('Parameter Error')

        except Exception as e:
            logger.error(e)
            resp.ret = RpcError(type(e).__name__)

        logger.debug('RpcServer  %s -> %s' % (req, resp))
        self.__conn.send(resp)


class RpcClient(RpcContext):
    def __init__(self, conn: Connection):
        super().__init__(conn)
        self.__conn = conn
        self.__seq_no = 0
        self.handlers: Dict[Type[T], T] = {}
        self.__waiting_requests: Dict[int, Callable[[Response], None]] = {}
        self.subscribe(Response, self.handle_resp)

    def execute(self, req: Request) -> Response:
        self.__seq_no += 1

        req.seq_no = self.__seq_no
        wait_resp_signal: Signal[Response] = Signal()
        self.__waiting_requests[req.seq_no] = lambda x: wait_resp_signal.notify_all(x)
        self.__conn.send(req)
        resp = wait_resp_signal.wait()
        return resp

    def handle_resp(self, resp: Response):
        seq_no = resp.seq_no
        if seq_no in self.__waiting_requests:
            consumer = self.__waiting_requests[seq_no]
            del self.__waiting_requests[seq_no]
            consumer(resp)

    def get_proxy(self, target: Type[T]) -> T:
        return RpcProxy(self, target)


class RpcProxy(Generic[T]):

    def __init__(self, client: RpcClient, target: Type[T]):
        self.__client = client
        self.__target = target

    def __getattr__(self, item):
        def delegate(*args):
            req = Request(0, self.__target, item, *args)
            resp = self.__client.execute(req)
            if type(resp.ret) == RpcError:
                logger.error(resp.ret)
                raise resp.ret
            return resp.ret

        return delegate
