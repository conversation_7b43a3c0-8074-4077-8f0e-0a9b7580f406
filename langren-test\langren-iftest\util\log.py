"""
<AUTHOR>
@date 2020/6/17 0017
"""
import functools
import logging

loggers = {}


def get_logger(name):
    if name in loggers:
        return loggers[name]
    logger = logging.getLogger(name)
    logger.setLevel(logging.INFO)
    # handler = logging.FileHandler(filename=r'./log/output.log', encoding='utf8')
    handler = logging.StreamHandler()
    formatter = logging.Formatter(
        fmt='[%(asctime)s] [%(processName)s] [%(threadName)s] [%(name)s] [%(levelname)s] - %(message)s'
    )
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    loggers[name] = logger
    return logger


def log(func):
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        _logger = getattr(args[0], 'logger', None)
        if _logger is None:
            _logger = get_logger('root')
        _logger.info('[BEGIN] %s %s%s' % (args[0].__class__.__name__, func.__name__, args[1:]))
        ret = None
        ex = None
        try:
            ret = func(*args, **kwargs)
        except Exception as e:
            ex = e
            raise ex
        finally:
            if ex is not None:
                _logger.error(
                    '[END] %s %s%s [%s] [%s]' % (args[0].__class__.__name__, func.__name__, args[1:], ret, ex))
            else:
                _logger.info(
                    '[END] %s %s%s [%s] [%s]' % (args[0].__class__.__name__, func.__name__, args[1:], ret, ex))
        return ret

    return wrapper
