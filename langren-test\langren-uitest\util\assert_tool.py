"""
<AUTHOR>
@date 2020/6/23 0023
"""
import functools
from typing import Callable, List, Tuple

from util.log import get_logger

logger = get_logger('Assert')


def assert_log(assert_name):
    def deco(func):
        @functools.wraps(func)
        def wrapper(*args):
            try:
                func(*args)
                logger.info('%s [PASSED]' % assert_name)
            except AssertionError as e:
                logger.error('%s [FAILED]' % assert_name)
                raise e

        return wrapper

    return deco


def assert_is_true(v: bool, assert_name: str = ""):
    if v:
        logger.info('%s [passed]' % assert_name)
    else:
        logger.error('%s [failed]' % assert_name)
        raise AssertionError(assert_name)


def assert_value_is_true(assert_name: str, func: Callable[..., bool], args: List[Tuple] = None):
    if args is None:
        args = ()
    try:
        assert func(*args)
        logger.info('%s [PASSED]' % assert_name)
    except Asser<PERSON>Error as e:
        logger.error('%s [FAILED]' % assert_name)
        raise e
