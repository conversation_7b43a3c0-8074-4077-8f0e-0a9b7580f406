#!/usr/bin/env python3
# encoding: utf-8
import hmac, time, base64, urllib.parse
from hashlib import sha1

boomtimestamp =str(int(time.time()))
boomAppSecretKey = {'btest': '123456', 'hunter': 'CaMWz3WKZ3E4QAG8A44V2zbQmB7AO4mn'}
boomAppClientSecretKey = {'btest': '123456'}
boomInfo = [['boom-app-id', 'btest'], ['boom-nonce', '123456'], ['boom-signature-method', 'HMAC-SHA1'],
            ['boom-timestamp', "1658913825"], ['boom-version', '*******']]


def GetSign(list):
    strsign = ''
    for key in list:
        strsign = strsign + str(key[0] + '=' + key[1] + '&')
        print(key[0] + ':' + key[1] + "<>")
    strsign = strsign.strip('&')
    print(strsign)
    boomsignature = hmac.new(boomAppSecretKey[list[0][1]].encode(), strsign.encode(), sha1).digest()  # hmac.has1加密(二进制)
    boomsignature = base64.b64encode(boomsignature)  # base64加密
    boomsignature = urllib.parse.quote(boomsignature, safe="")  # urlencode
    print(f'boomsignature:{boomsignature}<>')


GetSign(boomInfo)
