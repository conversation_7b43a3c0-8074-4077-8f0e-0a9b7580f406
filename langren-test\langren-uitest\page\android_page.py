"""
<AUTHOR>
@date 2020/6/16 0016
"""
from typing import Dict, List, Optional, Tuple

from airtest.core.helper import G
from poco import <PERSON><PERSON>
from poco.drivers.android.uiautomation import AndroidUiautomationPoco
from poco.proxy import UIObjectProxy

from biz.gift.domain import Gift
from page.page import HomeTabPage, PageRoot, GameRoomPage, MessageTabPage, UserHomePage, PrivateMessagePage, GiftPanel, \
    MyTabPage, UserProfileEditPage, UserGenderEditPage
from util.method_tool import retry


class AndroidHomeTabPage(HomeTabPage):

    @property
    def home_tab_btn(self) -> UIObjectProxy:
        return self.poco("android.widget.LinearLayout").offspring("android:id/content").offspring(
            "android.widget.LinearLayout").child("android.widget.ImageView")[0]

    @property
    def nickname_label(self) -> UIObjectProxy:
        return self.poco("com.c2vl.kgamebox:id/tv_user_info_name")

    @property
    def vip_duration_label(self) -> UIObjectProxy:
        return self.poco("com.c2vl.kgamebox:id/vip_duration_days")

    @property
    def user_info_back(self) -> UIObjectProxy:
        return self.poco("com.c2vl.kgamebox:id/user_info_back")

    @property
    def create_room_btn(self) -> UIObjectProxy:
        return self.poco("com.c2vl.kgamebox:id/hpb_home_create_room")

    @property
    def search_room_btn(self) -> UIObjectProxy:
        return self.poco("com.c2vl.kgamebox:id/hpb_home_search")

    @property
    def dialog_confirm_btn(self) -> UIObjectProxy:
        return self.poco("com.c2vl.kgamebox:id/dialog_positive_btn")

    @property
    def search_room_input(self) -> UIObjectProxy:
        return self.poco("com.c2vl.kgamebox:id/content_edit_area")

    @property
    def follow_room_btn(self) -> UIObjectProxy:
        return self.poco("com.c2vl.kgamebox:id/hpb_home_follow")

    @property
    def enter_follow_room_btn(self) -> UIObjectProxy:
        return self.poco("com.c2vl.kgamebox:id/icon_arrow")


class AndroidGameRoomPage(GameRoomPage):

    @property
    def room_num_label(self) -> UIObjectProxy:
        return self.poco("com.c2vl.kgamebox:id/tool_bar_title")

    @property
    def game_ready_btn(self) -> UIObjectProxy:
        return self.poco("com.c2vl.kgamebox:id/ready")

    @property
    def start_game_btn(self) -> UIObjectProxy:
        return self.poco("com.c2vl.kgamebox:id/start")

    @property
    def game_role_identity_label(self) -> UIObjectProxy:
        return self.poco("com.c2vl.kgamebox:id/identity_name")

    @property
    def game_role_prompt(self) -> UIObjectProxy:
        return self.poco("om.c2vl.kgamebox:id/layout_lang_ren_root")

    def select_kill_man_btn(self, seat_num: int) -> UIObjectProxy:
        parent = self.poco("android:id/content").offspring("com.c2vl.kgamebox:id/step_content_area").child(
            "android.widget.LinearLayout").child("com.c2vl.kgamebox:id/grid_view")
        parent.wait_for_appearance()
        children = parent.children()
        for child in children:
            text = child.offspring("com.c2vl.kgamebox:id/frame_layout").offspring(
                "com.c2vl.kgamebox:id/tv_room_member_index").get_text()
            if text == str(seat_num):
                return child
        raise RuntimeError('no element found')

    @property
    def seat_num(self) -> Dict[str, int]:
        # 座位号从1开始
        sn = 1

        seat_dict: Dict[str:int] = {}
        left_seats = self.poco("com.c2vl.kgamebox:id/lang_ren_room_left_member_list").children()
        for ls in left_seats:
            nn = ls.offspring("com.c2vl.kgamebox:id/user_name").get_text()
            if nn is not None:
                seat_dict[nn] = sn
            sn += 1

        right_seats = self.poco("com.c2vl.kgamebox:id/lang_ren_room_right_member_list").children()
        for ls in right_seats:
            nn = ls.offspring("com.c2vl.kgamebox:id/user_name").get_text()
            if nn is not None:
                seat_dict[nn] = sn
            sn += 1
        return seat_dict

    @retry(3)
    def seat_member_img(self, seat_num: int) -> UIObjectProxy:
        left_seats = self.poco("com.c2vl.kgamebox:id/lang_ren_room_left_member_list").children()
        right_seats = self.poco("com.c2vl.kgamebox:id/lang_ren_room_right_member_list").children()

        all_seats = []
        all_seats.extend(left_seats)
        all_seats.extend(right_seats)
        return all_seats[seat_num - 1]

    @property
    def send_gift_btn(self) -> UIObjectProxy:
        return self.poco("com.c2vl.kgamebox:id/send_gift")

    @property
    def latest_gift(self) -> Optional[Tuple[bool, int, int, Gift]]:
        try:
            poco = self.poco.freeze()
            gifts = poco("com.c2vl.kgamebox:id/chat_present_area")
            if len(gifts) == 0:
                return None

            gift_bubble = gifts[-1]
            name = gift_bubble.offspring("com.c2vl.kgamebox:id/chat_present_name").get_text()
            count_txt = gift_bubble.offspring("com.c2vl.kgamebox:id/chat_present_number").get_text()
            count = int(count_txt[count_txt.index('x') + 1:])
            pop_text = gift_bubble.offspring("com.c2vl.kgamebox:id/chat_present_add").get_text()
            popularity = int(pop_text[pop_text.index('人气') + 2:])
            effect_element = gift_bubble.offspring("com.c2vl.kgamebox:id/chat_present_effect")
            effect = ''
            if effect_element.exists():
                effect = effect_element.get_text()

            im_bubble = gift_bubble.parent()

            from_user_info = im_bubble.sibling("com.c2vl.kgamebox:id/chat_group_user_info")
            to_user_info = im_bubble.sibling("com.c2vl.kgamebox:id/ll_bottom_view").offspring(
                "com.c2vl.kgamebox:id/chat_group_user_info")

            is_send = False
            if not im_bubble.sibling("com.c2vl.kgamebox:id/chat_group_user_info").exists():
                # receive
                is_send = True
                from_user_info = im_bubble.parent().sibling("com.c2vl.kgamebox:id/chat_group_user_info")
                to_user_info = im_bubble.parent().sibling("com.c2vl.kgamebox:id/ll_bottom_view").offspring(
                    "com.c2vl.kgamebox:id/chat_group_user_info")

            from_sn = from_user_info.offspring("com.c2vl.kgamebox:id/chat_msg_user_type").get_text()
            to_sn = to_user_info.offspring("com.c2vl.kgamebox:id/chat_msg_user_type").get_text()

            return is_send, int(from_sn), int(to_sn), Gift(name, count, popularity, effect)
        except Exception:
            return None

    @property
    def unread_msg_label(self) -> UIObjectProxy:
        return self.poco("com.c2vl.kgamebox:id/btn_un_read_msg_bubble")

    def swipe_down(self):
        self.poco("android.widget.LinearLayout").offspring("android:id/content").offspring(
            "com.c2vl.kgamebox:id/room_area_back").offspring("android.widget.ListView").swipe([0, -0.2], None, 0.1)

    def select_poison_man_btn(self, seat_num: int) -> UIObjectProxy:
        parent = self.poco("android:id/content").offspring("com.c2vl.kgamebox:id/lang_ren_members_grid")
        parent.wait_for_appearance()
        children = parent.children()
        for child in children:
            text = child.child("com.c2vl.kgamebox:id/frame_layout").offspring(
                "com.c2vl.kgamebox:id/tv_room_member_index").get_text()
            if text == str(seat_num):
                return child
        raise RuntimeError('no element found')

    @property
    def witch_rescue_confirm_btn(self) -> UIObjectProxy:
        return self.poco("com.c2vl.kgamebox:id/step_confirm")

    @property
    def witch_rescue_cancel_btn(self) -> UIObjectProxy:
        return self.poco("com.c2vl.kgamebox:id/step_cancel")

    @property
    def witch_poison_confirm_btn(self) -> UIObjectProxy:
        return self.poco("com.c2vl.kgamebox:id/step_confirm")

    @property
    def witch_poison_cancel_btn(self) -> UIObjectProxy:
        return self.poco("com.c2vl.kgamebox:id/step_cancel")

    @property
    def speak_end_btn(self) -> UIObjectProxy:
        return self.poco("com.c2vl.kgamebox:id/finish_opt")

    def select_vote_man_btn(self, seat_num: int) -> UIObjectProxy:
        parent = self.poco("android:id/content").offspring("com.c2vl.kgamebox:id/lang_ren_members_grid")
        parent.wait_for_appearance()
        children = parent.children()
        for child in children:
            text = child.child("com.c2vl.kgamebox:id/frame_layout").offspring(
                "com.c2vl.kgamebox:id/tv_room_member_index").get_text()
            if text == str(seat_num):
                return child
        raise RuntimeError('no element found')

    @property
    def vote_confirm_btn(self) -> UIObjectProxy:
        return self.poco("com.c2vl.kgamebox:id/step_confirm")

    @property
    def vote_cancel_btn(self) -> UIObjectProxy:
        return self.poco("com.c2vl.kgamebox:id/step_cancel")

    @property
    def last_words_end_btn(self) -> UIObjectProxy:
        return self.poco("com.c2vl.kgamebox:id/finish_opt")

    @property
    def game_progress_label(self) -> UIObjectProxy:
        return self.poco("com.c2vl.kgamebox:id/tv_game_progress_description")

    @property
    def system_msg_labels(self) -> List[UIObjectProxy]:
        children = self.poco.freeze()("android.widget.LinearLayout").offspring("android:id/content").offspring(
            "com.c2vl.kgamebox:id/room_area_back").offspring("android.widget.ListView").children()
        results = []
        try:
            for child in children:
                if child.offspring("com.c2vl.kgamebox:id/chat_msg_system_type").exists():
                    results.append(child.offspring('com.c2vl.kgamebox:id/chat_from_text_content'))
        except Exception as e:
            # print(e)
            pass
        return results

    @property
    def latest_system_msg_label(self) -> UIObjectProxy:
        children = self.poco.freeze()("android.widget.LinearLayout").offspring("android:id/content").offspring(
            "com.c2vl.kgamebox:id/room_area_back").offspring("android.widget.ListView").children()

        for i in reversed(range(0, len(children))):
            try:
                if children[i].offspring("com.c2vl.kgamebox:id/chat_msg_system_type").exists():
                    return children[i].offspring('com.c2vl.kgamebox:id/chat_from_text_content')
            except Exception:
                pass

        raise RuntimeError('no element found')

    @property
    def vote_result_label(self) -> UIObjectProxy:
        children = self.poco("android.widget.LinearLayout").offspring("android:id/content").offspring(
            "com.c2vl.kgamebox:id/room_area_back").offspring("android.widget.ListView").children()

        for child in reversed(children):
            if child.offspring("com.c2vl.kgamebox:id/vote_description").exists():
                return child.offspring('com.c2vl.kgamebox:id/vote_description')

    @property
    def first_win_seat_num_label(self) -> UIObjectProxy:
        return self.poco("com.c2vl.kgamebox:id/gv_dialog_result_team_win").child("android.widget.RelativeLayout")[
            0].offspring(
            "com.c2vl.kgamebox:id/frame_layout").offspring("com.c2vl.kgamebox:id/tv_room_member_index")

    @property
    def game_result_win_dialog(self) -> UIObjectProxy:
        return self.poco("com.c2vl.kgamebox:id/gv_dialog_result_team_win")

    @property
    def kill_dialog_voice_btn(self) -> UIObjectProxy:
        return self.poco("com.c2vl.kgamebox:id/im_voice_hint")


class AndroidMessageTabPage(MessageTabPage):

    @property
    def msg_tab_btn(self) -> UIObjectProxy:
        return self.poco("android.widget.LinearLayout").offspring("android:id/content").offspring(
            "android.widget.LinearLayout").child("android.widget.ImageView")[1]

    @property
    def add_friend_btn(self) -> UIObjectProxy:
        return self.poco("com.c2vl.kgamebox:id/menu_message_fragment_search")

    @property
    def add_friend_id_input(self) -> UIObjectProxy:
        return self.poco("com.c2vl.kgamebox:id/content_edit_area")

    @property
    def add_friend_id_confirm_btn(self) -> UIObjectProxy:
        return self.poco("com.c2vl.kgamebox:id/dialog_positive_btn")


class AndroidMyTabPage(MyTabPage):

    @property
    def my_tab_btn(self) -> UIObjectProxy:
        return self.poco("android.widget.LinearLayout").offspring("android:id/content").offspring(
            "android.widget.LinearLayout").child("android.widget.ImageView")[2]

    @property
    def personal_card_btn(self) -> UIObjectProxy:
        return self.poco("com.c2vl.kgamebox:id/person_card")


class AndroidUserHomePage(UserHomePage):

    @property
    def private_msg_btn(self) -> UIObjectProxy:
        return self.poco("com.c2vl.kgamebox:id/iv_bottom_bar_send_message")

    @property
    def edit_profile_btn(self) -> UIObjectProxy:
        return self.poco("com.c2vl.kgamebox:id/menu_person_home_edit")

    @property
    def nickname_label(self) -> UIObjectProxy:
        return self.poco("com.c2vl.kgamebox:id/tv_person_home_user_nickname")

    @property
    def signature_label(self) -> UIObjectProxy:
        return self.poco("com.c2vl.kgamebox:id/tv_person_home_description")


class AndroidUserProfileEditPage(UserProfileEditPage):

    @property
    def buy_vip_dialog(self) -> UIObjectProxy:
        return self.poco("com.c2vl.kgamebox:id/iv_buy_vip")

    @property
    def nickname_input(self) -> UIObjectProxy:
        return self.poco("com.c2vl.kgamebox:id/et_edit_info_item_content")

    @property
    def signature_input(self) -> UIObjectProxy:
        return self.poco("com.c2vl.kgamebox:id/et_edit_info_item_content")

    @property
    def confirm_crop_btn(self) -> UIObjectProxy:
        return self.poco("com.c2vl.kgamebox:id/menu_crop")


class AndroidUserGenderEditPage(UserGenderEditPage):

    @property
    def selected_gender(self) -> int:
        return 1 if self.poco("com.c2vl.kgamebox:id/rb_user_data_male").exists() else 0

    @property
    def gender_card_num(self) -> int:
        num_txt = self.poco("com.c2vl.kgamebox:id/img_gender_card").get_text()
        return int(num_txt[1:])

    @property
    def edit_btn(self) -> UIObjectProxy:
        return self.poco("com.c2vl.kgamebox:id/sure_gender_change")


class AndroidPrivateMessagePage(PrivateMessagePage):

    @property
    def send_gift_btn(self) -> UIObjectProxy:
        return self.poco("com.c2vl.kgamebox:id/present_btn")

    @property
    def latest_gift(self) -> Optional[Tuple[bool, Gift]]:
        try:
            gift_bubbles = self.poco("com.c2vl.kgamebox:id/chat_area").offspring("com.c2vl.kgamebox:id/list").offspring(
                "android.widget.ListView").child('android.widget.RelativeLayout').child(
                "com.c2vl.kgamebox:id/rl_root_bubble")
            if len(gift_bubbles) == 0:
                return None
            is_send = True
            gift_bubble = gift_bubbles[len(gift_bubbles) - 1]
            if gift_bubble.offspring('com.c2vl.kgamebox:id/ll_bubble').exists():
                # receive
                is_send = False
            else:
                gift_bubble = gift_bubble.offspring("android.widget.LinearLayout")
                # send

            name = gift_bubble.offspring("com.c2vl.kgamebox:id/chat_present_name").get_text()
            count_txt = gift_bubble.offspring("com.c2vl.kgamebox:id/chat_present_number").get_text()
            count = int(count_txt[count_txt.index('x') + 1:])
            pop_text = gift_bubble.offspring("com.c2vl.kgamebox:id/chat_present_add").get_text()
            popularity = int(pop_text[pop_text.index('人气') + 2:])
            effect_element = gift_bubble.offspring("com.c2vl.kgamebox:id/chat_present_effect")
            effect = ''
            if effect_element.exists():
                effect = effect_element.get_text()
            return is_send, Gift(name, count, popularity, effect)
        except Exception as e:
            # print(e)
            return None

    @property
    def unread_msg_label(self) -> UIObjectProxy:
        return self.poco("com.c2vl.kgamebox:id/btn_un_read_msg_bubble")

    def swipe_down(self):
        self.poco("com.c2vl.kgamebox:id/chat_area").swipe([0, -0.2], None, 0.1)


class AndroidGiftPanel(GiftPanel):

    @property
    def open_select_num_btn(self) -> UIObjectProxy:
        return self.poco("com.c2vl.kgamebox:id/gift_num_pick")

    def gift_num_btn(self, num: str) -> UIObjectProxy:
        return self.poco(text=num)

    @property
    def gift_num_input(self) -> UIObjectProxy:
        return self.poco("com.c2vl.kgamebox:id/gift_num_input")

    @property
    def gift_num_input_confirm_btn(self) -> UIObjectProxy:
        return self.poco("com.c2vl.kgamebox:id/gift_num_input_confirm")

    def swipe_prev(self):
        self.poco("com.c2vl.kgamebox:id/gift_select_grid").swipe([1, 0.0], None, 0.1)

    def swipe_next(self):
        self.poco("com.c2vl.kgamebox:id/gift_select_grid").swipe([-1, 0.0], None, 0.1)

    @retry(3)
    def gift_names(self) -> List[Tuple[bool, UIObjectProxy]]:
        grid = self.poco("com.c2vl.kgamebox:id/gift_select_grid").children()

        gifts = []
        for child in grid:
            label = child.child("android.widget.LinearLayout").offspring("com.c2vl.kgamebox:id/decoration_name")
            selected = child.child('com.c2vl.kgamebox:id/decoration_cover').exists()
            gifts.append((selected, label))
        return gifts

    @property
    def confirm_send_btn(self) -> UIObjectProxy:
        return self.poco("com.c2vl.kgamebox:id/gift_send")


class AndroidPageRoot(PageRoot):

    def __init__(self):
        self.__poco = (AndroidUiautomationPoco(device=G.DEVICE, screenshot_each_action=False))
        self.__home_page = AndroidHomeTabPage(self.__poco)
        self.__game_room_page = AndroidGameRoomPage(self.__poco)
        self.__message_tab_page = AndroidMessageTabPage(self.__poco)
        self.__my_tab_page = AndroidMyTabPage(self.__poco)
        self.__user_home_page = AndroidUserHomePage(self.__poco)
        self.__user_profile_edit_page = AndroidUserProfileEditPage(self.__poco)
        self.__user_gender_edit_page = AndroidUserGenderEditPage(self.__poco)
        self.__private_message_page = AndroidPrivateMessagePage(self.__poco)
        self.__gift_panel = AndroidGiftPanel(self.__poco)

    def dispose(self):
        self.__poco.stop_running()

    @property
    def poco(self) -> Poco:
        return self.__poco

    @property
    def back_btn(self) -> UIObjectProxy:
        return self.poco("android.widget.ImageButton")[0]

    @property
    def home_tab_page(self) -> HomeTabPage:
        return self.__home_page

    @property
    def game_room_page(self) -> GameRoomPage:
        return self.__game_room_page

    @property
    def message_tab_page(self) -> MessageTabPage:
        return self.__message_tab_page

    @property
    def my_tab_page(self) -> MyTabPage:
        return self.__my_tab_page

    @property
    def user_home_page(self) -> UserHomePage:
        return self.__user_home_page

    @property
    def user_profile_edit_page(self) -> UserProfileEditPage:
        return self.__user_profile_edit_page

    @property
    def user_gender_edit_page(self) -> UserGenderEditPage:
        return self.__user_gender_edit_page

    @property
    def private_message_page(self) -> PrivateMessagePage:
        return self.__private_message_page

    @property
    def gift_panel(self) -> GiftPanel:
        return self.__gift_panel
