<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="DataSourceManagerImpl" format="xml" multifile-model="true">
    <data-source source="LOCAL" name="ClickHouse - @cc-bp1j6i01q18k59m5q.clickhouse.ads.aliyuncs.com" uuid="65f7d6e2-515f-4582-86a5-94860b6f65e2">
      <driver-ref>clickhouse</driver-ref>
      <synchronize>true</synchronize>
      <jdbc-driver>ru.yandex.clickhouse.ClickHouseDriver</jdbc-driver>
      <jdbc-url>***********************************************************************</jdbc-url>
    </data-source>
    <data-source source="LOCAL" name="boom" uuid="fa322cdd-1a7c-446b-8424-29ed67c28f7a">
      <driver-ref>mysql.8</driver-ref>
      <synchronize>true</synchronize>
      <jdbc-driver>com.mysql.cj.jdbc.Driver</jdbc-driver>
      <jdbc-url>************************************************************</jdbc-url>
    </data-source>
    <data-source source="LOCAL" name="console.db" uuid="b78e810c-8a45-4450-a577-293eb5618232">
      <driver-ref>sqlite.xerial</driver-ref>
      <synchronize>true</synchronize>
      <jdbc-driver>org.sqlite.JDBC</jdbc-driver>
      <jdbc-url>*************************************************</jdbc-url>
    </data-source>
  </component>
</project>