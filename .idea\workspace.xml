<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="04548e8a-747b-4f92-8b46-50ec5245575e" name="Default Changelist" comment="">
      <change beforePath="$PROJECT_DIR$/SmallTools/updownv3/app.py" beforeDir="false" afterPath="$PROJECT_DIR$/SmallTools/updownv3/app.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/SmallTools/updownv3/build.bat" beforeDir="false" afterPath="$PROJECT_DIR$/SmallTools/updownv3/build.bat" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/SmallTools/updownv3/templates/index.html" beforeDir="false" afterPath="$PROJECT_DIR$/SmallTools/updownv3/templates/index.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/SmallTools/updownv4/app.py" beforeDir="false" afterPath="$PROJECT_DIR$/SmallTools/updownv4/app.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/SmallTools/updownv4/build.bat" beforeDir="false" afterPath="$PROJECT_DIR$/SmallTools/updownv4/build.bat" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/SmallTools/updownv4/templates/index.html" beforeDir="false" afterPath="$PROJECT_DIR$/SmallTools/updownv4/templates/index.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/langren-test/.gitignore" beforeDir="false" afterPath="$PROJECT_DIR$/langren-test/.gitignore" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/langren-test/langren-iftest/.gitignore" beforeDir="false" afterPath="$PROJECT_DIR$/langren-test/langren-iftest/.gitignore" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/langren-test/langren-iftest/collect_gift_effect.py" beforeDir="false" afterPath="$PROJECT_DIR$/langren-test/langren-iftest/collect_gift_effect.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/langren-test/langren-iftest/resource/send_gift_effect.xlsx" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/langren-test/langren-iftest/resource/send_gift_validate.xlsx" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/langren-test/langren-iftest/validate_send_gift.py" beforeDir="false" afterPath="$PROJECT_DIR$/langren-test/langren-iftest/validate_send_gift.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/langren-test/langren-uitest/.gitignore" beforeDir="false" afterPath="$PROJECT_DIR$/langren-test/langren-uitest/.gitignore" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="DatabaseView">
    <option name="SHOW_INTERMEDIATE" value="true" />
    <option name="GROUP_DATA_SOURCES" value="true" />
    <option name="GROUP_SCHEMA" value="true" />
    <option name="GROUP_CONTENTS" value="false" />
    <option name="SORT_POSITIONED" value="false" />
    <option name="SHOW_EMPTY_GROUPS" value="false" />
    <option name="AUTO_SCROLL_FROM_SOURCE" value="false" />
    <option name="HIDDEN_KINDS">
      <set />
    </option>
    <expand>
      <path>
        <item name="Database" type="3277223f:DatabaseStructure$DbRootGroup" />
        <item name="ClickHouse - @cc-bp1j6i01q18k59m5q.clickhouse.ads.aliyuncs.com" type="feb32156:DbDataSourceImpl" />
      </path>
      <path>
        <item name="Database" type="3277223f:DatabaseStructure$DbRootGroup" />
        <item name="qa1 holostatic" type="feb32156:DbDataSourceImpl" />
      </path>
      <path>
        <item name="Database" type="3277223f:DatabaseStructure$DbRootGroup" />
        <item name="qa1 holostatic" type="feb32156:DbDataSourceImpl" />
        <item name="databases" type="d4e8921:DatabaseStructure$FamilyGroup" />
      </path>
      <path>
        <item name="Database" type="3277223f:DatabaseStructure$DbRootGroup" />
        <item name="qa1 holostatic" type="feb32156:DbDataSourceImpl" />
        <item name="databases" type="d4e8921:DatabaseStructure$FamilyGroup" />
        <item name="lr_statistic_hologres: database" type="162271b6:PgImplModel$Database" />
      </path>
      <path>
        <item name="Database" type="3277223f:DatabaseStructure$DbRootGroup" />
        <item name="qa1 holostatic" type="feb32156:DbDataSourceImpl" />
        <item name="databases" type="d4e8921:DatabaseStructure$FamilyGroup" />
        <item name="lr_statistic_hologres: database" type="162271b6:PgImplModel$Database" />
        <item name="schemas" type="d4e8921:DatabaseStructure$FamilyGroup" />
      </path>
      <path>
        <item name="Database" type="3277223f:DatabaseStructure$DbRootGroup" />
        <item name="qa1 holostatic" type="feb32156:DbDataSourceImpl" />
        <item name="databases" type="d4e8921:DatabaseStructure$FamilyGroup" />
        <item name="lr_statistic_hologres: database" type="162271b6:PgImplModel$Database" />
        <item name="schemas" type="d4e8921:DatabaseStructure$FamilyGroup" />
        <item name="public: schema" type="983ebcf7:PgImplModel$Schema" />
      </path>
      <path>
        <item name="Database" type="3277223f:DatabaseStructure$DbRootGroup" />
        <item name="qa1 holostatic" type="feb32156:DbDataSourceImpl" />
        <item name="databases" type="d4e8921:DatabaseStructure$FamilyGroup" />
        <item name="lr_statistic_hologres: database" type="162271b6:PgImplModel$Database" />
        <item name="schemas" type="d4e8921:DatabaseStructure$FamilyGroup" />
        <item name="public: schema" type="983ebcf7:PgImplModel$Schema" />
        <item name="tables" type="d4e8921:DatabaseStructure$FamilyGroup" />
      </path>
    </expand>
    <select />
  </component>
  <component name="FavoritesManager">
    <favorites_list name="pystorage" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="HTML File" />
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$/langren-test" value="yyq" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/mu-project" />
    <option name="ROOT_SYNC" value="DONT_SYNC" />
  </component>
  <component name="GithubPullRequestsUISettings">
    <option name="hiddenUrls">
      <set>
        <option value="**************:sunwusunwu/-Autotest.git" />
        <option value="**************:sunwusunwu/Autotest01.git" />
      </set>
    </option>
  </component>
  <component name="ProjectId" id="2BEZztE9JuamsYD0GZJ0KWolqS3" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showExcludedFiles" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">
    <property name="DatabaseDriversLRU" value="clickhouse&#10;mysql&#10;sqlite&#10;postgresql" />
    <property name="DefaultHtmlFileTemplate" value="HTML File" />
    <property name="RunOnceActivity.ShowReadmeOnStart" value="true" />
    <property name="WebServerToolWindowFactoryState" value="false" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$" />
    <property name="nodejs_package_manager_path" value="npm" />
    <property name="settings.editor.selected.configurable" value="vcs.Git" />
    <property name="two.files.diff.last.used.file" value="$PROJECT_DIR$/langren_admin2/admin_api_create.py" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\pystorage\langren_admin71" />
      <recent name="D:\pystorage" />
      <recent name="D:\pystorage\Voyage" />
      <recent name="D:\pystorage\SmallTools" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\pystorage\Voyage" />
      <recent name="D:\pystorage\SmallTools" />
    </key>
  </component>
  <component name="RunManager" selected="Python.ClientFunc">
    <configuration name="ClientFunc" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="pystorage" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/Voyage" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/Voyage/ClientFunc.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="ServerFunc" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="pystorage" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/Voyage" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/Voyage/ServerFunc.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="edgemission" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="pystorage" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/SmallTools" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/SmallTools/edgemission.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="join" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="pystorage" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/join.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="minigamee" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="pystorage" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/SmallTools" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/SmallTools/minigamee.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Python.ClientFunc" />
        <item itemvalue="Python.ServerFunc" />
        <item itemvalue="Python.join" />
        <item itemvalue="Python.edgemission" />
        <item itemvalue="Python.minigamee" />
      </list>
    </recent_temporary>
  </component>
  <component name="ServiceViewManager">
    <option name="viewStates">
      <list>
        <serviceView>
          <treeState>
            <expand>
              <path>
                <item name="services root" type="e789fda9:ObjectUtils$Sentinel" />
                <item name="lr_statistic_hologres: root" type="7427dc5b:ServiceModel$ServiceGroupNode" />
              </path>
              <path>
                <item name="services root" type="e789fda9:ObjectUtils$Sentinel" />
                <item name="lr_statistic_hologres: root" type="7427dc5b:ServiceModel$ServiceGroupNode" />
                <item name="com.intellij.database.console.session.MessageBusSession@1e8cd36f" type="9fbbdea:ServiceModel$ServiceNode" />
              </path>
              <path>
                <item name="services root" type="e789fda9:ObjectUtils$Sentinel" />
                <item name="qa1 holostatic: root" type="7427dc5b:ServiceModel$ServiceGroupNode" />
              </path>
              <path>
                <item name="services root" type="e789fda9:ObjectUtils$Sentinel" />
                <item name="qa1 holostatic: root" type="7427dc5b:ServiceModel$ServiceGroupNode" />
                <item name="com.intellij.database.console.session.MessageBusSession@43703443" type="9fbbdea:ServiceModel$ServiceNode" />
              </path>
            </expand>
            <select />
          </treeState>
        </serviceView>
      </list>
    </option>
  </component>
  <component name="SvnConfiguration">
    <configuration />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="04548e8a-747b-4f92-8b46-50ec5245575e" name="Default Changelist" comment="" />
      <created>1656473470647</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1656473470647</updated>
      <workItem from="1656473473611" duration="40000" />
      <workItem from="1656473522363" duration="48000" />
      <workItem from="1670838371454" duration="4474000" />
      <workItem from="1671000200236" duration="8345000" />
      <workItem from="1672816132063" duration="1000" />
      <workItem from="1672816402349" duration="1000" />
      <workItem from="1672825070029" duration="608000" />
      <workItem from="1673431358786" duration="8139000" />
      <workItem from="1673597900415" duration="2152000" />
      <workItem from="1673925585223" duration="7261000" />
      <workItem from="1674873100831" duration="10785000" />
      <workItem from="1674972046451" duration="4985000" />
      <workItem from="1675239999774" duration="23200000" />
      <workItem from="1675677807354" duration="366000" />
      <workItem from="1675757014907" duration="1028000" />
      <workItem from="1675763843931" duration="20622000" />
      <workItem from="1675925893819" duration="10997000" />
      <workItem from="1676430322142" duration="770000" />
      <workItem from="1676535763846" duration="10352000" />
      <workItem from="1677050669186" duration="598000" />
      <workItem from="1677565433968" duration="473000" />
      <workItem from="1677642066595" duration="19336000" />
      <workItem from="1680058324895" duration="1950000" />
      <workItem from="1681698288117" duration="1250000" />
      <workItem from="1682505278978" duration="3148000" />
      <workItem from="1682586175453" duration="6639000" />
      <workItem from="1683513521408" duration="5513000" />
      <workItem from="1684131109910" duration="21500000" />
      <workItem from="1684913393784" duration="7771000" />
      <workItem from="1685435195382" duration="4666000" />
      <workItem from="1686215921566" duration="16613000" />
      <workItem from="1687862330627" duration="1000" />
      <workItem from="1688455837459" duration="10000" />
      <workItem from="1688635728638" duration="4055000" />
      <workItem from="1689322705703" duration="4639000" />
      <workItem from="1689905291220" duration="46663000" />
      <workItem from="1690273195217" duration="426000" />
      <workItem from="1690276791838" duration="17371000" />
      <workItem from="1690453543849" duration="1423000" />
      <workItem from="1690599541458" duration="5315000" />
      <workItem from="1690784261809" duration="8262000" />
      <workItem from="1690870178877" duration="600000" />
      <workItem from="1690876491431" duration="22235000" />
      <workItem from="1691290147971" duration="52364000" />
      <workItem from="1691732826621" duration="4494000" />
      <workItem from="1691982133643" duration="1294000" />
      <workItem from="1692071537377" duration="151000" />
      <workItem from="1692085922576" duration="148000" />
      <workItem from="1692087199600" duration="6348000" />
      <workItem from="1692176149202" duration="5159000" />
      <workItem from="1692325825826" duration="5453000" />
      <workItem from="1692789381851" duration="8000" />
      <workItem from="1692792503608" duration="6869000" />
      <workItem from="1692877937036" duration="2834000" />
      <workItem from="1692951625985" duration="600000" />
      <workItem from="1693208109839" duration="10886000" />
      <workItem from="1693463963540" duration="5000" />
      <workItem from="1693464489621" duration="20000" />
      <workItem from="1693466018268" duration="27425000" />
      <workItem from="1695107133329" duration="95000" />
      <workItem from="1695290971878" duration="2092000" />
      <workItem from="1695351707785" duration="596000" />
      <workItem from="1695369890849" duration="1000" />
      <workItem from="1695371643269" duration="18675000" />
      <workItem from="1696646258535" duration="14585000" />
      <workItem from="1696917928769" duration="18268000" />
      <workItem from="1697182392207" duration="26000" />
      <workItem from="1697182573605" duration="4459000" />
      <workItem from="1697424972758" duration="32944000" />
      <workItem from="1698916599336" duration="295000" />
      <workItem from="1698996527511" duration="185000" />
      <workItem from="1699499681227" duration="1198000" />
      <workItem from="1699867181423" duration="10459000" />
      <workItem from="1700644253056" duration="12724000" />
      <workItem from="1701310155667" duration="11126000" />
      <workItem from="1701682574061" duration="599000" />
      <workItem from="1701852333900" duration="1523000" />
      <workItem from="1701917231312" duration="1337000" />
      <workItem from="1701919863100" duration="2520000" />
      <workItem from="1701932887713" duration="1970000" />
      <workItem from="1701937893413" duration="8848000" />
      <workItem from="1702456421201" duration="1548000" />
      <workItem from="1702630391431" duration="613000" />
      <workItem from="1703066574316" duration="13905000" />
      <workItem from="1704682480329" duration="2640000" />
      <workItem from="1706596379172" duration="686000" />
      <workItem from="1706683356515" duration="1108000" />
      <workItem from="1706685449542" duration="1289000" />
      <workItem from="1706756845187" duration="2811000" />
      <workItem from="1708400152913" duration="2765000" />
      <workItem from="1708420587351" duration="30625000" />
      <workItem from="1711450865082" duration="1000" />
      <workItem from="1711526766376" duration="16000" />
      <workItem from="1711607293424" duration="7348000" />
      <workItem from="1712025194142" duration="12554000" />
      <workItem from="1712814091258" duration="37718000" />
      <workItem from="1713504888316" duration="5746000" />
      <workItem from="1713774665272" duration="2692000" />
      <workItem from="1713938751521" duration="51155000" />
      <workItem from="1714458357073" duration="1318000" />
      <workItem from="1714981027562" duration="873000" />
      <workItem from="1715135900989" duration="7193000" />
      <workItem from="1715394907833" duration="16712000" />
      <workItem from="1715653446365" duration="4227000" />
      <workItem from="1715668123820" duration="1696000" />
      <workItem from="1715671888089" duration="175000" />
      <workItem from="1715672771914" duration="9591000" />
      <workItem from="1715738554012" duration="13718000" />
      <workItem from="1715848889392" duration="3000" />
      <workItem from="1715854456088" duration="1000" />
      <workItem from="1715855811427" duration="624000" />
      <workItem from="1715929249086" duration="3000" />
      <workItem from="1716171261314" duration="596000" />
      <workItem from="1716199055615" duration="594000" />
      <workItem from="1716434373861" duration="2396000" />
      <workItem from="1716948042568" duration="4682000" />
      <workItem from="1717062084729" duration="23000" />
      <workItem from="1717062248559" duration="133000" />
      <workItem from="1717470858991" duration="4821000" />
      <workItem from="1717644008465" duration="6635000" />
      <workItem from="1719201640101" duration="5700000" />
      <workItem from="1719818278368" duration="15570000" />
      <workItem from="1720429764359" duration="36857000" />
      <workItem from="1721183275532" duration="34711000" />
      <workItem from="1722924450642" duration="11127000" />
      <workItem from="1723455376456" duration="606000" />
      <workItem from="1723520953234" duration="1795000" />
      <workItem from="1724382083561" duration="32655000" />
      <workItem from="1725415805422" duration="19603000" />
      <workItem from="1726122308594" duration="18394000" />
      <workItem from="1726646558948" duration="9560000" />
      <workItem from="1726798893472" duration="37837000" />
      <workItem from="1728378812144" duration="33794000" />
      <workItem from="1730182318635" duration="15832000" />
      <workItem from="1731308529711" duration="6743000" />
      <workItem from="1731900694603" duration="2160000" />
      <workItem from="1732075719372" duration="28017000" />
      <workItem from="1732772286890" duration="28762000" />
      <workItem from="1733106316065" duration="7498000" />
      <workItem from="1733129366957" duration="3612000" />
      <workItem from="1733378417234" duration="3134000" />
      <workItem from="1733468333746" duration="613000" />
      <workItem from="1733797616671" duration="14851000" />
      <workItem from="1733884385923" duration="42076000" />
      <workItem from="1734598121109" duration="39775000" />
      <workItem from="1735610796659" duration="13465000" />
      <workItem from="1735788442490" duration="59609000" />
      <workItem from="1736849097719" duration="19407000" />
      <workItem from="1737085545511" duration="8162000" />
      <workItem from="1737689194722" duration="572000" />
      <workItem from="1738743824468" duration="7837000" />
      <workItem from="1739155995876" duration="915000" />
      <workItem from="1739157247738" duration="1374000" />
      <workItem from="1739159864591" duration="1328000" />
      <workItem from="1739172528278" duration="764000" />
      <workItem from="1739183650177" duration="1000" />
      <workItem from="1739240917341" duration="934000" />
      <workItem from="1739263984785" duration="7986000" />
      <workItem from="1739514523243" duration="593000" />
      <workItem from="1739781382067" duration="3212000" />
      <workItem from="1739932311980" duration="15398000" />
      <workItem from="1741686354909" duration="71000" />
      <workItem from="1741688834316" duration="6000" />
      <workItem from="1741853817014" duration="1348000" />
      <workItem from="1742370340822" duration="3295000" />
      <workItem from="1742807248881" duration="76000" />
      <workItem from="1743066522390" duration="3033000" />
      <workItem from="1744167343696" duration="2637000" />
      <workItem from="1745207697751" duration="1941000" />
      <workItem from="1745562670523" duration="662000" />
      <workItem from="1745565449347" duration="5582000" />
      <workItem from="1745827504920" duration="1190000" />
      <workItem from="1746502331000" duration="7644000" />
      <workItem from="1747204800075" duration="9533000" />
      <workItem from="1749705966157" duration="754000" />
      <workItem from="1750392550254" duration="2176000" />
      <workItem from="1750917214408" duration="6202000" />
      <workItem from="1751945930021" duration="72199000" />
      <workItem from="1754373897130" duration="80545000" />
      <workItem from="1756094545687" duration="27000" />
      <workItem from="1756102754814" duration="52000" />
      <workItem from="1756102850792" duration="42000" />
      <workItem from="1756103318800" duration="440000" />
      <workItem from="1756103784944" duration="5000" />
      <workItem from="1756103802395" duration="400000" />
      <workItem from="1756280523988" duration="11476000" />
      <workItem from="1756804322983" duration="86000" />
      <workItem from="1756885678775" duration="7050000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="1" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="COLUMN_ORDER" />
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="WindowStateProjectService">
    <state x="651" y="350" key="#HTTP_Proxy" timestamp="1754897065974">
      <screen x="0" y="0" width="1920" height="1040" />
    </state>
    <state x="651" y="350" key="#HTTP_Proxy/0.0.1920.1040@0.0.1920.1040" timestamp="1754897065974" />
    <state x="552" y="181" key="#com.intellij.execution.impl.EditConfigurationsDialog" timestamp="1754898756459">
      <screen x="0" y="0" width="1920" height="1040" />
    </state>
    <state x="552" y="181" key="#com.intellij.execution.impl.EditConfigurationsDialog/0.0.1920.1040@0.0.1920.1040" timestamp="1754898756459" />
    <state x="671" y="328" key="#com.intellij.fileTypes.FileTypeChooser" timestamp="1753775176041">
      <screen x="0" y="0" width="1920" height="1040" />
    </state>
    <state x="671" y="328" key="#com.intellij.fileTypes.FileTypeChooser/0.0.1920.1040@0.0.1920.1040" timestamp="1753775176041" />
    <state x="874" y="287" key="FileChooserDialogImpl" timestamp="1756804408354">
      <screen x="0" y="0" width="1920" height="1040" />
    </state>
    <state x="874" y="287" key="FileChooserDialogImpl/0.0.1920.1040@0.0.1920.1040" timestamp="1756804408354" />
    <state width="1400" height="246" key="GridCell.Tab.0.bottom" timestamp="1757404916439">
      <screen x="0" y="0" width="1920" height="1040" />
    </state>
    <state width="1400" height="246" key="GridCell.Tab.0.bottom/0.0.1920.1040@0.0.1920.1040" timestamp="1757404916439" />
    <state width="1400" height="246" key="GridCell.Tab.0.center" timestamp="1757404916438">
      <screen x="0" y="0" width="1920" height="1040" />
    </state>
    <state width="1400" height="246" key="GridCell.Tab.0.center/0.0.1920.1040@0.0.1920.1040" timestamp="1757404916438" />
    <state width="1400" height="246" key="GridCell.Tab.0.left" timestamp="1757404916438">
      <screen x="0" y="0" width="1920" height="1040" />
    </state>
    <state width="1400" height="246" key="GridCell.Tab.0.left/0.0.1920.1040@0.0.1920.1040" timestamp="1757404916438" />
    <state width="1400" height="246" key="GridCell.Tab.0.right" timestamp="1757404916438">
      <screen x="0" y="0" width="1920" height="1040" />
    </state>
    <state width="1400" height="246" key="GridCell.Tab.0.right/0.0.1920.1040@0.0.1920.1040" timestamp="1757404916438" />
    <state width="220" height="477" key="HiddenNamespacesPopup" timestamp="1754898780006">
      <screen x="0" y="0" width="1920" height="1040" />
    </state>
    <state width="220" height="477" key="HiddenNamespacesPopup/0.0.1920.1040@0.0.1920.1040" timestamp="1754898780006" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/langren-test/langren-iftest/util/method_tool.py</url>
          <line>4</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/pystorage$templiveV2__1_.coverage" NAME="templiveV2 (1) Coverage Results" MODIFIED="1701079128364" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/SmallTools" />
    <SUITE FILE_PATH="coverage/pystorage$CalTax.coverage" NAME="CalTax Coverage Results" MODIFIED="1713170981972" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/SmallTools" />
    <SUITE FILE_PATH="coverage/pystorage$getsign.coverage" NAME="getsign Coverage Results" MODIFIED="1715745642302" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/BOOM/boom2" />
    <SUITE FILE_PATH="coverage/pystorage$minigameeeeeee.coverage" NAME="minigameeeeeee Coverage Results" MODIFIED="1720597096095" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/SmallTools" />
    <SUITE FILE_PATH="coverage/pystorage$apollo_change.coverage" NAME="apollo_change Coverage Results" MODIFIED="1732271221571" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Voyage" />
    <SUITE FILE_PATH="coverage/pystorage$chat.coverage" NAME="chat Coverage Results" MODIFIED="1686298382669" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Voyage" />
    <SUITE FILE_PATH="coverage/pystorage$api_requests.coverage" NAME="api_requests Coverage Results" MODIFIED="1724835166665" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/langrenadmin" />
    <SUITE FILE_PATH="coverage/pystorage$caogao666.coverage" NAME="caogao666 Coverage Results" MODIFIED="1701339888437" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/SmallTools" />
    <SUITE FILE_PATH="coverage/pystorage$4444.coverage" NAME="4444 Coverage Results" MODIFIED="1727423709319" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Voyage" />
    <SUITE FILE_PATH="coverage/pystorage$caogao.coverage" NAME="caogao Coverage Results" MODIFIED="1747206941187" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/langren_admin71" />
    <SUITE FILE_PATH="coverage/pystorage$caogao999.coverage" NAME="caogao999 Coverage Results" MODIFIED="1715399555592" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/SmallTools" />
    <SUITE FILE_PATH="coverage/pystorage$clientsign.coverage" NAME="clientsign Coverage Results" MODIFIED="1675408377242" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/BOOM/boom" />
    <SUITE FILE_PATH="coverage/pystorage$app.coverage" NAME="app Coverage Results" MODIFIED="1741660727932" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/SmallTools/uploadimage" />
    <SUITE FILE_PATH="coverage/pystorage$caogao8.coverage" NAME="caogao8 Coverage Results" MODIFIED="1690189412755" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/SmallTools" />
    <SUITE FILE_PATH="coverage/pystorage$qa1_admin_api_202409061803.coverage" NAME="qa1_admin_api_202409061803 Coverage Results" MODIFIED="1732778184377" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/langren_admin2" />
    <SUITE FILE_PATH="coverage/pystorage$caogao2.coverage" NAME="caogao2 Coverage Results" MODIFIED="1735796976705" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/langren_admin71" />
    <SUITE FILE_PATH="coverage/pystorage$app__1_.coverage" NAME="app (1) Coverage Results" MODIFIED="1741665784635" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/SmallTools/updown" />
    <SUITE FILE_PATH="coverage/pystorage$BoomServerFunc.coverage" NAME="BoomServerFunc Coverage Results" MODIFIED="1713423402778" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/BOOM/boom" />
    <SUITE FILE_PATH="coverage/pystorage$templiveV2.coverage" NAME="templiveV2 Coverage Results" MODIFIED="1703668007231" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/SmallTools" />
    <SUITE FILE_PATH="coverage/pystorage$readytolive2__1_.coverage" NAME="readytolive2 (1) Coverage Results" MODIFIED="1695786173442" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/SmallTools" />
    <SUITE FILE_PATH="coverage/pystorage$apitest2.coverage" NAME="apitest2 Coverage Results" MODIFIED="1732790140355" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/langren_admin71" />
    <SUITE FILE_PATH="coverage/pystorage$caogao10.coverage" NAME="caogao10 Coverage Results" MODIFIED="1690168021248" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/SmallTools" />
    <SUITE FILE_PATH="coverage/pystorage$google1__1_.coverage" NAME="google1 (1) Coverage Results" MODIFIED="1725588892216" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/SmallTools" />
    <SUITE FILE_PATH="coverage/pystorage$create_excel.coverage" NAME="create_excel Coverage Results" MODIFIED="1747712428386" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/langren_admin71" />
    <SUITE FILE_PATH="coverage/pystorage$jdmate70.coverage" NAME="jdmate70 Coverage Results" MODIFIED="1740114489695" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/pystorage$apitest.coverage" NAME="apitest Coverage Results" MODIFIED="1732787240566" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/langren_admin71" />
    <SUITE FILE_PATH="coverage/pystorage$minigamee.coverage" NAME="minigamee Coverage Results" MODIFIED="1754548016877" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/SmallTools" />
    <SUITE FILE_PATH="coverage/pystorage$caogao8989898.coverage" NAME="caogao8989898 Coverage Results" MODIFIED="1747640205667" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/SmallTools" />
    <SUITE FILE_PATH="coverage/pystorage$temptest__1_.coverage" NAME="temptest (1) Coverage Results" MODIFIED="1727583082671" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Voyage" />
    <SUITE FILE_PATH="coverage/pystorage$liveeeeeeeeeeee2__1_.coverage" NAME="liveeeeeeeeeeee2 (1) Coverage Results" MODIFIED="1694513317114" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/SmallTools" />
    <SUITE FILE_PATH="coverage/pystorage$admin_api_202408281818.coverage" NAME="admin_api_202408281818 Coverage Results" MODIFIED="1724898753788" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/langren_admin" />
    <SUITE FILE_PATH="coverage/pystorage$templive.coverage" NAME="templive Coverage Results" MODIFIED="1703668025112" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/SmallTools" />
    <SUITE FILE_PATH="coverage/pystorage$ServerFunc.coverage" NAME="ServerFunc Coverage Results" MODIFIED="1757403645668" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Voyage" />
    <SUITE FILE_PATH="coverage/pystorage$obscaogao.coverage" NAME="obscaogao Coverage Results" MODIFIED="1714357660965" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/SmallTools" />
    <SUITE FILE_PATH="coverage/pystorage$5555.coverage" NAME="5555 Coverage Results" MODIFIED="1727427943844" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Voyage" />
    <SUITE FILE_PATH="coverage/pystorage$Appsign.coverage" NAME="Appsign Coverage Results" MODIFIED="1729239760958" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Voyage" />
    <SUITE FILE_PATH="coverage/pystorage$admin_api_202408281811.coverage" NAME="admin_api_202408281811 Coverage Results" MODIFIED="1724839932899" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/langren_admin" />
    <SUITE FILE_PATH="coverage/pystorage$gamecenter.coverage" NAME="gamecenter Coverage Results" MODIFIED="1740389530937" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/BOOM" />
    <SUITE FILE_PATH="coverage/pystorage$GAOZI.coverage" NAME="GAOZI Coverage Results" MODIFIED="1729045662377" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/pystorage$getcoin.coverage" NAME="getcoin Coverage Results" MODIFIED="1689322714372" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/SmallTools" />
    <SUITE FILE_PATH="coverage/pystorage$.coverage" NAME="的 Coverage Results" MODIFIED="1715744849584" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/BOOM/boom2" />
    <SUITE FILE_PATH="coverage/pystorage$caogao7.coverage" NAME="caogao7 Coverage Results" MODIFIED="1701337710595" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/SmallTools" />
    <SUITE FILE_PATH="coverage/pystorage$fund.coverage" NAME="fund Coverage Results" MODIFIED="1752461372437" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/SmallTools" />
    <SUITE FILE_PATH="coverage/pystorage$joingame.coverage" NAME="joingame Coverage Results" MODIFIED="1751947047572" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/joingame" />
    <SUITE FILE_PATH="coverage/pystorage$base64.coverage" NAME="base64 Coverage Results" MODIFIED="1675851235993" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="E:/python3913/Lib" />
    <SUITE FILE_PATH="coverage/pystorage$caogao3333.coverage" NAME="caogao3333 Coverage Results" MODIFIED="1736908501290" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/langren_admin2" />
    <SUITE FILE_PATH="coverage/pystorage$adtest.coverage" NAME="adtest Coverage Results" MODIFIED="1714385912016" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/BOOM/boom" />
    <SUITE FILE_PATH="coverage/pystorage$admin_api_202408281816.coverage" NAME="admin_api_202408281816 Coverage Results" MODIFIED="1724840239490" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/langren_admin" />
    <SUITE FILE_PATH="coverage/pystorage$admin_api_create__1_.coverage" NAME="admin_api_create (1) Coverage Results" MODIFIED="1724916927076" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/langren_admin" />
    <SUITE FILE_PATH="coverage/pystorage$caogao88888.coverage" NAME="caogao88888 Coverage Results" MODIFIED="1732786711463" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/SmallTools" />
    <SUITE FILE_PATH="coverage/pystorage$test.coverage" NAME="test Coverage Results" MODIFIED="1701936550638" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/AK" />
    <SUITE FILE_PATH="coverage/pystorage$CalTaxByYear__1_.coverage" NAME="CalTaxByYear (1) Coverage Results" MODIFIED="1674892754625" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/SmallTools" />
    <SUITE FILE_PATH="coverage/pystorage$GetAppsign.coverage" NAME="GetAppsign Coverage Results" MODIFIED="1675408625026" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Voyage" />
    <SUITE FILE_PATH="coverage/pystorage$qa1_admin_api_202410171722.coverage" NAME="qa1_admin_api_202410171722 Coverage Results" MODIFIED="1729159230742" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/langren_admin2" />
    <SUITE FILE_PATH="coverage/pystorage$admin.coverage" NAME="admin Coverage Results" MODIFIED="1724835131795" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/langrenadmin" />
    <SUITE FILE_PATH="coverage/pystorage$qa3_admin_api_202408291148.coverage" NAME="qa3_admin_api_202408291148 Coverage Results" MODIFIED="1724903354663" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/langren_admin" />
    <SUITE FILE_PATH="coverage/pystorage$comparexlsx.coverage" NAME="comparexlsx Coverage Results" MODIFIED="1747712612341" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/langren_admin71" />
    <SUITE FILE_PATH="coverage/pystorage$cccccccccccccccccccccccccccccccccccccccccccccccc.coverage" NAME="cccccccccccccccccccccccccccccccccccccccccccccccc Coverage Results" MODIFIED="1736758274105" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/langren_admin71" />
    <SUITE FILE_PATH="coverage/pystorage$join.coverage" NAME="join Coverage Results" MODIFIED="1757326728090" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/pystorage$opopopp.coverage" NAME="opopopp Coverage Results" MODIFIED="1692154532194" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/SmallTools" />
    <SUITE FILE_PATH="coverage/pystorage$IMserver.coverage" NAME="IMserver Coverage Results" MODIFIED="1717754297752" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Voyage" />
    <SUITE FILE_PATH="coverage/pystorage$google2.coverage" NAME="google2 Coverage Results" MODIFIED="1725531403378" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/SmallTools" />
    <SUITE FILE_PATH="coverage/pystorage$google_refund__1_.coverage" NAME="google_refund (1) Coverage Results" MODIFIED="1725615262573" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Voyage" />
    <SUITE FILE_PATH="coverage/pystorage$caogao5.coverage" NAME="caogao5 Coverage Results" MODIFIED="1708423505187" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/SmallTools" />
    <SUITE FILE_PATH="coverage/pystorage$test_server.coverage" NAME="test_server Coverage Results" MODIFIED="1715842864150" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/BOOM/boom2/test" />
    <SUITE FILE_PATH="coverage/pystorage$caogao88.coverage" NAME="caogao88 Coverage Results" MODIFIED="1716970454670" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/SmallTools" />
    <SUITE FILE_PATH="coverage/pystorage$admin_api_create.coverage" NAME="admin_api_create Coverage Results" MODIFIED="1732013180247" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/langren_admin2" />
    <SUITE FILE_PATH="coverage/pystorage$caogao4.coverage" NAME="caogao4 Coverage Results" MODIFIED="1708422846604" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/SmallTools" />
    <SUITE FILE_PATH="coverage/pystorage$minigamee__1_.coverage" NAME="minigamee (1) Coverage Results" MODIFIED="1723026514972" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/SmallTools" />
    <SUITE FILE_PATH="coverage/pystorage$tttttttttttttttttttttttttt.coverage" NAME="tttttttttttttttttttttttttt Coverage Results" MODIFIED="1693914236498" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/SmallTools" />
    <SUITE FILE_PATH="coverage/pystorage$templiveV3.coverage" NAME="templiveV3 Coverage Results" MODIFIED="1715855945382" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/SmallTools" />
    <SUITE FILE_PATH="coverage/pystorage$CalTax__1_.coverage" NAME="CalTax (1) Coverage Results" MODIFIED="1675134556701" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/SmallTools" />
    <SUITE FILE_PATH="coverage/pystorage$networktest.coverage" NAME="networktest Coverage Results" MODIFIED="1715572687986" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/SmallTools" />
    <SUITE FILE_PATH="coverage/pystorage$serversign.coverage" NAME="serversign Coverage Results" MODIFIED="1713261178397" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/BOOM/boom" />
    <SUITE FILE_PATH="coverage/pystorage$caogao98.coverage" NAME="caogao98 Coverage Results" MODIFIED="1725531525825" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/SmallTools" />
    <SUITE FILE_PATH="coverage/pystorage$stream.coverage" NAME="stream Coverage Results" MODIFIED="1689327230926" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/SmallTools" />
    <SUITE FILE_PATH="coverage/pystorage$BoomClientFunc.coverage" NAME="BoomClientFunc Coverage Results" MODIFIED="1717580195203" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/BOOM/boom" />
    <SUITE FILE_PATH="coverage/pystorage$enterroom.coverage" NAME="enterroom Coverage Results" MODIFIED="1734664183749" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/langrenjinfang" />
    <SUITE FILE_PATH="coverage/pystorage$caogao3.coverage" NAME="caogao3 Coverage Results" MODIFIED="1687330292607" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/SmallTools" />
    <SUITE FILE_PATH="coverage/pystorage$caogao9.coverage" NAME="caogao9 Coverage Results" MODIFIED="1690175497738" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/SmallTools" />
    <SUITE FILE_PATH="coverage/pystorage$caogao6.coverage" NAME="caogao6 Coverage Results" MODIFIED="1689994126045" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/SmallTools" />
    <SUITE FILE_PATH="coverage/pystorage$Appsign__1_.coverage" NAME="Appsign (1) Coverage Results" MODIFIED="1675923436979" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Voyage" />
    <SUITE FILE_PATH="coverage/pystorage$testlog.coverage" NAME="testlog Coverage Results" MODIFIED="1727581144323" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/log" />
    <SUITE FILE_PATH="coverage/pystorage$CalTaxByMonth.coverage" NAME="CalTaxByMonth Coverage Results" MODIFIED="1674877977759" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/SmallTools" />
    <SUITE FILE_PATH="coverage/pystorage$admin_api_202408291142.coverage" NAME="admin_api_202408291142 Coverage Results" MODIFIED="1724903382266" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/langren_admin" />
    <SUITE FILE_PATH="coverage/pystorage$dec.coverage" NAME="dec Coverage Results" MODIFIED="1701936997411" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/24goal" />
    <SUITE FILE_PATH="coverage/pystorage$iuuuuuu.coverage" NAME="iuuuuuu Coverage Results" MODIFIED="1723026130018" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/SmallTools" />
    <SUITE FILE_PATH="coverage/pystorage$lrslive.coverage" NAME="lrslive Coverage Results" MODIFIED="1706686213648" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/../" />
    <SUITE FILE_PATH="coverage/pystorage$google_refund.coverage" NAME="google_refund Coverage Results" MODIFIED="1728896462231" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Voyage" />
    <SUITE FILE_PATH="coverage/pystorage$downloaderweima.coverage" NAME="downloaderweima Coverage Results" MODIFIED="1740563178226" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/SmallTools" />
    <SUITE FILE_PATH="coverage/pystorage$3333333333333333333333333333333333333.coverage" NAME="3333333333333333333333333333333333333 Coverage Results" MODIFIED="1723023996788" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/SmallTools" />
    <SUITE FILE_PATH="coverage/pystorage$caltax.coverage" NAME="caltax Coverage Results" MODIFIED="1674874013385" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/SmallTools" />
    <SUITE FILE_PATH="coverage/pystorage$caogao777.coverage" NAME="caogao777 Coverage Results" MODIFIED="1712815685165" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/SmallTools" />
    <SUITE FILE_PATH="coverage/pystorage$caogoa.coverage" NAME="caogoa Coverage Results" MODIFIED="1719308650052" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/BOOM/boom2" />
    <SUITE FILE_PATH="coverage/pystorage$tttttt.coverage" NAME="tttttt Coverage Results" MODIFIED="1737092700152" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/langren_admin71" />
    <SUITE FILE_PATH="coverage/pystorage$camille.coverage" NAME="camille Coverage Results" MODIFIED="1682505222941" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/../download/camille-master" />
    <SUITE FILE_PATH="coverage/pystorage$Unittests_in_1_py.coverage" NAME="Unittests in 1.py Coverage Results" MODIFIED="1715681479477" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/BOOM/boom" />
    <SUITE FILE_PATH="coverage/pystorage$getheaders__1_.coverage" NAME="getheaders (1) Coverage Results" MODIFIED="1715842855186" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/BOOM/boom2" />
    <SUITE FILE_PATH="coverage/pystorage$ClientFunc.coverage" NAME="ClientFunc Coverage Results" MODIFIED="1757404916431" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Voyage" />
    <SUITE FILE_PATH="coverage/pystorage$templive__1_.coverage" NAME="templive (1) Coverage Results" MODIFIED="1695721240269" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/SmallTools" />
    <SUITE FILE_PATH="coverage/pystorage$admin__1_.coverage" NAME="admin (1) Coverage Results" MODIFIED="1724839037068" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/langren_admin" />
    <SUITE FILE_PATH="coverage/pystorage$chepai.coverage" NAME="chepai Coverage Results" MODIFIED="1721878455617" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/SmallTools" />
    <SUITE FILE_PATH="coverage/pystorage$admin_api.coverage" NAME="admin_api Coverage Results" MODIFIED="1732703951418" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/langren_admin2" />
    <SUITE FILE_PATH="coverage/pystorage$cccccccccc.coverage" NAME="cccccccccc Coverage Results" MODIFIED="1732782814751" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/langren_admin71" />
    <SUITE FILE_PATH="coverage/pystorage$edgemission.coverage" NAME="edgemission Coverage Results" MODIFIED="1757325967857" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/SmallTools" />
  </component>
</project>