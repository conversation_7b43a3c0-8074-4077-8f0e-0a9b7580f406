<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="dataSourceStorageLocal">
    <data-source name="ClickHouse - @cc-bp1j6i01q18k59m5q.clickhouse.ads.aliyuncs.com" uuid="65f7d6e2-515f-4582-86a5-94860b6f65e2">
      <database-info product="ClickHouse" version="22.8.5.29" jdbc-version="0.1" driver-name="ru.yandex.clickhouse-jdbc" driver-version="0.1.50" dbms="CLICKHOUSE" exact-version="22.8.5.29" exact-driver-version="0.1">
        <identifier-quote-string>`</identifier-quote-string>
      </database-info>
      <case-sensitivity plain-identifiers="exact" quoted-identifiers="exact" />
      <secret-storage>master_key</secret-storage>
      <user-name>langren</user-name>
      <schema-mapping>
        <introspection-scope>
          <node kind="schema" qname="@" />
        </introspection-scope>
      </schema-mapping>
    </data-source>
    <data-source name="boom" uuid="fa322cdd-1a7c-446b-8424-29ed67c28f7a">
      <database-info product="MySQL" version="5.7.28-log" jdbc-version="4.2" driver-name="MySQL Connector/J" driver-version="mysql-connector-java-8.0.21 (Revision: 33f65445a1bcc544eb0120491926484da168f199)" dbms="MYSQL" exact-version="5.7.28" exact-driver-version="8.0">
        <extra-name-characters>#@</extra-name-characters>
        <identifier-quote-string>`</identifier-quote-string>
      </database-info>
      <case-sensitivity plain-identifiers="lower" quoted-identifiers="lower" />
      <secret-storage>master_key</secret-storage>
      <user-name>boom</user-name>
      <schema-mapping>
        <introspection-scope>
          <node kind="schema">
            <name qname="@" />
            <name qname="boom_data" />
          </node>
        </introspection-scope>
      </schema-mapping>
    </data-source>
    <data-source name="console.db" uuid="b78e810c-8a45-4450-a577-293eb5618232">
      <database-info product="SQLite" version="3.31.1" jdbc-version="2.1" driver-name="SQLite JDBC" driver-version="3.31.1" dbms="SQLITE" exact-version="3.31.1" exact-driver-version="3.31">
        <identifier-quote-string>&quot;</identifier-quote-string>
      </database-info>
      <case-sensitivity plain-identifiers="mixed" quoted-identifiers="mixed" />
      <secret-storage>master_key</secret-storage>
      <auth-required>false</auth-required>
      <schema-mapping>
        <introspection-scope>
          <node kind="schema" qname="@" />
        </introspection-scope>
      </schema-mapping>
    </data-source>
    <data-source name="lr_statistic_hologres" uuid="d8f39399-2f6b-448a-95d3-de7f6ef99810">
      <database-info product="PostgreSQL" version="11.3" jdbc-version="4.2" driver-name="PostgreSQL JDBC Driver" driver-version="42.2.5" dbms="POSTGRES" exact-version="11.3" exact-driver-version="42.2">
        <identifier-quote-string>&quot;</identifier-quote-string>
      </database-info>
      <case-sensitivity plain-identifiers="lower" quoted-identifiers="exact" />
      <secret-storage>master_key</secret-storage>
      <user-name>LTAII9wl2YMObIg2</user-name>
      <schema-mapping>
        <introspection-scope>
          <node kind="database" qname="lr_statistic_hologres">
            <node kind="schema" negative="1" />
          </node>
        </introspection-scope>
      </schema-mapping>
    </data-source>
    <data-source name="qa1 holostatic" uuid="99f715a3-bffb-4287-a3e0-5f29afccbdcd">
      <database-info product="PostgreSQL" version="11.3" jdbc-version="4.2" driver-name="PostgreSQL JDBC Driver" driver-version="42.2.5" dbms="POSTGRES" exact-version="11.3" exact-driver-version="42.2">
        <identifier-quote-string>&quot;</identifier-quote-string>
      </database-info>
      <case-sensitivity plain-identifiers="lower" quoted-identifiers="exact" />
      <secret-storage>master_key</secret-storage>
      <user-name>LTAII9wl2YMObIg2</user-name>
      <schema-mapping>
        <introspection-scope>
          <node negative="1">
            <node kind="database" qname="@">
              <node kind="schema" qname="@" />
            </node>
            <node kind="database" qname="lr_statistic_hologres">
              <node kind="schema" qname="public" />
            </node>
          </node>
        </introspection-scope>
      </schema-mapping>
    </data-source>
  </component>
</project>