import requests, json
from Appsign import getsign
import urllib3

baseurl1 = 'https://qa.boom.caniculab.com/boom/'
# baseurl2 = 'http://k8s-voyageqa-ingressv-0e43378f0b-1537062284.ap-southeast-1.elb.amazonaws.com/voyage/'
baseurl2='https://qa-voyage.digitalconch.com/voyage/'
# baseurl='http://k8s-imqa-ingressi-5c3f8dc70f-2010225555.ap-southeast-1.elb.amazonaws.com/im/'

proj = 'voyage'
# appid = 'prod856774'
# proj='boom'
appid='yg28295931'
baseurl = baseurl1 if proj == 'boom' else baseurl2
headers = getsign(False, proj, appid)


# 订单查询
def query():
    data = {'cpOrderNo': 'JM0840001685605681673'}
    url = 'https://apisdb.boom.caniculab.com/boom/sdk/recharge/order/query'
    response = requests.get(url, params=data, headers=headers)
    print(response.url)
    print(response.text)


# query()

# 查ip
def ipquery(ip):
    url = baseurl + 'sdk/ip/query'
    data = {'ip': ip}
    response = requests.get(url, params=data, headers=headers)
    print(url, ip)
    print(response.text)
    return response.text


# ipquery('240e:0688:0400:0560:116:228:135:217') #上海
# ipquery('2404:c800:c206:a1:bf22:ae98:9:8') #香港
# ipquery('2001:f90:0:a000::234')#澳门
# ipquery('2001:b030:7053:ff03::3')#台湾
def checkimg(img, it):
    headers['Content-Type'] = 'application/json'
    body = {
        "imageType": it,  # 1普通图片 2用户资料图片
        "account": "123",
        "imageUrl": img,
        "ip": "*******"
    }
    url = baseurl + 'sdk/check/image'
    response = requests.post(url, data=json.dumps(body), headers=headers)
    print(response.text)


# checkimg('https://p2.qhimg.com/t01769309965ced345b.jpg',1)
# checkimg('https://cdn.hk01.com/di/media/images/710855/org/928b6f21df6f561125188f8bd04e6040.jpg/-xdMhTOTG9X9n3gG2q3wIiGHNPjL-2b4Ojs_GDo7Pxg?v=w1920',2)

def checktext(text, tt):
    headers['Content-Type'] = 'application/json'
    body = {
        "textType": tt,  # 类型 1普通文本, 2用户资料文本
        "account": "123",
        "content": text,
        "ip": "*******"
    }
    url = baseurl + 'sdk/check/text'
    response = requests.post(url, data=json.dumps(body), headers=headers)
    print(url)
    print(response.text)


# checktext('拳打镇关西',1)
# checktext('习近平天下第一',2)
def placeorder():
    headers['Content-Type'] = 'application/json'
    url = baseurl + 'sdk/recharge/order/place'
    data = {
        "cpOrderNo": "orderNo_1713349932292.93",
        "openId": "yg28295931-qa-443",
        "productCount": 1,
        "productId": "onedoller",
    }
    data1 = {
        "amount": 1,
        "body": "冲冲冲",
        "cpOrderNo": "orderNo_1713350516377.724",
        "openId": "bestwish10-sdb-17299",
        "productCount": 1,
        "productId": "eggkn",
        "subject": "蛋刀"
    }
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
    response = requests.post(url, headers=headers, data=json.dumps(data), verify=False)
    # response = requests.post(url, headers=headers, data=json.dumps(data))
    print(response.text)


# placeorder()

def anticheat(bid):
    headers['Content-Type'] = 'application/json'
    url = baseurl + 'sdk/check/antiCheating'
    data = {
        # "assetVersion": "null",
        "businessId": bid,
        # "extData": "string",
        # "gameVersion": "2.0",
        "ip": "*************",
        "roleAccount": "yg28295931-qa-499",
        "roleId": "497",
        # "roleName": "1",
        # "serverId": "1",
        "token": "npBBsfmRPicAE0cAQQmFDETJdw8eykcDQVpTRQ=="
    }
    response = requests.post(url, data=json.dumps(data), headers=headers)
    print(response.text)


# anticheat('afe6eefce8f7a16b443d7e76fc75159c')#ios
# anticheat('9ded8972af20ec47d00c60d9b63813fe')#
def getloginstyle(openid):
    url=baseurl+'sdk/security/login/bound/list'
    data={'openId':openid}
    response=requests.get(url,params=data,headers=headers)
    print(headers)
    print(response.text)

getloginstyle('yg28295931-qa-13045')
# getloginstyle('yg28295931-qa-13066')
# getloginstyle('yg28295931-qa-11090')
