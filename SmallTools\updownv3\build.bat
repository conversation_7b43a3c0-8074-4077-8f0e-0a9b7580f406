@echo off
chcp 65001
echo 正在安装必要的依赖...
pip install nuitka
pip install flask
pip install wheel
pip install setuptools
pip install qrcode[pil]

echo 开始打包...
python -m nuitka ^
    --follow-imports ^
    --enable-plugin=tk-inter ^
    --include-package=flask ^
    --include-package=werkzeug ^
    --include-package=jinja2 ^
    --include-package=qrcode ^
    --include-package=PIL ^
    --include-data-dir=templates=templates ^
    --output-dir=build ^
    --onefile ^
    --output-filename=updown.exe ^
    --standalone ^
    --assume-yes-for-downloads ^
    --show-progress ^
    --remove-output ^
    app.py

echo 打包完成！exe文件位置：build\updown.exe
pause