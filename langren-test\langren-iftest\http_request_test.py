"""
<AUTHOR>
@date 2020/7/29 0029
"""
import random
import unittest
import warnings
from typing import Union, Dict, Any

import requests
import time
from BeautifulReport import BeautifulReport
from requests import Response

import langren_apollo as apollo
from domain import ExportCase, Profile, Request, RequestBody
from util.log import get_logger
from util.method_tool import parametrized_test_with

logger = get_logger('HttpRequestTest')


def run_cases(report_filename: str, case_file_path: str, profile_name: str, group_name: str = None,
              req_uuid: str = None):
    case: ExportCase = ExportCase.read_from_json_file(case_file_path)
    profile: Profile = next(filter(lambda x: x.name == profile_name, case.profiles))

    ctx: dict = {}
    global_scope: dict = {}
    exec(profile.script, {}, global_scope)
    if 'init' in global_scope:
        exec('init(ctx)', {'ctx': ctx}, global_scope)

    # 配置APOLLO
    apollo_config_params = ['APOLLO_ENV', 'APOLLO_CLUSTER', 'APOLLO_PORTAL_URL', 'APOLLO_PORTAL_TOKEN']
    for ap in apollo_config_params:
        k = '$' + ap
        if k in ctx:
            setattr(apollo, ap, ctx[k])

    class Fn(object):

        def __init__(self):
            self.apollo_call = apollo.ApolloCall()

        def update_apollo_config(self, app_id: str, namespace: str, key: str, value: str):
            print(f'update_apollo_config [{app_id}][{namespace}] {key}->{value}')
            self.apollo_call.edit_config(app_id, namespace, key, value)
            self.apollo_call.release_namespace(app_id, namespace)

        @staticmethod
        def __random_chi() -> str:
            head = random.randint(0xb0, 0xf7)
            body = random.randint(0xa1, 0xf9)
            val = f'{head:x}{body:x}'
            return bytes.fromhex(val).decode('gb2312')

        @staticmethod
        def __random_letter() -> str:
            alphabet = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ'
            return alphabet[random.randrange(0, len(alphabet))]

        @staticmethod
        def __random_number() -> str:
            return str(random.randint(0, 9))

        @staticmethod
        def random_str(prefix: str = "", length: int = 5) -> str:
            random_method = [Fn.__random_chi, Fn.__random_letter, Fn.__random_number]
            random_chars = [random_method[random.randrange(0, len(random_method))]() for i in range(0, length)]
            return prefix + ''.join(random_chars)

        @staticmethod
        def time_now() -> str:
            ct = time.time()
            local_time = time.localtime(ct)
            data_head = time.strftime("%Y%m%d%H%M%S", local_time)
            data_secs = (ct - int(ct)) * 1000
            return "%s%03d" % (data_head, data_secs)

    fn = Fn()

    def replace_token(s: str, d: dict) -> str:
        while True:
            start = s.find('${')
            if start == -1:
                break
            end = s.find('}', start)
            if end == -1:
                break
            key = s[start + 2:end]
            if key not in d:
                raise AssertionError(key + ' is not exists')
            s = s[0:start] + d[key] + s[end + 1:]

        return s

    def replace_path_variable(s: str, d: dict) -> str:
        while True:
            start = s.find('{')
            if start == -1:
                break
            end = s.find('}', start)
            if end == -1:
                break
            key = s[start + 1:end]
            if key not in d:
                raise AssertionError(key + ' is not exists')
            s = s[0:start] + d[key] + s[end + 1:]
            del d[key]
        return s

    logger.info("%s v%s start..." % (case.name, str(case.version)))
    test_params = []
    for grp in case.groups:
        if group_name is not None and grp.name != group_name:
            continue
        test_params.extend(
            [(grp.name, req) for req in grp.requests if (req_uuid is None and req.enabled) or req.uuid == req_uuid])

    @parametrized_test_with({'http_req': test_params})
    class HttpRequestTestCase(unittest.TestCase):

        @classmethod
        def setUpClass(cls) -> None:
            warnings.simplefilter("ignore", ResourceWarning)
            cls.__sessions: Dict[str, requests.Session] = {}

        @classmethod
        def tearDownClass(cls) -> None:
            for session in cls.__sessions.values():
                session.close()

        def parametrized_test_http_req(self, group: str, req: Request):
            """HTTP接口调用"""
            if group in self.__sessions:
                session = self.__sessions[group]
            else:
                session = requests.Session()
                self.__sessions[group] = session

            req_scope: dict = {}
            exec(req.script, {'fn': fn}, req_scope)
            if 'init' in req_scope:
                exec('init(ctx,test)', {'ctx': ctx, 'test': self}, req_scope)

            before_params = {'ctx': ctx, 'test': self, 'req': req}
            if 'before' in global_scope:
                exec('before(ctx,test,req)', before_params, global_scope)
            if 'before' in req_scope:
                exec('before(ctx,test,req)', before_params, req_scope)

            url: str = replace_token(req.url, ctx)

            def replace_token_dict(d: Dict[str, Any]) -> Dict:
                return {dk: replace_token(d[dk], ctx) for dk in d if isinstance(d[dk], str)}

            headers: dict = replace_token_dict(req.headers)
            params: dict = replace_token_dict(req.params)

            # 替换路径变量
            url = replace_path_variable(url, params)
            form_data: dict = {}
            json_data: Union[str, None] = None
            body: RequestBody = req.body

            files = {}
            if body.type == 'formdata':
                form_data = replace_token_dict(body.formdata)

                for fk in form_data:
                    v = form_data[fk]
                    if isinstance(v, bytes):
                        del form_data[fk]
                        files[fk] = v
            elif body.type == 'raw':
                json_data = body.raw
                # todo bin

            print('[%s] url: %s, headers: %s, params: %s, formdata: %s' % (req.name, url,
                                                                           str(headers), str(params), str(form_data)))
            start_time = time.time()
            resp: Response = session.request(req.method, url, params=params, data=form_data, json=json_data,
                                             headers=headers, files=files)
            elapsed = time.time() - start_time
            print('[%s] costs: %sms, resp: %s' % (req.name, str(round(elapsed * 1000, 2)), str(resp.json())))
            after_params = {'ctx': ctx, 'test': self, 'req': req, 'resp': resp}
            if 'after' in global_scope:
                exec('after(ctx,test,req,resp)', after_params, global_scope)
            if 'after' in req_scope:
                exec('after(ctx,test,req,resp)', after_params, req_scope)

    suite = unittest.defaultTestLoader.loadTestsFromTestCase(HttpRequestTestCase)
    run = BeautifulReport(suite)
    run.report('接口测试报告', report_filename, r'./report')


if __name__ == '__main__':
    run_cases(time.strftime('%Y%m%d_%H%M%S'), r'./resource/httpcase/狼人杀.json', 'qa3')
