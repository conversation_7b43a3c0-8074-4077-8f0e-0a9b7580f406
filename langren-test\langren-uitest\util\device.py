"""
<AUTHOR>
@date 2020/6/30 0030
"""
from multiprocessing import Process, Pipe
from multiprocessing.connection import Connection
from typing import List, Dict

from airtest.core.api import auto_setup, init_device
from airtest.core.device import Device

from role.role import Device<PERSON>ana<PERSON>, RoleManager
from role.user_role import User
from util.event import DeviceReadyEvent, TerminateEvent
from util.log import get_logger
from util.rpc import RpcClient, RpcServer
from util.thread_tool import CountDownLatch, ThreadTool

logger = get_logger('Device')


class DeviceContext(object):

    def __init__(self, rpc_clients: Dict[str, RpcClient], processes: List[Process]):
        self.rpc_clients = rpc_clients
        self.__processes = processes

    def dispose(self):
        for idx in self.rpc_clients:
            rpc_client = self.rpc_clients[idx]
            rpc_client.dispatch(TerminateEvent(0))
            rpc_client.dispose()
        for p in self.__processes:
            logger.info('close pid %s' % p.pid)
            p.join()
            p.close()
        logger.info('disposed')

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.dispose()


def setup_device(uri) -> Device:
    import airtest.utils.logger as alg
    alg.get_logger('airtest').setLevel('WARN')

    auto_setup(__file__, logdir=r"./log", devices=[],
               project_root=r"./")
    device = init_device(uuid=uri)
    # device.install_app(filepath=r'./resource/GameBox_v2.9.5_debugQA_pocketLangren_develop.apk', replace=True)
    return device


def run_device(uri: str, con: Connection):
    device = setup_device(uri)
    server = RpcServer(con)
    server.handlers[DeviceManager] = DeviceManager(device)
    role_manager = RoleManager(server)
    server.handlers[RoleManager] = role_manager
    role_manager.assign_role(User)
    server.dispatch(DeviceReadyEvent())
    server.wait(TerminateEvent)
    role_manager.dispose()
    server.dispose()


def start_devices(devices: List[str]) -> DeviceContext:
    processes: List[Process] = []
    rpc_clients: Dict[str, RpcClient] = {}
    logger.info('devices: %s' % devices)

    for device in devices:
        p, c = Pipe()
        processes.append(Process(target=run_device, name=device, args=[device, c]))
        rpc_clients[device] = RpcClient(p)

    latch = CountDownLatch(len(rpc_clients))

    def wait_device_ready(did):
        rpc_clients[did].wait(DeviceReadyEvent)
        latch.count_down()

    with ThreadTool(len(devices)) as tool:
        for idx in rpc_clients:
            tool.run_async(wait_device_ready, (idx,))
        for p in processes:
            p.start()
        latch.wait()
        logger.info('all device ready')
    return DeviceContext(rpc_clients, processes)
