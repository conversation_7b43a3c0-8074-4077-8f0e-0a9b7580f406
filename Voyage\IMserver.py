import requests, multiprocessing
from Appsign import getsign
import json, os, socket, threading

baseurl = 'http://k8s-imqa-ingressi-5c3f8dc70f-2010225555.ap-southeast-1.elb.amazonaws.com/im/'
headers = getsign(False, 'im', 'yg28295931')


def gettoken(cpuserid):
    url = baseurl + 'sdk/security/token'
    data = {'cpUserId': cpuserid,
            'validTime': 7 * 60 * 60 * 24}
    response = requests.get(url, params=data, headers=headers)
    # print(gettoken.__name__)
    # print(response.text)
    return response.text


# gettoken('june001')


# 创建群组
def createg(cpuserid, groupid):
    data = {'cpUserId': cpuserid,
            'groupId': groupid}
    url = baseurl + 'sdk/group/create'
    gettoken(cpuserid)
    response = requests.post(url, data=data, headers=headers)
    print(createg.__name__)
    print(response.text)


# createg('june001', 'rain')


# 解散群组
def dissloveg(cpuserid, groupid):
    data = {'cpUserId': cpuserid,
            'groupId': groupid}
    url = baseurl + 'sdk/group/dissolve'
    gettoken(cpuserid)
    response = requests.post(url, data=data, headers=headers)
    print(dissloveg.__name__)
    print(response.text)


# dissloveg('thunder001', 'thunder-as')


# 加入群组
def joing(cpuserid, groupid):
    data = {'cpUserId': cpuserid,
            'groupId': groupid,
            'messageCount': 10}
    url = baseurl + 'sdk/group/join'
    gettoken(cpuserid)
    response = requests.post(url, data=data, headers=headers)
    print(joing.__name__)
    print(response.text)


# joing('june001', 'rain')

# 退出群组
def quitg(cpuserid, groupid):
    data = {'cpUserId': cpuserid,
            'groupId': groupid,
            }
    url = baseurl + 'sdk/group/quit'
    gettoken(cpuserid)
    response = requests.post(url, data=data, headers=headers)
    print(quitg.__name__)
    print(response.text)


# quitg('june001', 'rain')

# quitg('sky002','skyking01')
# 群组禁言
def muteg(cpuserid, groupid, banids):
    headers['Content-Type'] = 'application/json'
    data = {"cpUserId": cpuserid,
            "groupId": groupid,
            "muteTime": 60,
            "targetIds": banids}

    url = baseurl + 'sdk/group/mute'
    response = requests.post(url, data=json.dumps(data), headers=headers)
    print(muteg.__name__)
    print(response.text)
    # print(response.request.body)
    # print(response.request.headers)
    # print(headers)


# muteg('sky001', 'skyking01', ['sky002', ])


# 群组解禁
def ummuteg(cpuserid, groupid, unbanids):
    headers['Content-Type'] = 'application/json'
    data = {'groupUnBanParam': {'cpUserId': cpuserid,
                                'groupId': groupid,
                                'targetIds': unbanids}
            }
    url = baseurl + 'sdk/group/unmute'
    response = requests.post(url, data=json.dumps(data), headers=headers)
    print(muteg.__name__)
    print(response.text)


# 成员在线状态
def getmen(cpuserids, groupid):
    data = {"cpUserIds": cpuserids,
            'groupId': groupid}
    url = baseurl + 'sdk/group/member/online'
    response = requests.get(url, data, headers=headers)
    print(getmen.__name__)
    print(response.text)


# getmen('pgt00001', 'small001')

# 成员在线状态
def postmen(cpuserids, groupid):
    data = {"cpUserIds": cpuserids,
            "groupId": groupid}
    url = baseurl + 'sdk/group/member/online'
    response = requests.post(url=url, json=data, headers=headers)
    print(postmen.__name__)
    print(response.text)


# postmen(['pgt00001'], 'small001')


# 创建聊天室
def createchatroom(cpuserid, chatroom):
    data = {'cpUserId': cpuserid,
            'chatRoomId': chatroom}
    url = baseurl + 'sdk/chatroom/create'
    gettoken(cpuserid)
    response = requests.post(url, data=data, headers=headers)
    print(createchatroom.__name__)
    print(response.text)


# createchatroom('tongtianjiaozhu', 'jiejiao')


# 解散聊天室
def disslovechatroom(cpuserid, chatroom):
    data = {'cpUserId': cpuserid,
            'chatRoomId': chatroom}
    url = baseurl + 'sdk/chatroom/dissolve'
    gettoken(cpuserid)
    response = requests.post(url, data=data, headers=headers)
    print(disslovechatroom.__name__)
    print(response.text)


# disslovechatroom('chat001','chatroom001')

# 加入聊天室
def joinchatroom(cpuserid, chatroom):
    data = {'cpUserId': cpuserid,
            'chatRoomId': chatroom,
            'messageCount': 10}
    url = baseurl + 'sdk/chatroom/join'
    gettoken(cpuserid)
    response = requests.post(url, data=data, headers=headers)
    print(joinchatroom.__name__)
    print(response.text)


# joinchatroom('guangchengzi','jiejiao')

# 退出聊天室
def quitchatroom(cpuserid, chatroom):
    data = {'cpUserId': cpuserid,
            'chatRoomId': chatroom,
            }
    url = baseurl + 'sdk/chatroom/quit'
    gettoken(cpuserid)
    response = requests.post(url, data=data, headers=headers)
    print(quitchatroom.__name__)
    print(response.text)


# quitchatroom('chat002','chatroom001')
# 禁言聊天室
def mutechatroom(cpuserid, chatroom, banids):
    data = {"cpUserId": cpuserid,
            "chatRoomId": chatroom,
            "muteTime": 60,
            "targetIds": banids}

    url = baseurl + 'sdk/chatroom/mute'
    response = requests.post(url, json=data, headers=headers)
    print(mutechatroom.__name__)
    print(response.text)
    # print(response.request.body)
    # print(response.request.headers)
    # print(headers)


mutechatroom('tongtianjiaozhu', 'jiejiao', ['guangchengzi', ])


# 聊天室解禁
def ummutechatroom(cpuserid, chatroom, unbanids):
    headers['Content-Type'] = 'application/json'
    data = {'cpUserId': cpuserid,
            'chatRoomId': chatroom,
            'targetIds': unbanids}

    url = baseurl + 'sdk/chatroom/unmute'
    response = requests.post(url, data=json.dumps(data), headers=headers)
    print(mutechatroom.__name__)
    print(response.text)
