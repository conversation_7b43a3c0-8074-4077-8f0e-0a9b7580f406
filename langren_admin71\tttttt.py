import pandas as pd
import requests,json
import time
from openpyxl import load_workbook

def run_tests():
    headers = {'cookie': 'SESSION=59c8d4eb-5d6c-4997-a4cf-14f1c6bc3d60',
               'charset':'utf-8'}
    # 读取Excel表格数据
    df = pd.read_excel('test_data.xlsx')
    success_count = 0
    fail_count = 0
    start_row = 145# 设置从第几行开始读取（这里的行号按Excel里常规的从1开始算）
    num_rows_to_read =1  # 设置要读取的行数
    start_row_index = start_row - 2  # 将开始行号转换为数据框索引（从0开始）
    row_count = 0  # 新增计数器，用来记录当前处理到数据框里的第几行（索引从0开始）

    # 加载原始Excel文件
    workbook = load_workbook('test_data.xlsx')
    sheet = workbook.active

    for index, row in df.iterrows():
        # 判断是否达到开始行
        if index < start_row_index:
            continue
        # 判断是否已经读取了指定的行数，如果是则结束循环
        if row_count >= num_rows_to_read:
            break
        row_count += 1

        url = row['接口URL']
        method = row['接口方法']
        params_str = row['接口参数']
        expected_result_str = row['预期结果']
        iftest=row['是否测试']
        if iftest=='no':continue

        if pd.isnull(expected_result_str):
            expected_result_str = ''

        if pd.isnull(params_str):
            params = {}
        else:
            params = eval(params_str)

        # 根据接口方法处理请求参数
        if method == 'GET':
            url = url.format(**params)
            print(url)
            response = requests.get(url, params=params, headers=headers)
            print(response.json())
        elif method == "POST":
            if params_str:
                params = eval(params_str)
            else:
                params = {}
            print(params)
            url=url.format(**params)
            print(url)
            response = requests.post(url, json=params, headers=headers)
            print(response.text)
            # print('json***',response.text)
            if response.status_code == 400 or response.json().get("err_code") == '11000'or response.json().get('err_msg')=='系统错误':
                print(params)
                c=params
                print(f'上面的******************{index+2}')
                # sheet.cell(row=index+2,column=7).value='application/x-www-form-urlencoded'
                if c==params : print('yiy')
                response = requests.request('POST',url, data=params, headers=headers)
                print(response.text)

        elif method=="PUT":
            url = url.format(**params)
            print(url)
            response = requests.put(url, params=params, headers=headers)
            print(response.text)
        else:
            raise ValueError(f"不支持的接口方法: {method}")

        if response.status_code == 200 and expected_result_str in response.text:
            success_count += 1
            test_result = {
                '接口概述': row['接口概述'],
                '接口url': url,
                '接口参数': params,
                '接口返回结果': response.text,
                '接口成功与否': True
            }
        else:
            fail_count += 1
            test_result = {
                '接口概述': row['接口概述'],
                '接口url': url,
                '部位': params,
                '接口返回结果': response.text,
                '接口成功与否': False
            }

        # 将请求结果写入Excel对应行的预期结果下面同一列
        # sheet.cell(row=index + 2, column=5).value = response.text

    # 保存修改后的Excel文件
    #     workbook.save('test_data.xlsx')



run_tests()